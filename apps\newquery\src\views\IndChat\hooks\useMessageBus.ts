import mitt from "mitt";

// 定义消息类型
export interface PreviewMessage {
  uid: string;
  area: "right-bottom" | "mid-def-top" | "mid-def-bottom" | "mid-txt-bottom";
  data: any;
}

// 消息类型枚举
export const MessageType = {
  // 触发组件预览渲染
  TRIGGER_PREVIEW: "trigger_preview",
  // 触发工作台组件渲染
  TRIGGER_WORKBENCH: "trigger_workbench",
  // 其他消息类型...
} as const;

// 定义所有可能的消息类型
type Events = {
  [MessageType.TRIGGER_PREVIEW]: PreviewMessage;
  [MessageType.TRIGGER_WORKBENCH]: {
    uid: string;
    data: any;
  };
};

// 创建消息总线实例
const emitter = mitt<Events>();

export function useMessageBus() {
  // 发送预览渲染消息
  const triggerPreview = (message: PreviewMessage) => {
    emitter.emit(MessageType.TRIGGER_PREVIEW, message);
  };

  // 发送工作台渲染消息
  const triggerWorkbench = (uid: string, data: any) => {
    emitter.emit(MessageType.TRIGGER_WORKBENCH, { uid, data });
  };

  // 监听消息
  const onMessage = <T extends keyof Events>(
    type: T,
    callback: (data: Events[T]) => void
  ) => {
    emitter.on(type, callback);
  };

  // 移除监听
  const offMessage = <T extends keyof Events>(
    type: T,
    callback: (data: Events[T]) => void
  ) => {
    emitter.off(type, callback);
  };

  return {
    triggerPreview,
    triggerWorkbench,
    onMessage,
    offMessage,
  };
}
