// useThinking.ts - 处理AI思考过程的逻辑
import { ref, computed, Ref, ComputedRef } from "vue";

// 思考标签定义
interface ThinkingTags {
  startTags: string[];
  endTags: string[];
}

// 文本处理结果接口
interface ProcessTextResult {
  normalText: string;
  thinkingText: string;
  thinkingComplete: boolean;
}

// 消息思考状态
interface MessageThinkingState {
  isThinking: boolean;
  thinkingContent: string;
  isCompleted: boolean;
}

// 钩子函数返回类型
interface ThinkingHook {
  processText: (text: string, messageId: string) => ProcessTextResult;
  getThinkingContent: (messageId: string) => string;
  hasThinkingContent: (messageId: string) => boolean;
  isThinkingCompleted: (messageId: string) => boolean;
  resetThinking: (messageId?: string) => void;
}

/**
 * 用于处理AI思考过程的钩子函数
 * 负责检测思考标签、提取思考内容和管理思考状态
 */
export function useThinking(): ThinkingHook {
  // 保存每个消息的思考状态
  const messageThinkingMap = ref<Map<string, MessageThinkingState>>(new Map());

  // 思考标签定义
  const thinkingTags: ThinkingTags = {
    startTags: ["<think>", "<reasoning_content>", "<reasoning>"],
    endTags: ["</think>", "</reasoning_content>", "</reasoning>"],
  };

  // 获取或创建消息思考状态
  const getMessageThinkingState = (messageId: string): MessageThinkingState => {
    if (!messageThinkingMap.value.has(messageId)) {
      messageThinkingMap.value.set(messageId, {
        isThinking: false,
        thinkingContent: "",
        isCompleted: false,
      });
    }
    return messageThinkingMap.value.get(messageId)!;
  };

  // 检查是否包含开始思考标签
  const checkStartThinkingTag = (text: string): boolean => {
    return thinkingTags.startTags.some((tag) => text.includes(tag));
  };

  // 检查是否包含结束思考标签
  const checkEndThinkingTag = (text: string): boolean => {
    return thinkingTags.endTags.some((tag) => text.includes(tag));
  };

  // 获取匹配的开始标签
  const getStartTag = (text: string): string | null => {
    for (const tag of thinkingTags.startTags) {
      if (text.includes(tag)) {
        return tag;
      }
    }
    return null;
  };

  // 获取匹配的结束标签
  const getEndTag = (text: string): string | null => {
    for (const tag of thinkingTags.endTags) {
      if (text.includes(tag)) {
        return tag;
      }
    }
    return null;
  };

  // 获取对应的结束标签
  const getCorrespondingEndTag = (startTag: string): string | null => {
    const index = thinkingTags.startTags.indexOf(startTag);
    return index !== -1 ? thinkingTags.endTags[index] : null;
  };

  // 处理文本内容，识别并提取思考内容
  const processText = (text: string, messageId: string): ProcessTextResult => {
    const messageState = getMessageThinkingState(messageId);

    let result: ProcessTextResult = {
      normalText: "",
      thinkingText: "",
      thinkingComplete: false,
    };

    // 如果当前不在思考状态，检查是否有思考开始标签
    if (!messageState.isThinking) {
      const startTag = getStartTag(text);
      if (startTag) {
        // 找到开始标签，进入思考状态
        messageState.isThinking = true;
        const parts = text.split(startTag);
        result.normalText = parts[0];

        // 检查同一文本中是否有结束标签
        const remainingText = parts[1] || "";
        const endTag = getCorrespondingEndTag(startTag);

        if (endTag && remainingText.includes(endTag)) {
          // 如果同一文本中有结束标签，提取思考内容并结束思考状态
          const thinkingParts = remainingText.split(endTag);
          result.thinkingText = thinkingParts[0];
          result.normalText += thinkingParts[1] || "";
          result.thinkingComplete = true;
          messageState.isThinking = false;
          messageState.isCompleted = true;
        } else {
          // 如果没有结束标签，所有剩余内容都是思考内容
          result.thinkingText = remainingText;
        }
      } else {
        // 没有开始标签，全部是普通内容
        result.normalText = text;
      }
    } else {
      // 已经在思考状态，检查是否有结束标签
      const endTags = thinkingTags.endTags;
      let endTagFound = false;
      let endTag = "";
      let endTagIndex = -1;

      for (const tag of endTags) {
        const index = text.indexOf(tag);
        if (index !== -1 && (endTagIndex === -1 || index < endTagIndex)) {
          endTagFound = true;
          endTag = tag;
          endTagIndex = index;
        }
      }

      if (endTagFound) {
        // 找到结束标签，提取思考内容并结束思考状态
        const parts = text.split(endTag);
        result.thinkingText = parts[0];
        result.normalText = parts[1] || "";
        result.thinkingComplete = true;
        messageState.isThinking = false;
        messageState.isCompleted = true;
      } else {
        // 没有结束标签，所有内容都是思考内容
        result.thinkingText = text;
      }
    }

    // 更新消息的思考内容
    messageState.thinkingContent += result.thinkingText;

    return result;
  };

  // 获取消息的思考内容
  const getThinkingContent = (messageId: string): string => {
    // 获取原始思考内容，并将换行符替换为<br/>标签
    const rawContent = getMessageThinkingState(messageId).thinkingContent;
    return rawContent.replace(/\n/g, "<br />");
  };

  // 检查消息是否有思考内容
  const hasThinkingContent = (messageId: string): boolean => {
    return getMessageThinkingState(messageId).thinkingContent.trim().length > 0;
  };

  // 判断思考是否已完成
  const isThinkingCompleted = (messageId: string): boolean => {
    return getMessageThinkingState(messageId).isCompleted;
  };

  // 重置思考状态
  const resetThinking = (messageId?: string): void => {
    if (messageId) {
      // 重置特定消息的思考状态
      messageThinkingMap.value.delete(messageId);
    } else {
      // 重置所有消息的思考状态
      messageThinkingMap.value.clear();
    }
  };

  return {
    processText,
    getThinkingContent,
    hasThinkingContent,
    isThinkingCompleted,
    resetThinking,
  };
}
