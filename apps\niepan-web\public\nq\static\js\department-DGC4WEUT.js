import{d as L,A as ae,o as E,m as i,c as O,b as r,n as te,z as ne,r as c,Q as S,E as m,s as j,e as x,f as n,ah as re,p as h,aa as _,a1 as le,aV as oe,aW as se,aX as K,ao as ie,aY as me,aZ as de,x as ce,y as ue}from"./pnpm-pnpm-B4aX-tnA.js";import{_ as pe,w as fe,d as ve,aE as he,aF as Z,aG as _e,aH as be,a as ge}from"./index-oCQpNzhc.js";import{_ as Ce}from"./MoreIcon.vue_vue_type_script_setup_true_lang-DK105Kpz.js";import{_ as we}from"./EditIcon.vue_vue_type_script_setup_true_lang-CEndjpto.js";import{_ as xe}from"./DeleteIcon.vue_vue_type_script_setup_true_lang-TAzPfaAy.js";import{v as Ve}from"./v3Confirms-DIlUsu5z.js";const Te={xmlns:"http://www.w3.org/2000/svg",width:"16",height:"17",viewBox:"0 0 16 17",fill:"none"},Ie=["fill"],De={name:"AddSubDeptIcon"},ye=L({...De,props:{color:{default:"#697077"},size:{default:16},className:{default:""}},setup(V){return(g,d)=>(E(),ae(pe,{color:g.color,size:g.size,class:te(g.className)},{default:i(()=>[(E(),O("svg",Te,[r("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M5.33317 2.50016C5.33317 2.1335 5.63317 1.8335 5.99984 1.8335H10.6665C11.0332 1.8335 11.3332 2.1335 11.3332 2.50016V5.50016C11.3332 5.86683 11.0332 6.16683 10.6665 6.16683H8.99984V7.8335H11.9998C12.3665 7.8335 12.6665 8.1335 12.6665 8.50016V11.8335H13.9998C14.3665 11.8335 14.6665 12.1335 14.6665 12.5002C14.6665 12.8668 14.3665 13.1668 13.9998 13.1668H12.6665V14.5002C12.6665 14.8668 12.3665 15.1668 11.9998 15.1668C11.6332 15.1668 11.3332 14.8668 11.3332 14.5002V13.1668H9.99984C9.63317 13.1668 9.33317 12.8668 9.33317 12.5002C9.33317 12.1335 9.63317 11.8335 9.99984 11.8335H11.3332V9.16683H5.33317V10.8335H6.99984C7.3665 10.8335 7.6665 11.1335 7.6665 11.5002V14.5002C7.6665 14.8668 7.3665 15.1668 6.99984 15.1668H2.33317C1.9665 15.1668 1.6665 14.8668 1.6665 14.5002V11.5002C1.6665 11.1335 1.9665 10.8335 2.33317 10.8335H3.99984V8.50016C3.99984 8.1335 4.29984 7.8335 4.6665 7.8335H7.6665V6.16683H5.99984C5.63317 6.16683 5.33317 5.86683 5.33317 5.50016V2.50016ZM4.6665 12.1668H2.99984V13.8335H6.33317V12.1668H4.6665ZM6.6665 3.16683V4.8335H9.99984V3.16683H6.6665Z",fill:g.color},null,8,Ie)]))]),_:1},8,["color","size","class"]))}}),p=V=>(ce("data-v-57dbe31e"),V=V(),ue(),V),ke={class:"rounded-[20px]"},He={class:"flex justify-between mb-4"},Se={class:"search-box flex items-center"},Ue=p(()=>r("div",{class:"whitespace-nowrap text-[14px] leading-[18px] font-medium mr-[10px]"}," 部门名称 ",-1)),Ne={class:"rounded-xl h-[calc(100vh-132px)] bg-white overflow-auto"},$e={class:"flex items-center"},Ae=["onClick"],Ee={class:"cursor-pointer p-2 hover:bg-[#e6e7e7] rounded-md w-8 h-8"},ze={class:"flex items-center text-[#343A3F]"},Me=p(()=>r("span",null,"编辑",-1)),Fe={class:"flex items-center text-[#CD1525]"},Be=p(()=>r("span",null,"删除",-1)),je={class:"mb-4"},Ke={class:"mb-2 flex items-center"},Ze=p(()=>r("span",{class:"text-[14px] font-medium"},"上级部门",-1)),Le={class:"ml-1 text-gray-400 cursor-help"},Oe={class:"mb-4"},qe=p(()=>r("div",{class:"mb-2 flex items-center"},[r("span",{class:"text-[14px] font-medium"},"部门名称"),r("span",{class:"text-red-500 ml-1"},"*")],-1)),Je={class:"flex justify-end"},Ge={class:"mb-4"},Pe=p(()=>r("div",{class:"mb-2 flex items-center"},[r("span",{class:"text-[14px] font-medium"},"上级部门")],-1)),Qe={class:"mb-4"},We=p(()=>r("div",{class:"mb-2 flex items-center"},[r("span",{class:"text-[14px] font-medium"},"部门名称"),r("span",{class:"text-red-500 ml-1"},"*")],-1)),Xe={class:"flex justify-end"},Ye={class:"mb-4"},Re=p(()=>r("div",{class:"mb-2 flex items-center"},[r("span",{class:"text-[14px] font-medium"},"上级部门")],-1)),ea={class:"mb-4"},aa=p(()=>r("div",{class:"mb-2 flex items-center"},[r("span",{class:"text-[14px] font-medium"},"部门名称"),r("span",{class:"text-red-500 ml-1"},"*")],-1)),ta={class:"flex justify-end"},na=L({__name:"department",setup(V){const g=fe(),d=ve();ne(async()=>{g.setActiveMenu("department"),await T()});const N=c(""),T=async()=>{var e,a;const t=S.service({lock:!0,text:"加载部门数据中...",background:"rgba(255, 255, 255, 0.7)"});try{d.userInfo||await d.fetchUserInfo(null);const l=(a=(e=d.userInfo)==null?void 0:e.tenant)==null?void 0:a.id;if(!l){m.error("获取租户信息失败");return}const o=await he(l);o&&o.data?I.value=z(o.data):m.warning("未获取到部门数据")}catch(l){console.error("获取部门数据失败:",l),m.error("获取部门数据失败")}finally{t.close()}},z=t=>!t||!Array.isArray(t)?[]:t.map(e=>({id:e.id,name:e.name,createTime:e.create_time||new Date().toISOString().split("T")[0],memberCount:e.member_count||0,manager:e.members&&e.members.length>0?e.members[0].name:"",uuid:e.uuid,sequence:e.sequence,update_time:e.update_time,children:e.children&&e.children.length>0?z(e.children):[]})),I=c([]);c([{id:1,name:"总公司",manager:"张三",memberCount:120,createTime:"2024-01-01",children:[{id:2,name:"研发部",manager:"李四",memberCount:45,createTime:"2024-01-01",children:[{id:5,name:"前端组",manager:"王五",memberCount:15,createTime:"2024-01-01"},{id:6,name:"后端组",manager:"赵六",memberCount:20,createTime:"2024-01-01"},{id:7,name:"测试组",manager:"钱七",memberCount:10,createTime:"2024-01-01"}]},{id:3,name:"市场部",manager:"孙八",memberCount:30,createTime:"2024-01-01",children:[{id:8,name:"销售组",manager:"周九",memberCount:20,createTime:"2024-01-01"},{id:9,name:"市场推广组",manager:"吴十",memberCount:10,createTime:"2025-01-01"}]},{id:4,name:"行政部",manager:"郑十一",memberCount:15,createTime:"2025-01-01",children:[{id:10,name:"人力资源组",manager:"王十二",memberCount:8,createTime:"2025-01-02"},{id:11,name:"行政管理组",manager:"李十三",memberCount:7,createTime:"2025-01-03"}]},{id:12,name:"财务部",manager:"张十四",memberCount:20,createTime:"2025-01-04",children:[{id:13,name:"会计组",manager:"王十五",memberCount:10,createTime:"2025-01-05"},{id:14,name:"财务分析组",manager:"赵十六",memberCount:10,createTime:"2025-01-06"}]},{id:15,name:"产品部",manager:"刘十七",memberCount:25,createTime:"2025-01-07",children:[{id:16,name:"产品经理组",manager:"张十八",memberCount:10,createTime:"2025-01-08"},{id:17,name:"UI设计组",manager:"李十九",memberCount:8,createTime:"2025-01-09"},{id:18,name:"用户体验组",manager:"王二十",memberCount:7,createTime:"2025-01-10"}]}]},{id:100,name:"北京分公司",manager:"刘二十一",memberCount:80,createTime:"2025-02-01",children:[{id:101,name:"北京研发部",manager:"王二十二",memberCount:30,createTime:"2025-02-02",children:[{id:102,name:"北京前端组",manager:"李二十三",memberCount:10,createTime:"2025-02-03"},{id:103,name:"北京后端组",manager:"赵二十四",memberCount:12,createTime:"2025-02-04"},{id:104,name:"北京测试组",manager:"孙二十五",memberCount:8,createTime:"2025-02-05"}]},{id:105,name:"北京市场部",manager:"周二十六",memberCount:25,createTime:"2025-02-06",children:[{id:106,name:"北京销售组",manager:"吴二十七",memberCount:15,createTime:"2025-02-07"},{id:107,name:"北京市场推广组",manager:"郑二十八",memberCount:10,createTime:"2025-02-08"}]},{id:108,name:"北京行政部",manager:"王二十九",memberCount:10,createTime:"2025-02-09"},{id:109,name:"北京财务部",manager:"李三十",memberCount:15,createTime:"2025-02-10"}]},{id:200,name:"上海分公司",manager:"张三十一",memberCount:75,createTime:"2025-03-01",children:[{id:201,name:"上海研发部",manager:"王三十二",memberCount:30,createTime:"2025-03-02",children:[{id:202,name:"上海前端组",manager:"李三十三",memberCount:10,createTime:"2025-03-03"},{id:203,name:"上海后端组",manager:"赵三十四",memberCount:12,createTime:"2025-03-04"},{id:204,name:"上海测试组",manager:"孙三十五",memberCount:8,createTime:"2025-03-05"}]},{id:205,name:"上海市场部",manager:"周三十六",memberCount:25,createTime:"2025-03-06",children:[{id:206,name:"上海销售组",manager:"吴三十七",memberCount:15,createTime:"2025-03-07"},{id:207,name:"上海市场推广组",manager:"郑三十八",memberCount:10,createTime:"2025-03-08"}]},{id:208,name:"上海行政部",manager:"王三十九",memberCount:10,createTime:"2025-03-09"},{id:209,name:"上海财务部",manager:"李四十",memberCount:10,createTime:"2025-03-10"}]}]);const D=c(!1),y=c(!1),k=c(!1),f=c({parentId:"",name:""}),u=c({id:"",parentId:"",parentName:"",name:""}),v=c({parentId:"",parentName:"",name:""}),q=j(()=>{const t=e=>e.map(a=>{const l={value:a.id,label:a.name};return a.children&&a.children.length>0&&(l.children=t(a.children)),l});return t(I.value)}),J=()=>{f.value={parentId:"",name:""},D.value=!0},G=async()=>{var t,e;if(!f.value.name.trim()){m.warning("请输入部门名称");return}try{const a=S.service({lock:!0,text:"添加部门中...",background:"rgba(255, 255, 255, 0.7)"});d.userInfo||await d.fetchUserInfo(null);const l=(e=(t=d.userInfo)==null?void 0:t.tenant)==null?void 0:e.id;if(!l){m.error("获取租户信息失败"),a.close();return}const o=await Z(l,f.value.parentId||null,f.value.name);o&&o.data&&o.data.id?(await T(),m.success("添加部门成功"),D.value=!1):m.warning("添加部门失败，请重试"),a.close()}catch(a){console.error("添加部门失败:",a),m.error("添加部门失败: "+(a.message||"未知错误"))}},P=t=>{v.value={parentId:t.id,parentName:t.name,name:""},k.value=!0},Q=async()=>{var t,e;if(!v.value.name.trim()){m.warning("请输入部门名称");return}try{const a=S.service({lock:!0,text:"添加子部门中...",background:"rgba(255, 255, 255, 0.7)"});d.userInfo||await d.fetchUserInfo(null);const l=(e=(t=d.userInfo)==null?void 0:t.tenant)==null?void 0:e.id;if(!l){m.error("获取租户信息失败"),a.close();return}const o=await Z(l,v.value.parentId,v.value.name);o&&o.data&&o.data.id?(await T(),m.success("添加子部门成功"),k.value=!1):m.warning("添加子部门失败，请重试"),a.close()}catch(a){console.error("添加子部门失败:",a),m.error("添加子部门失败: "+(a.message||"未知错误"))}},W=t=>{const e=(l,o,C=null)=>{for(const b of l){if(b.id===o)return C;if(b.children&&b.children.length>0){const w=e(b.children,o,b);if(w)return w}}return null},a=e(I.value,t.id);u.value={id:t.id,parentId:a?a.id:"",parentName:a?a.name:"无（一级部门）",name:t.name},y.value=!0},X=async()=>{var t,e;if(!u.value.name.trim()){m.warning("请输入部门名称");return}try{const a=S.service({lock:!0,text:"编辑部门中...",background:"rgba(255, 255, 255, 0.7)"});d.userInfo||await d.fetchUserInfo(null);const l=(e=(t=d.userInfo)==null?void 0:t.tenant)==null?void 0:e.id;if(!l){m.error("获取租户信息失败"),a.close();return}const o=await _e(l,u.value.id,u.value.parentId,u.value.name);o&&o.data&&o.data.id?(await T(),m.success("编辑部门成功"),y.value=!1):m.warning("编辑部门失败，请重试"),a.close()}catch(a){console.error("编辑部门失败:",a),m.error("编辑部门失败: "+(a.message||"未知错误"))}},U=c(!1),H=c(null),Y=t=>{if(t.children&&t.children.length>0){m.warning("该部门下有子部门，无法直接删除");return}H.value=t,U.value=!0},R=async()=>{var t,e;if(H.value)try{const a=S.service({lock:!0,text:"删除部门中...",background:"rgba(255, 255, 255, 0.7)"});d.userInfo||await d.fetchUserInfo(null);const l=(e=(t=d.userInfo)==null?void 0:t.tenant)==null?void 0:e.id;if(!l){m.error("获取租户信息失败"),a.close();return}const o=await be(l,H.value.id);o&&o.data&&o.data.error==="0"?(await T(),m.success(`删除部门: ${H.value.name} 成功`),U.value=!1,H.value=null):m.warning("删除部门失败，请重试"),a.close()}catch(a){console.error("删除部门失败:",a),m.error("删除部门失败: "+(a.message||"未知错误"))}},$=()=>{A.value=N.value.trim()},M=()=>{$()},A=c(""),F=(t,e)=>e?t.filter(a=>{const l=a.name.toLowerCase().includes(e.toLowerCase());let o=[];return a.children&&a.children.length>0&&(o=F(a.children,e)),o.length>0?(a.children=o,!0):l}):t,ee=j(()=>{if(!A.value)return I.value;const t=JSON.parse(JSON.stringify(I.value));return F(t,A.value)});return(t,e)=>{const a=x("el-input"),l=x("el-button"),o=x("el-icon"),C=x("el-table-column"),b=x("el-table"),w=x("el-dialog");return E(),O("div",ke,[r("div",He,[r("div",Se,[Ue,n(a,{modelValue:N.value,"onUpdate:modelValue":e[0]||(e[0]=s=>N.value=s),placeholder:"请输入部门名称",class:"mr-[10px] w-[240px] role-input",clearable:"",onInput:M,onClear:M,onKeyup:re($,["enter"])},null,8,["modelValue"]),n(l,{type:"primary",class:"filter-btn rounded-md h-8 min-w-[60px] px-4 text-sm",onClick:$},{default:i(()=>[h("筛选")]),_:1})]),n(l,{type:"primary",class:"add-dept-btn !rounded-md",onClick:J},{default:i(()=>[n(o,{class:"mr-1"},{default:i(()=>[n(_(le))]),_:1}),h("添加部门 ")]),_:1})]),r("div",Ne,[n(b,{data:ee.value,style:{width:"100%"},"row-key":"id","default-expand-all":"","tree-props":{children:"children",hasChildren:"hasChildren"}},{default:i(()=>[n(C,{prop:"name",label:"部门名称",width:"*"}),n(C,{prop:"memberCount",label:"部门人数",width:"120"}),n(C,{prop:"createTime",label:"创建时间",width:"180"}),n(C,{label:"操作",width:"120",fixed:"right"},{default:i(s=>[r("div",$e,[r("div",{class:"cursor-pointer p-2 hover:bg-[#e6e7e7] rounded-md w-8 h-8",onClick:B=>P(s.row)},[n(ye,{size:"16",color:"#697077"})],8,Ae),n(_(oe),{trigger:"click"},{dropdown:i(()=>[n(_(se),null,{default:i(()=>[n(_(K),{onClick:B=>W(s.row)},{default:i(()=>[r("div",ze,[n(we,{size:"16",class:"mr-1"}),Me])]),_:2},1032,["onClick"]),n(_(K),{onClick:B=>Y(s.row)},{default:i(()=>[r("div",Fe,[n(xe,{size:"16",class:"mr-1",color:"#CD1525"}),Be])]),_:2},1032,["onClick"])]),_:2},1024)]),default:i(()=>[r("div",Ee,[n(Ce,{width:"16",height:"16"})])]),_:2},1024)])]),_:1})]),_:1},8,["data"])]),n(w,{modelValue:D.value,"onUpdate:modelValue":e[4]||(e[4]=s=>D.value=s),title:"添加部门",width:"500px","close-on-click-modal":!1,"show-close":!0,class:"!rounded-3xl !p-6"},{footer:i(()=>[r("div",Je,[n(l,{onClick:e[3]||(e[3]=s=>D.value=!1)},{default:i(()=>[h("取消")]),_:1}),n(l,{type:"primary",onClick:G},{default:i(()=>[h("确认")]),_:1})])]),default:i(()=>[r("div",je,[r("div",Ke,[Ze,n(_(ie),{content:"未选择的默认添加为一级部门",placement:"top"},{default:i(()=>[r("span",Le,[n(o,null,{default:i(()=>[n(_(me))]),_:1})])]),_:1})]),n(_(de),{modelValue:f.value.parentId,"onUpdate:modelValue":e[1]||(e[1]=s=>f.value.parentId=s),data:q.value,placeholder:"请选择",class:"w-full dept-input",clearable:"","check-strictly":""},null,8,["modelValue","data"])]),r("div",Oe,[qe,n(a,{modelValue:f.value.name,"onUpdate:modelValue":e[2]||(e[2]=s=>f.value.name=s),placeholder:"请输入部门名称",class:"w-full dept-input"},null,8,["modelValue"])])]),_:1},8,["modelValue"]),n(w,{modelValue:y.value,"onUpdate:modelValue":e[8]||(e[8]=s=>y.value=s),title:"编辑部门",width:"500px","close-on-click-modal":!1,"show-close":!0},{footer:i(()=>[r("div",Xe,[n(l,{onClick:e[7]||(e[7]=s=>y.value=!1)},{default:i(()=>[h("取消")]),_:1}),n(l,{type:"primary",onClick:X},{default:i(()=>[h("保存")]),_:1})])]),default:i(()=>[r("div",Ge,[Pe,n(a,{modelValue:u.value.parentName,"onUpdate:modelValue":e[5]||(e[5]=s=>u.value.parentName=s),disabled:"",class:"w-full dept-input"},null,8,["modelValue"])]),r("div",Qe,[We,n(a,{modelValue:u.value.name,"onUpdate:modelValue":e[6]||(e[6]=s=>u.value.name=s),placeholder:"请输入部门名称",class:"w-full dept-input"},null,8,["modelValue"])])]),_:1},8,["modelValue"]),n(w,{modelValue:k.value,"onUpdate:modelValue":e[12]||(e[12]=s=>k.value=s),title:"添加子部门",width:"500px","close-on-click-modal":!1,"show-close":!0},{footer:i(()=>[r("div",ta,[n(l,{onClick:e[11]||(e[11]=s=>k.value=!1)},{default:i(()=>[h("取消")]),_:1}),n(l,{type:"primary",onClick:Q},{default:i(()=>[h("确定")]),_:1})])]),default:i(()=>[r("div",Ye,[Re,n(a,{modelValue:v.value.parentName,"onUpdate:modelValue":e[9]||(e[9]=s=>v.value.parentName=s),disabled:"",class:"w-full dept-input"},null,8,["modelValue"])]),r("div",ea,[aa,n(a,{modelValue:v.value.name,"onUpdate:modelValue":e[10]||(e[10]=s=>v.value.name=s),placeholder:"请输入部门名称",class:"w-full dept-input"},null,8,["modelValue"])])]),_:1},8,["modelValue"]),n(Ve,{show:U.value,title:"确定删除部门吗？",message:"部门删除后不可恢复，是否继续？",confirmText:"删除",onClose:e[13]||(e[13]=s=>U.value=!1),onConfirm:R},null,8,["show"])])}}}),da=ge(na,[["__scopeId","data-v-57dbe31e"]]);export{da as default};
