<template>
  <div v-if="content" class="rounded-md mt-[20px] border border-gray-200">
    <div class="flex items-center mb-2 ml-3">
      <div class="mr-1 text-blue-500">
        <el-icon size="17">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M3 7.875C3 4.97549 5.35049 2.625 8.25 2.625C11.1495 2.625 13.5 4.97549 13.5 7.875C13.5 8.00246 13.5325 8.12781 13.5944 8.23923L14.9704 10.7161L14.1029 10.8896C13.7523 10.9597 13.5 11.2675 13.5 11.625V13.125H13.1871L10.9983 12.7602C10.7991 12.727 10.595 12.7755 10.432 12.8948C10.2691 13.0141 10.1611 13.194 10.1325 13.3939L9.84953 15.375H5.4943L4.86146 12.0902C4.83179 11.9362 4.75459 11.7954 4.64072 11.6876C3.62933 10.7297 3 9.37647 3 7.875ZM8.25 1.125C4.52206 1.125 1.5 4.14706 1.5 7.875C1.5 9.71622 2.23809 11.3862 3.4327 12.6032L4.13854 16.2669C4.20655 16.6199 4.5155 16.875 4.875 16.875H10.5C10.8732 16.875 11.1897 16.6006 11.2425 16.2311L11.5089 14.366L13.0017 14.6148C13.0424 14.6216 13.0837 14.625 13.125 14.625H14.25C14.6642 14.625 15 14.2892 15 13.875V12.2399L16.2721 11.9854C16.5069 11.9385 16.7052 11.7822 16.8057 11.5648C16.9063 11.3474 16.8969 11.0951 16.7806 10.8858L14.9971 7.67545C14.8916 4.03981 11.9112 1.125 8.25 1.125ZM8.25 6C7.62869 6 7.125 6.50369 7.125 7.125C7.125 7.53921 6.78921 7.875 6.375 7.875C5.96079 7.875 5.625 7.53921 5.625 7.125C5.625 5.67526 6.80026 4.5 8.25 4.5C9.69974 4.5 10.875 5.67526 10.875 7.125C10.875 8.31412 10.0843 9.31859 9 9.64129V10.125C9 10.5392 8.66421 10.875 8.25 10.875C7.83579 10.875 7.5 10.5392 7.5 10.125V9C7.5 8.58579 7.83579 8.25 8.25 8.25C8.87131 8.25 9.375 7.74631 9.375 7.125C9.375 6.50369 8.87131 6 8.25 6ZM8.25 11.625C8.66421 11.625 9 11.9608 9 12.375V12.75C9 13.1642 8.66421 13.5 8.25 13.5C7.83579 13.5 7.5 13.1642 7.5 12.75V12.375C7.5 11.9608 7.83579 11.625 8.25 11.625Z"
              fill="#343A3F"
            />
          </svg>
        </el-icon>
      </div>
      <div
        class="text-[#343A3F] font-medium text-[14px] leading-[22px] font-pingfang relative top-[-1px]"
      >
        {{ completed ? "已深度思考" : "思考中..." }}
      </div>
    </div>
    <div
      class="pl-3 border-0 border-solid border-l-2 border-[#E5E5E5] text-[#697077] text-[14px] font-normal leading-[22px] font-pingfang"
      v-html="renderedContent"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { computed, PropType } from "vue";
import { marked } from "marked";

const props = defineProps({
  content: {
    type: String as PropType<string>,
    default: "",
  },
  completed: {
    type: Boolean as PropType<boolean>,
    default: false,
  },
});

// 渲染Markdown内容
const renderedContent = computed(() => {
  if (!props.content) return "";
  return marked(props.content);
});
</script>

<style>
/* 为 Markdown 内容添加的样式 */
.font-pingfang {
  font-family: "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* Markdown 内容样式 */
.pl-4.border-l-2 pre {
  @apply bg-gray-100 p-2 rounded-md overflow-x-auto;
}

.pl-4.border-l-2 code {
  @apply font-mono text-xs;
}
</style>
