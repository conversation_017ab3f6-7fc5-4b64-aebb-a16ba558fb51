const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/index-DqLOvDaQ.js","static/js/pnpm-pnpm-B4aX-tnA.js","static/css/pnpm-pnpm-BdGb95pV.css","static/js/SearchIcon.vue_vue_type_script_setup_true_lang-4BGMlqW3.js","static/js/apiUrl-l4DNRpzY.js","static/css/apiUrl-ytsw70E2.css","static/css/index-Bz4heLgD.css","static/js/ChatView-BP1WmwpV.js","static/css/ChatView-BM2rf-d_.css","static/js/index-xhSOFnTC.js","static/css/index-z89QIozq.css","static/js/FilePreviewPage-cEv_AJah.js","static/css/FilePreviewPage-DPm_INuQ.css","static/js/index-dqEKp0WT.js","static/js/DeleteIcon.vue_vue_type_script_setup_true_lang-TAzPfaAy.js","static/css/index-BLGGHHr0.css","static/js/index-BWudzzWS.js","static/js/quesheng-C5oWzANf.js","static/css/index-MoMk4NZj.css","static/js/index-BR7-Ob6r.js","static/css/index-WzLFu10n.css","static/js/Docfragments-CKYN7E1b.js","static/js/BackArrowIcon.vue_vue_type_script_setup_true_lang-BntbR4_Q.js","static/js/UploadThinIcon.vue_vue_type_script_setup_true_lang-D5ZNlFf9.js","static/css/Docfragments-BOd5ih1H.css","static/js/Detail-DkFugHS6.js","static/js/Docs-CaCa2q0a.js","static/css/Docs-BHHjrjHx.css","static/js/Test-MMU_raRr.js","static/css/Test-CU1yxqLo.css","static/js/Settings-C2MV-lE_.js","static/css/Settings-cyxZnP2J.css","static/js/index-CqJoS3As.js","static/css/index-B1FICvWr.css","static/js/Detail-xIdcdvcs.js","static/js/Settings-Comqx2Ls.js","static/js/EditIcon.vue_vue_type_script_setup_true_lang-CEndjpto.js","static/css/Settings-CiLfRkpy.css","static/js/department-DGC4WEUT.js","static/js/MoreIcon.vue_vue_type_script_setup_true_lang-DK105Kpz.js","static/js/v3Confirms-DIlUsu5z.js","static/css/v3Confirms-qPu1A5-y.css","static/css/department-5khHmqEF.css","static/js/user-CYURvluk.js","static/css/user-B41hN6Sf.css","static/js/role-iO_qqXTm.js","static/css/role-BK7PYLqL.css","static/js/model-provider-CizF2tHO.js","static/js/data-source-B3SkTLgL.js","static/js/api-extension-BnxwXGaq.js","static/js/license-YzC5_D8d.js","static/css/license-B_SY1GJM.css","static/js/system-config-BWomNM7h.js","static/css/system-config-Cal1MQNp.css","static/js/privacy-Dz6C79G9.js","static/css/privacy-C4AGU5yE.css","static/js/favorite-_sJSL1Hs.js","static/css/favorite-hvdH83KP.css"])))=>i.map(i=>d[i]);
var sa=Object.defineProperty;var na=(s,o,l)=>o in s?sa(s,o,{enumerable:!0,configurable:!0,writable:!0,value:l}):s[o]=l;var fs=(s,o,l)=>na(s,typeof o!="symbol"?o+"":o,l);import{r as n,d as ye,c,a as z,o as a,b as e,e as _,f as i,g as aa,h as la,i as mo,l as Cs,j as vo,w as _e,k as me,v as Ae,t as S,n as W,m as p,p as Q,u as Ce,q as De,s as et,x as be,y as we,E,V as ia,z as ge,A as X,B as Xe,C as ca,D as It,F as ra,G as da,H as ua,I as ma,J as N,T as dn,K as le,L as bt,M as un,N as Pe,O as Ke,P as ks,Q as Re,R as pt,S as $e,U as mn,W as Lt,X as vt,Y as tt,Z as pa,_ as va,$ as pn,a0 as vn,a1 as hn,a2 as Ys,a3 as js,a4 as Js,a5 as mt,a6 as ha,a7 as We,a8 as fa,a9 as As,aa as oe,ab as _a,ac as ga,ad as No,ae as fn,af as Aa,ag as xs,ah as ya,ai as Gs,aj as Yt,ak as ba,al as Ss,am as wa,an as Ca,ao as Uo,ap as at,aq as _n,ar as Ro,as as ka,at as xa,au as Sa,av as gn,aw as Ia,ax as Ba,ay as Ta,az as Ma,aA as Da,aB as Va}from"./pnpm-pnpm-B4aX-tnA.js";(function(){const o=document.createElement("link").relList;if(o&&o.supports&&o.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))t(d);new MutationObserver(d=>{for(const m of d)if(m.type==="childList")for(const u of m.addedNodes)u.tagName==="LINK"&&u.rel==="modulepreload"&&t(u)}).observe(document,{childList:!0,subtree:!0});function l(d){const m={};return d.integrity&&(m.integrity=d.integrity),d.referrerPolicy&&(m.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?m.credentials="include":d.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function t(d){if(d.ep)return;d.ep=!0;const m=l(d);fetch(d.href,m)}})();const _s=n(!1);function qo(){return{visible:_s,showTip:()=>{_s.value=!0},hideTip:()=>{_s.value=!1}}}const Ea=ye({name:"DevelopingModal",setup(){const{visible:s,hideTip:o}=qo();return{visible:s,hideTip:o}}}),ue=(s,o)=>{const l=s.__vccOpts||s;for(const[t,d]of o)l[t]=d;return l},La={key:0,class:"fixed top-0 left-0 w-full h-full z-[120] bg-white/30 backdrop-blur-[14px]"},za={class:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-[30px] w-[420px] p-6",style:{"box-shadow":"0px 10px 44px 1px rgba(0, 0, 0, 0.1)"}},Fa=e("div",{class:"text-xl mb-[24px]"},"功能正在开发中，敬请期待",-1);function Ua(s,o,l,t,d,m){return s.visible?(a(),c("div",La,[e("div",za,[Fa,e("button",{class:"text-base rounded-[10px] w-[80px] h-[40px] border border-solid border-[#E5E5E5] bg-transparent float-right",onClick:o[0]||(o[0]=(...u)=>s.hideTip&&s.hideTip(...u))}," 知道了 ")])])):z("",!0)}const Pa=ue(Ea,[["render",Ua]]),Ra=ye({__name:"App",setup(s){return(o,l)=>{const t=_("router-view");return a(),c("div",null,[i(t),i(Pa)])}}}),Na="modulepreload",qa=function(s){return"/nq/"+s},Ws={},Ie=function(o,l,t){let d=Promise.resolve();if(l&&l.length>0){document.getElementsByTagName("link");const m=document.querySelector("meta[property=csp-nonce]"),u=(m==null?void 0:m.nonce)||(m==null?void 0:m.getAttribute("nonce"));d=Promise.all(l.map(r=>{if(r=qa(r),r in Ws)return;Ws[r]=!0;const v=r.endsWith(".css"),k=v?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${r}"]${k}`))return;const h=document.createElement("link");if(h.rel=v?"stylesheet":Na,v||(h.as="script"),h.crossOrigin="",h.href=r,u&&h.setAttribute("nonce",u),document.head.appendChild(h),v)return new Promise((g,x)=>{h.addEventListener("load",g),h.addEventListener("error",()=>x(new Error(`Unable to preload CSS for ${r}`)))})}))}return d.then(()=>o()).catch(m=>{const u=new Event("vite:preloadError",{cancelable:!0});if(u.payload=m,window.dispatchEvent(u),!u.defaultPrevented)throw m})},Ka="locale",Ha=[{value:"en-US",name:"English(United States)",example:"Hello, Intellido!",supported:!0},{value:"zh-Hans",name:"简体中文",example:"你好，Intellido！",supported:!0},{value:"zh-Hant",name:"繁體中文",example:"你好，Intellido！",supported:!0},{value:"pt-BR",name:"Português(Brasil)",example:"Olá, Intellido!",supported:!1},{value:"es-ES",name:"Español(España)",example:"Saluton, Intellido!",supported:!1},{value:"fr-FR",name:"Français(France)",example:"Bonjour, Intellido!",supported:!1},{value:"de-DE",name:"Deutsch(Deutschland)",example:"Hallo, Intellido!",supported:!1},{value:"ja-JP",name:"日本語(日本)",example:"こんにちは、Intellido!",supported:!1},{value:"ko-KR",name:"한국어(대한민국)",example:"안녕, Intellido!",supported:!1}];Ha.filter(s=>s.supported).map(s=>s.value);const Qa={common:{welcome:"Welcome to Intellido",search:"Search",cancel:"Cancel",confirm:"Confirm",save:"Save",delete:"Delete",edit:"Edit",add:"Add",loading:"Loading",noData:"No Data",back:"Back",next:"Next",submit:"Submit",reset:"Reset",close:"Close",open:"Open",yes:"Yes",no:"No",success:"Success",error:"Error",warning:"Warning",info:"Info",home:"Home",settings:"Settings",logout:"Logout",login:"Login",register:"Register",username:"Username",password:"Password",email:"Email",phone:"Phone",address:"Address",description:"Description",createTime:"Create Time",updateTime:"Update Time",operation:"Operation",pleaseInput:"Please input",pleaseSelect:"Please select",all:"All",more:"More",learn:"Learn",download:"Download",upload:"Upload",preview:"Preview",copy:"Copy",paste:"Paste",cut:"Cut",refresh:"Refresh",fullScreen:"Full Screen",exitFullScreen:"Exit Full Screen",language:"Language",theme:"Theme",dark:"Dark",light:"Light",auto:"Auto"},layout:{header:{search:"Search",profile:"Profile",settings:"Settings",logout:"Logout"},sidebar:{home:"Home",dashboard:"Dashboard",apps:"Applications",explore:"Explore",settings:"Settings",help:"Help",logo:"Logo",historyChat:"History Chat",favorites:"Favorites",workspace:"Workspace",systemManagement:"System Management"},logo:"Logo",workspace:"Workspace",goToWorkspace:"Go to Workspace",username:"Username",accountInfo:"Account",languageAndTimezone:"Language & Timezone",systemManagement:"System Management",help:"Docs",logout:"Log out",about:"About"},apps:{title:"Applications",search:"Search applications",filter:"Filter",sort:"Sort",create:"Create",import:"Import",export:"Export",delete:"Delete",edit:"Edit",view:"View",run:"Run",stop:"Stop",restart:"Restart",log:"Log",config:"Config",status:"Status",type:"Type",tags:"Tags",noData:"No applications found",noMoreData:"No more data",loadFailed:"Failed to load data",pinSuccess:"Pinned successfully",pinFailed:"Failed to pin",pinToTop:"Pin to top",appIcon:"App icon",noDescription:"This app has no description yet",pageTitle:"Knowledge Plaza",pageSubtitle:"Gather enterprise AI wisdom, explore unlimited future possibilities, and start a new era of collaboration with knowledge engine!",tags:{all:"All tags",noTag:"No tags",loadFailed:"Failed to load tags"},confirmDelete:"This application will be permanently deleted and cannot be recovered. Are you sure to delete?",emptyState:{title:"No applications here",description:"Go to the app factory to create AI applications",createButton:"Create Application"},types:{all:"All",chat:"Chatbot",agentchat:"Agent",workflow:"Workflow",completion:"Completion",advancedchat:"ChatFlow"}}},Oa={common:{welcome:"欢迎使用Intellido",search:"搜索",cancel:"取消",confirm:"确认",save:"保存",delete:"删除",edit:"编辑",add:"添加",loading:"加载中",noData:"暂无数据",back:"返回",next:"下一步",submit:"提交",reset:"重置",close:"关闭",open:"打开",yes:"是",no:"否",success:"成功",error:"错误",warning:"警告",info:"信息",home:"首页",settings:"设置",logout:"退出登录",login:"登录",register:"注册",username:"用户名",password:"密码",email:"邮箱",phone:"电话",address:"地址",description:"描述",createTime:"创建时间",updateTime:"更新时间",operation:"操作",pleaseInput:"请输入",pleaseSelect:"请选择",all:"全部",more:"更多",learn:"了解",download:"下载",upload:"上传",preview:"预览",copy:"复制",paste:"粘贴",cut:"剪切",refresh:"刷新",fullScreen:"全屏",exitFullScreen:"退出全屏",language:"语言",theme:"主题",dark:"深色",light:"浅色",auto:"自动"},layout:{header:{search:"搜索",profile:"个人信息",settings:"设置",logout:"退出登录"},sidebar:{home:"首页",dashboard:"仪表盘",apps:"应用",explore:"探索",settings:"设置",help:"帮助",logo:"Logo",historyChat:"历史对话",favorites:"收藏夹",workspace:"工作空间",systemManagement:"系统管理"},logo:"Logo",workspace:"工作空间",goToWorkspace:"去工作空间",username:"用户名",accountInfo:"账户信息",languageAndTimezone:"语言和时区",systemManagement:"系统管理",help:"帮助",logout:"退出登录",about:"关于"},apps:{title:"应用",search:"搜索应用",filter:"筛选",sort:"排序",create:"创建",import:"导入",export:"导出",delete:"删除",edit:"编辑",view:"查看",run:"运行",stop:"停止",restart:"重启",log:"日志",config:"配置",status:"状态",type:"类型",tags:"标签",noData:"未找到应用",noMoreData:"没有更多数据了",loadFailed:"加载数据失败",pinSuccess:"置顶成功",pinFailed:"置顶失败",unpinSuccess:"取消置顶成功",unpinFailed:"取消置顶失败",pinToTop:"移到最前",unpinFromTop:"取消置顶",appIcon:"应用图标",noDescription:"这个应用还没有介绍～",pageTitle:"智识广场",pageSubtitle:"汇聚企业AI智慧，探索未来无限可能，用知识引擎开启协作新纪元！",tags:{all:"全部标签",noTag:"暂无标签",loadFailed:"加载标签失败"},confirmDelete:"该应用将被永久删除，不可恢复及撤销。确定要删除吗？",emptyState:{title:"广场上空空如也",description:"快去应用工厂创建AI应用吧",createButton:"去创建应用"},types:{all:"全部",chat:"聊天助手",agentchat:"Agent",workflow:"工作流",completion:"文本生成",advancedchat:"对话流"}}},Za={common:{welcome:"歡迎使用Intellido",search:"搜尋",cancel:"取消",confirm:"確認",save:"保存",delete:"刪除",edit:"編輯",add:"添加",loading:"載入中",noData:"暫無數據",back:"返回",next:"下一步",submit:"提交",reset:"重置",close:"關閉",open:"打開",yes:"是",no:"否",success:"成功",error:"錯誤",warning:"警告",info:"信息",home:"首頁",settings:"設置",logout:"退出登錄",login:"登錄",register:"註冊",username:"用戶名",password:"密碼",email:"郵箱",phone:"電話",address:"地址",description:"描述",createTime:"創建時間",updateTime:"更新時間",operation:"操作",pleaseInput:"請輸入",pleaseSelect:"請選擇",all:"全部",more:"更多",learn:"了解",download:"下載",upload:"上傳",preview:"預覽",copy:"複製",paste:"粘貼",cut:"剪切",refresh:"刷新",fullScreen:"全屏",exitFullScreen:"退出全屏",language:"語言",theme:"主題",dark:"深色",light:"淺色",auto:"自動"},layout:{header:{search:"搜尋",profile:"個人信息",settings:"設置",logout:"退出登錄"},sidebar:{home:"首頁",dashboard:"儀表板",apps:"應用",explore:"探索",settings:"設置",help:"幫助",logo:"Logo",historyChat:"歷史對話",favorites:"收藏夾",workspace:"工作空間",systemManagement:"系統管理"},logo:"Logo",workspace:"工作空間",goToWorkspace:"去工作空間",username:"用戶名",accountInfo:"賬戶信息",languageAndTimezone:"語言和時區",systemManagement:"系統管理",help:"幫助",logout:"退出登錄",about:"關於"},apps:{title:"應用",search:"搜尋應用",filter:"篩選",sort:"排序",create:"創建",import:"導入",export:"導出",delete:"刪除",edit:"編輯",view:"查看",run:"運行",stop:"停止",restart:"重啟",log:"日誌",config:"配置",status:"狀態",type:"類型",tags:"標籤",noData:"未找到應用",noMoreData:"沒有更多數據了",loadFailed:"加載數據失敗",pinSuccess:"置頂成功",pinFailed:"置頂失敗",unpinSuccess:"取消置頂成功",unpinFailed:"取消置頂失敗",pinToTop:"移到最前",unpinFromTop:"取消置頂",appIcon:"應用圖標",noDescription:"這個應用還沒有介紹～",pageTitle:"智識廣場",pageSubtitle:"匯聚企業AI智慧，探索未來無限可能，用知識引擎開啟協作新紀元！",tags:{all:"全部標籤",noTag:"暫無標籤",loadFailed:"加載標籤失敗"},confirmDelete:"該應用將被永久刪除，不可恢復及撤銷。確定要刪除嗎？",emptyState:{title:"廣場上空空如也",description:"快去應用工廠創建AI應用吧",createButton:"去創建應用"},types:{all:"全部",chat:"聊天助手",agentchat:"Agent",workflow:"工作流",completion:"文本生成",advancedchat:"對話流"}}},$a={"en-US":Qa,"zh-Hans":Oa,"zh-Hant":Za},Ya="zh-Hans",Is=()=>la.get(Ka)||Ya,ja=aa({legacy:!1,locale:Is(),fallbackLocale:"en-US",messages:$a}),Ja={timeout:1e5,headers:{Accept:"application/json, text/plain, */*","Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"},paramsSerializer:{serialize:Cs.stringify}};class zt{constructor(o){fs(this,"config");fs(this,"axiosInstance");this.config=o,this.httpInit(),this.httpInterceptorsRequest(),this.httpInterceptorsResponse()}httpInit(){this.axiosInstance=mo.create({...Ja,...this.config})}httpInterceptorsRequest(){this.axiosInstance.interceptors.request.use(async o=>{const l=localStorage.getItem("console_token");l&&(o.headers.Authorization=`Bearer ${l}`);const t=Is();return o.headers["Accept-Language"]=t,o},o=>Promise.reject(o))}httpInterceptorsResponse(){this.axiosInstance.interceptors.response.use(o=>o,o=>(o.response&&o.response.status===403&&window.location.pathname!=="/nq/login"&&(window.location.href="/nq/login"),Promise.reject(o)))}request(o,l,t,d){const m={method:o,url:l,...t,...d};return new Promise((u,r)=>{this.axiosInstance.request(m).then(v=>{u(v)}).catch(v=>{r(v)})})}post(o,l,t){return this.request("post",o,(t==null?void 0:t.params)||{},{...t,data:l})}get(o,l){return this.request("get",o,(l==null?void 0:l.params)||{},l)}delete(o,l){return this.request("delete",o,(l==null?void 0:l.params)||{},l)}put(o,l,t){return this.request("put",o,(t==null?void 0:t.params)||{},{...t,data:l})}}new zt;function lt(){const s={fileConvertService:{baseUrl:"http://*************:8012",convertApi:"/getConvertedFile"},workspaceSettings:{apiExtensions:"http://localhost:3000/embed/api-extensions",dataSources:"http://localhost:3000/embed/data-sources",modelProviders:"http://localhost:3000/embed/model-providers",modalLanguage:"http://localhost:3000/embed/modal-language",modalUserinfo:"http://localhost:3000/embed/modal-userinfo"},loginRedirectUrl:"/explore/apps"},o={};return window._aa_global_config||(console.warn("请注意，当前未正确挂载 _aa_global_config，请检查部署环境是否正确部署了 config.js"),window._aa_global_config={}),window._server_config||(console.warn("请注意，当前未正确挂载 _server_config，请检查部署环境是否正确部署了 config.js"),window._server_config={}),Object.assign(s,window._aa_global_config),Object.assign(o,window._server_config),{globalConfig:s,serverConfig:o}}const{serverConfig:An}=lt(),ie=new zt({baseURL:`${An.VITE_API_BASE_URL}/myquery`,headers:{"Content-Type":"multipart/form-data"}}),yn=async()=>ie.get("/api/get_siteuser_data/"),Ga=async()=>ie.get("/views/use-queryapp-list/"),Wa=async s=>ie.get("/views/topic-list/",{params:{qid:s}}),Xa=async s=>ie.get("/views/taskmessage-list/",{params:{tid:s}}),bn=async(s,o,l,t,d="object")=>{if(o){const m=localStorage.getItem("console_token"),u=new FormData;u.append("content",s);let r=`${An.VITE_API_BASE_URL}/myquery/views/taskmessage-add/?stream=${o}&stream_out_type=${d}`;l&&(r+=`&qid=${l}`),t&&(r+=`&tid=${t}`);const v=await fetch(r,{method:"POST",headers:{Authorization:`Bearer ${m}`},body:u});if(!v.ok)throw new Error("Network response was not ok");return v.body}else return await ie.post("/views/taskmessage-add/",{content:s},{params:{stream:o,qid:l,tid:t}})},el=async(s,o)=>ie.get("/ws/kbases/page",{params:{page:s,count:o}}),tl=async(s,o,l,t)=>ie.post("/ws/kbases/add",{title:s,description:o,type:l}),ol=async(s,o,l,t,d)=>ie.post(`ws/kbases/${l}/modify`,{title:s,description:o,siteuser_ids:t}),jt=async s=>ie.get(`/ws/kbases/${s}`,{}),sl=async(s,o,l,t)=>ie.get("/ws/documents/page",{params:{kbase_id:s,page:o,count:l,keyword:t}}),po=async(s,o)=>ie.get("/ws/documents/"+o,{params:{kbase_id:s}}),nl=async(s,o)=>ie.post("/ws/documents/"+o+"/delete",{kbase_id:s}),al=async(s,o)=>ie.get("/api/queryapp-list/",{params:{p:s,count:o}}),ll=async(s,o,l,t)=>ie.post("/api/queryapp-add/",{title:s,description:o,embedding_model:l,icon_path:t}),il=async(s,o,l,t,d)=>ie.post("/api/queryapp-add/",{title:s,description:o,retrival_type:l,history_limit:d},{params:{qid:t}}),cl=async s=>ie.get("/api/queryapp-detail/",{params:{qid:s}}),wn=async s=>ie.get("/api/queryapp-del/",{params:{qid:s}}),rl=async(s,o)=>ie.get("/api/set-queryapp-status/",{params:{qid:s,status:o}}),dl=async(s,o,l,t="",d="",m="",u="",r="",v="")=>ie.post("/api/queryapp-update/",{prompt:o,opening_statement:l,kids:t,aids:d,ftids:m,sids:u,dids:r,model_name:v},{params:{qid:s}}),ul=async s=>ie.get("/api/get-department-sons/",{params:{did:s}}),ml=async()=>ie.get("/api/get-all-departments/",{}),pl=async s=>ie.get("/api/get-department-staffs/",{params:{did:s}}),Xs=async()=>ie.get("/api/get-all-no-position-siteusers/",{}),vl=async()=>ie.get("/api/get-all-staffs/",{}),hl=async s=>ie.get("/api/get-department-fathers/",{params:{did:s}}),fl=async()=>ie.get("/ws/kbases/all",{}),en=async(s,o,l)=>ie.post("/views/taskmessage-delete/",{qid:s,tid:o,tmid:l}),_l=async(s,o,l)=>ie.get("/ws/documents/"+o+"/doc_fragments/all",{params:{kbase_id:s,keyword:l}}),gl=async(s,o,l)=>ie.get("/ws/documents/"+o+"/doc_fragments/"+l,{params:{kbase_id:s}}),Al=async(s,o,l,t)=>ie.post("/ws/documents/"+o+"/doc_fragments/"+l+"/modify",{kbase_id:s,content:t}),yl=async(s,o,l)=>ie.post("/ws/documents/"+o+"/doc_fragments/add",{kbase_id:s,content:l}),bl=async(s,o,l)=>ie.post("/ws/documents/"+o+"/doc_fragments/"+l+"/delete",{kbase_id:s}),Bs=async(s,o)=>ie.post(`/ws/kbases/${s}/test`,{content:o}),wl=async(s,o)=>ie.get("/ws/query_tools/page",{params:{page:s,count:o}}),Cn=async(s,o)=>ie.post("/ws/query_tools/add",{name:s,description:o}),Ts=async s=>ie.get("/ws/query_tools/"+s,{params:{}}),Cl=async(s,o,l,t,d,m,u,r)=>{const v={apiurl:o,apimethod:l,apiheaders:t||"{}",apiqueryparams:d||"{}",apibody:m||"{}",response:u||"{}",fun_name:r||""};return ie.post(`/ws/query_tools/${s}/modify`,v)},kl=async(s,o,l,t)=>{const d={name:o,description:l,siteuser_ids:t};return ie.post(`/ws/query_tools/${s}/modify`,d)},kn=async s=>ie.post(`/ws/query_tools/${s}/delete`),xl=async s=>ie.post(`/ws/kbases/${s}/delete`),Sl=async()=>ie.get("/ws/query_tools/all",{}),Il=async(s,o,l)=>ie.post(`/ws/query_tools/${s}/validate`,{query_params:o,request_body:l}),xn=async(s,o,l="",t="",d="",m="")=>{let u={kbase_id:s,learn_type:o};return l=="text"?(u.title=t,u.content=d,u.type=l):u.file=m,ie.post("/ws/documents/add",u)},Bl=async(s,o)=>ie.post(`/ws/documents/${o}/learn`,{kbase_id:s},{timeout:0}),Tl=async(s,o)=>ie.post("/views/topic-delete/",{qid:s,tid:o}),Ml=async(s,o)=>ie.get("/ws/query_file_templates/page",{params:{page:s,count:o}}),Dl=async()=>ie.get("/ws/query_file_templates/all",{params:{}}),Vl=async s=>ie.get("/ws/query_file_templates/"+s,{params:{}}),El=async(s,o,l,t,d)=>{const m={name:s,description:o,type:l,params:t,file:d};return ie.post("/ws/query_file_templates/add",m)},Ll=async(s,o,l,t,d)=>{const m={name:o,description:l,params:t,file:d};return ie.post("/ws/query_file_templates/"+s+"/modify",m)},zl=async s=>ie.post("/ws/query_file_templates/"+s+"/delete",{},{headers:{"Content-Type":"application/json"}}),Fl=async s=>ie.post("/views/taskmessage-attachments/",{tmid:s}),KC=async(s,o)=>ie.get("/ws/app_remodels/page",{params:{page:s,count:o}}),HC=async s=>ie.post("/ws/app_remodels/batch_delete",{app_remodel_ids:s},{headers:{"Content-Type":"application/json"}}),QC=async(s,o)=>ie.post("/ws/app_remodels/batch_update_status",{app_remodel_ids:s,status:o},{headers:{"Content-Type":"application/json"}}),OC=async s=>ie.post("/ws/app_remodels/add",{app_remodels:s},{headers:{"Content-Type":"application/json"}}),ZC=async()=>ie.get("/ws/app_remodels/modelcraft_models_all",{}),$C=async(s,o,l)=>ie.post(`/ws/documents/${o}/modify`,{kbase_id:s,description:l}),YC=async({kbase_id:s,document_ids:o,is_enabled:l})=>ie.post("/ws/documents/batch_modify",{kbase_id:s,document_ids:o,is_enabled:l}),jC=async({document_ids:s,kbase_id:o})=>ie.post("/ws/documents/batch_delete",{document_ids:s,kbase_id:o}),JC=async(s,o)=>ie.post("/ws/documents/status",{kbase_id:s,document_ids:o}),GC=async(s,o,l,t)=>ie.post(`/ws/documents/${o}/doc_fragments/${l}/modify_info`,{kbase_id:s,is_enabled:t}),WC=async(s,o,l,t)=>ie.post(`/ws/documents/${o}/doc_fragments/${l}/points/${t}/delete`,{kbase_id:s}),XC=async(s,o,l,t)=>ie.post(`/ws/documents/${o}/doc_fragments/${l}/add_point`,{kbase_id:s,content:t}),ek=async(s,o,l,t,d)=>ie.post(`/ws/documents/${o}/doc_fragments/${l}/points/${t}/modify`,{kbase_id:s,content:d}),tn=async(s,o,l)=>ie.get("/ws/app_remodels/page",{params:{page:s,count:o,keyword:l}}),uo=async s=>{try{const o=await s;return o&&o.data&&o.data.detail&&o.data.detail.error&&(o.data.error=o.data.detail.error,o.data.message=o.data.detail.message||"操作失败"),o}catch(o){throw o.response&&o.response.data&&o.response.data.detail&&o.response.data.detail.error&&(o.response.data.error=o.response.data.detail.error,o.response.data.message=o.response.data.detail.message||"操作失败"),o}},Ul=s=>{const o=new zt(s),l=o.get.bind(o),t=o.post.bind(o),d=o.put.bind(o),m=o.delete.bind(o),u=o.request.bind(o);return o.get=async(...r)=>uo(l(...r)),o.post=async(...r)=>uo(t(...r)),o.put=async(...r)=>uo(d(...r)),o.delete=async(...r)=>uo(m(...r)),o.request=async(...r)=>uo(u(...r)),o},{serverConfig:Pl}=lt(),je=Ul({baseURL:`${Pl.VITE_API_BASE_URL}/api/v1`,headers:{"Content-Type":"multipart/form-data"}}),Rl=async s=>je.post("/login",s),Nl=async()=>je.get("/users/current"),tk=async s=>je.get(`/tenants/${s}/departments/all`),ok=async(s,o,l)=>{const t={name:l};return o&&(t.parent_id=o),je.post(`/tenants/${s}/departments`,t)},sk=async(s,o,l,t)=>{const d={name:t};return d.sequence=1,je.post(`/tenants/${s}/departments/${o}`,d)},nk=async(s,o)=>je.delete(`/tenants/${s}/departments/${o}`),ak=async(s,o={})=>{const l={};o.department_id&&(l.department_id=o.department_id),o.name&&(l.name=o.name),o.email&&(l.email=o.email),o.status&&(l.status=o.status),o.enabled&&(l.enabled=o.enabled=="enabled"),l.page=o.page||1,l.limit=o.limit||20;const t=Cs.stringify(l);return je.get(`/tenants/${s}/members?${t}`)},lk=async s=>je.post("/users/create_user_and_member",s,{headers:{"Content-Type":"application/json"}}),ik=async(s,o)=>je.post(`/users/update_user_and_member/${s}`,o,{headers:{"Content-Type":"application/json"}}),ck=async(s,o={})=>{const l={};o.name&&(l.name=o.name),l.page=o.page||1,l.limit=o.limit||20;const t=Cs.stringify(l);return je.get(`/tenants/${s}/roles?${t}`)},rk=async s=>(console.log("获取license信息URL:",`/tenants/${s}/license`),je.request("get",`/tenants/${s}/license`,{},{headers:{"Content-Type":"application/json"}})),dk=async(s,o)=>(console.log("激活license URL:",`/tenants/${s}/license`),je.request("post",`/tenants/${s}/license`,{},{headers:{"Content-Type":"application/json"},data:{data:o}})),uk=async s=>(console.log("移除license URL:",`/tenants/${s}/license`),je.request("delete",`/tenants/${s}/license`,{},{headers:{"Content-Type":"application/json"}})),mk=async(s,o,l)=>{const t={enabled:l};return je.post(`/tenants/${s}/members/${o}/status`,t)},Bt=vo("user",{state:()=>({userInfo:null}),getters:{isLoggedIn:s=>!!s.userInfo},actions:{async fetchUserInfo(s){try{const o=await Nl().catch(l=>(console.log(l),l&&l.response&&l.response.status===401&&(localStorage.removeItem("console_token"),s&&s.fullPath?localStorage.setItem("redirectAfterLogin",s.fullPath):localStorage.setItem("redirectAfterLogin",window.location.pathname),window.location.replace("/nq/login")),null));if(o&&o.data)return this.userInfo=o.data,o.data}catch(o){console.error("Failed to fetch user info:",o),this.userInfo=null}return null},clearUserInfo(){this.userInfo=null}}}),ql="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFsAAAAZCAMAAABpeKLMAAABMlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAARm/8AAAAAAAAAAAASm/8AAAAAAAAAAAAAAAAAAAAAAAAAAAAQmv8AAAARm/8AAAAAAAAAAAAAAAAPl/8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQm/8AAAAAkP8Rm/8Sm/8AAAASm/8AAAAAAAAAAAASm/8Rmv8Rm/8AAAAQmv8AAAANm/8AAAARm/8Sm/8AAAARm/8Rm/8AAAARmv8AAAARm/8AAAAAAAARm/8RnP8Rmv8Sm/8AAAAAAAAQm/8AAAAAAAARm/8Rm/8AAAASm/8RnP8Rm/8Rm/8AAAAAAAAAAAASm/8AAAARmv8AAAARmf8Nmf8Unf8Ajv8SnP8Sm/8Rm/8Pmv8AAAASm//DRRaRAAAAZHRSTlMAv2ttVErk3f7z5zv8+S4e9tZ7XlpCPiceENBwaCMVEAkF79HEtJdGQi8I+fLr2MyuqZxqWE09GhgG7eXfxrKhoIyIhnZ1ZUs5NzQwDP3dvrisp5SSkIGAcGJfUS0lDQS5f3pTrcPxrwAAAv9JREFUSMd1lGdD4kAQhgdFQiKKdJDeBUVBBcTeu56ep57lzivh//8Fsy/ZZVN8PySzk9mHZWZ2aKyr+6W5nXWSlPColQY5FX+qlrWSWtJ8SsjTJaLCgaZ5IkUSiq1oeykSWl8cMj2Q0EDRdd13bSe/bOuyZrJEVViVHA8JqsZyj7iSc8OR/gvXNHbErORrcGR1KW5aHh4UYasSca0OTc0L1xQ2eGGf7j5ssHfRY0drKcpwe5JGmsSKc/I4doA9FlzYJ4Z/uWkYYZOycjARiWQmj8PpPtEEZ7dSTjY2s+3sce/CnmcfekQ5Fb7QBckCGzpwY98iHf9w+KST/Y19eOfH1oLkZLf8KKyTfYZjr20gKYdOds9I2T4RleE6Jhe2p8OeHSf7D2PO5WmXvTebzlomX1kZUn64gq5sNJ5/2s7+hWPv8/O/OtndWnpAZrNp5MqmZzS5nf0DyHOijy3k3c5Gl1WJoqMe+YJd2Ea8lf17SRAP+a9Y2chzn0FAcWeTl72VgYW9BuDPcXa+29k+Zs1SGp6KgOZqK3vBMZtwsdIW9rJZSdFtixvu7Aw8ZcGusHMWxuzGDbvrKYm9YFYSWsfiLzPriHiR2F54VI7OYhkbs6nGrEeJvQtcj6DmJltsfRDvirTEvtShhGWYRSV2roQLJNjJAK47kdwzbI7PIuJIYg9m4Ap/yR5VpC3Yh6KS0DuWO+ymIOJOYtMdXJjp7uxiiF2gDj4gCaJ60A7gV4al6CywK7GjOtQuIPINizfBFkXym+xT0XUQd6yKgXrTjiTqE8dPR30iCumQEo4HU40y7IZgQ22dC+NTGtq4Sbwlcy0e5ldbPiXB0oCMy9omKzvLI/zURCVvSdKq+LW4IIn2iNrhUaKMxMa/5WNnC9NVZl8FxBS/UMYQxSxgyII+4jV9JK6ODj0TnS0hubLWFoeBE4IKEY+qQ75LGqkYqaqcHPLCVTOsHAllFCMjtaJh5c+TZFO+J7v6jelYPV4gScHLWH1qKpbl68RFkWQlYkGiT3JPjny6Us1QAAAAAElFTkSuQmCC",Kl="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQBAMAAADt3eJSAAAAMFBMVEUAAADqAADqAADrAADrAADqAADqAADqAADpAADrAADqAADrAADqAADrAADoAADqAACXJdp+AAAAD3RSTlMAVt1ZMoTxPznup0vocGSoTsfSAAAAYUlEQVQI12NABs4mEFro/39FEM36/1v+/wAgQ/7/9/r/H4EM/f8/+v9/YmBg+f//9/7//x0YmP////j+/38DEONvPJABkvozHygFUvxrPVAxWHs/UDvYwPn/A2BWICxFAABeuTbmssc51wAAAABJRU5ErkJggg==",Hl={name:"LoginView",setup(){const s=n(""),o=n(""),l=n(""),t=n(!1),d=Ce();De();const m=Bt(),u=n(!1),r=et(()=>m.isLoggedIn),v=et(()=>s.value&&o.value);return{username:s,password:o,err_msg:l,showErr:t,isButtonRed:v,fetchhandleLogin:async()=>{if(!u.value){E({message:"请先阅读并同意用户隐私政策",type:"error",duration:3e3});return}try{const h=await Rl({username:s.value.trim(),password:o.value.trim()});if(h.data.error)t.value=!0,l.value=h.data.message;else{t.value=!1,l.value="";const g=h.data.access_token;if(localStorage.setItem("token",g),localStorage.setItem("console_token",g),localStorage.setItem("refresh_token",g),localStorage.removeItem("expiry_notice_hidden"),await m.fetchUserInfo(),r.value){const x=localStorage.getItem("redirectAfterLogin"),{globalConfig:y}=lt();x?(d.push(x),localStorage.removeItem("redirectAfterLogin")):y.loginRedirectUrl?window.location.href=y.loginRedirectUrl:d.push({name:"Index"})}else t.value=!0,l.value="获取用户信息失败"}}catch(h){console.error("发送消息失败:",h),t.value=!0,l.value="登录失败，请重试"}},privacyChecked:u}}},Ft=s=>(be("data-v-9d35469d"),s=s(),we(),s),Ql={class:"login-container"},Ol={class:"login-right"},Zl={class:"login-box"},$l=Ft(()=>e("img",{src:ql,alt:"",style:{height:"25px","margin-bottom":"40px"}},null,-1)),Yl=Ft(()=>e("div",{class:"font18 font-zhongcu",style:{"margin-bottom":"30px"}}," 用户登录 ",-1)),jl={class:"input-group"},Jl=Ft(()=>e("div",{class:"login-ipt-title font16"},[Q(" 账号"),e("span",{class:"required-star"},"*")],-1)),Gl={class:"input-group",style:{"margin-bottom":"5px"}},Wl=Ft(()=>e("div",{class:"login-ipt-title font16"},[Q(" 密码"),e("span",{class:"required-star"},"*")],-1)),Xl=Ft(()=>e("div",{class:"password-tip font12"}," 如果忘记密码，请找管理员找回或修改密码 ",-1)),ei={style:{height:"16px","margin-top":"5px"}},ti={key:0,class:"error-div font12"},oi=Ft(()=>e("img",{src:Kl,alt:"",class:"error-img"},null,-1)),si={class:"flex items-center justify-center mt-4"},ni=Ft(()=>e("a",{href:"#",target:"_blank",class:"text-[#129bff] hover:text-blue-700"},"用户隐私政策",-1));function ai(s,o,l,t,d,m){const u=_("el-checkbox");return a(),c("div",Ql,[e("div",Ol,[e("div",Zl,[$l,Yl,e("form",{onSubmit:o[2]||(o[2]=_e((...r)=>t.fetchhandleLogin&&t.fetchhandleLogin(...r),["prevent"]))},[e("div",jl,[Jl,me(e("input",{type:"text","onUpdate:modelValue":o[0]||(o[0]=r=>t.username=r),placeholder:"输入邮箱地址",required:"",class:"font14"},null,512),[[Ae,t.username]])]),e("div",Gl,[Wl,me(e("input",{type:"password","onUpdate:modelValue":o[1]||(o[1]=r=>t.password=r),placeholder:"输入密码",required:"",class:"font14"},null,512),[[Ae,t.password]])]),Xl,e("div",ei,[t.showErr?(a(),c("div",ti,[oi,e("span",null,S(t.err_msg),1)])):z("",!0)]),e("button",{type:"submit",class:W(["login-button login-button-no font18",{"theme-background":t.isButtonRed}])}," 登录 ",2)],32),e("div",si,[i(u,{modelValue:t.privacyChecked,"onUpdate:modelValue":o[3]||(o[3]=r=>t.privacyChecked=r),class:"custom-checkbox"},{default:p(()=>[Q(" 我已阅读并同意 "),ni]),_:1},8,["modelValue"])])])])])}const on=ue(Hl,[["render",ai],["__scopeId","data-v-9d35469d"]]);function He(s){return s?s.charAt(0):""}function pk(s){return s?s.slice(0,2):""}function ze(s,o){return o.some(l=>s.includes(l))}var Ms=(s=>(s.StartTool="start_tool",s.Data="data",s))(Ms||{});const li={name:"TestDoc1",components:{VueOfficeDocx:ia},props:{docxSrc:{type:String,required:!0}},setup(s){const o=n(""),l=()=>{console.log("文档渲染完成")},t=d=>{console.error("文档渲染失败:",d)};return ge(()=>{o.value=s.docxSrc}),{docContent:o,renderedHandler:l,errorHandler:t}}};function ii(s,o,l,t,d,m){const u=_("vue-office-docx");return l.docxSrc?(a(),X(u,{key:0,src:l.docxSrc,style:{width:"100%",height:"100%"},onRendered:t.renderedHandler,onError:t.errorHandler},null,8,["src","onRendered","onError"])):z("",!0)}const ci=ue(li,[["render",ii]]),{serverConfig:ri}=lt(),Le=new zt({baseURL:`${ri.VITE_API_BASE_URL}/avatar`,headers:{"Content-Type":"multipart/form-data"}}),di=async()=>Le.get("/ws/departments/all",{}),ui=async(s,o)=>Le.get("/ws/departments/page",{params:{page:s,count:o}}),mi=async s=>Le.post(`/ws/departments/${s}/delete`),Sn=async s=>Le.get("/ws/departments/"+s,{}),pi=async(s,o)=>Le.post("/ws/departments/add",{father_id:s,name:o}),vi=async(s,o)=>Le.post("/ws/departments/"+s+"/modify",{name:o}),hi=async(s,o,l)=>Le.get("/ws/departments/"+s+"/siteusers/page",{params:{page:o,count:l}}),fi=async(s,o,l)=>Le.get("/ws/departments/"+s+"/positions/page",{params:{page:o,count:l}}),In=async s=>Le.post(`/ws/positions/${s}/delete`,{},{headers:{"Content-Type":"application/json"}}),Bn=async s=>Le.get("/ws/positions/"+s),Tn=async(s,o,l,t)=>Le.post("/ws/positions/"+s+"/modify",{name:o,is_able:l,is_leader:t}),Mn=async(s,o,l,t,d)=>Le.post("/ws/positions/add",{department_id:s,siteuser_id:o,name:l,is_able:t,is_leader:d}),_i=async s=>Le.get("/ws/siteusers/search",{params:{keywords:s}}),gi=async(s,o)=>Le.get("/ws/siteusers/page",{params:{page:s,count:o}}),Ai=async s=>Le.post("/ws/siteusers/"+s+"/delete",{},{headers:{"Content-Type":"application/json"}}),yi=async(s,o,l)=>Le.get("/ws/siteusers/"+s+"/positions/page",{params:{page:o,count:l}}),Dn=async s=>Le.get("/ws/siteusers/"+s,{}),bi=async(s,o,l,t,d,m,u)=>{let r="";o!==""&&o!==void 0&&o!==null&&(r=o);let v="";return t!==""&&t!==void 0&&t!==null&&(v=t),Le.post("/ws/siteusers/add",{username:s,password:r,nickname:l,tel:v,is_valid:d,role:m,permissions:u})},wi=async(s,o,l,t,d,m,u,r)=>{let v="";l!==""&&l!==void 0&&l!==null&&(v=l);let k="";return d!==""&&d!==void 0&&d!==null&&(k=d),Le.post("/ws/siteusers/"+s+"/modify",{username:o,password:v,nickname:t,tel:k,is_valid:m,role:u,permissions:r})},Vn=async(s,o,l)=>Le.post(`/api/siteusers/${s}/modify_password`,{old_password:o,new_password:l}),Po="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAYlJREFUOE9jZGBgYJDfuCqHkfF/NwMDIweIjwsEyMh/dBWTdvCWkbmAroYRbNCmlZcZGRh18BkCkktSUmXIVdN8s/PVU+sIGeVbyOrBBilsWnWDgYFBnRiDarUNGN7//Plo7ePH1qmqqk9gesgyCKT5zc+f1xe/vmtTJKv9DsQnySBfKVmGScYWcIc///79zKwPH+wbpKS+kWQQMyMjQ56aFoOhoBADEyNYK8ONTx+3piir+5BkEI4wfPDAL0xx4AzykJRm2PH8KbLj8LtIgoOT4de/vww//v5lYGViYvj4+zdYc6G6NkP/zavEGcQETFsLzG0ZPvz+xXDxwzsGEXYOsCtsRMUYuJhZGLpvXCHOIAMBIQZOZhYGA0EhhrWPHzC4SUgz8LGyMtz58plBiI2NYcWj+8QZpMsvyHD7yycGZR5ehu9//jK8+fWDQY9fkOHHv78MT759Y3jx4ztxBhHKKmjyAxz9WFyLcBGxxQgOL99+4BemBimPiCzYMA36/4OBganygV/oBABNz7ET0z36UgAAAABJRU5ErkJggg==",Ci="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAYJJREFUOE9jZGBgYPDL+uHByPhvFgMDgyyIjw/cv3dp5aXtFhHoahhBAv7Z3x4RYwhI7fWrJxjY2DlWX9lpFYZsGMyg/4RcApMHGfTv318GDk5uFMPINghkMLJhJBv0+NENhi+fP8A9wMHJBQ4zkg3CFgQbp3Ix0tYgfQ0mhqu3/zH8+YtqPwszA4O2KhPDxRv/UCRwuqgln41h3e4/DOeu/WNws2YGa9p19C+DkRYTQ5ArC0PNxF/EGeTryMIgI87IMHPlb4Y5LRxgTSk1PxjSw1kZnrz8z7B5/x/iDBIXZmToKGJnmLj4F0OEFytY04ptvxnyY9kYKvp+Mrx8i5rs8AZ2fyU7w69fDAwHTkNsdzBlYWBjY2AobP+JEXF4DYr0ZmEIdmNhSKr+AdY4r5WDYe2uPwzLt6J6CySH1yAxIUYGVQUmhqPnIFFnbcTMcPvBP4ZX7zBzE+3TEbEZGKYO2UVEFyMYlvxneLxxGpccOIuQUrChGfT4/3+mtE3TOHYAAC5BsxPA+U4kAAAAAElFTkSuQmCC",ki="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAcFJREFUOE/llMsrRHEUx7+/Ox5Dg2EsvBsmhlAWIiFT3q+YUfLKSrGSLf+ArT/BEuVdwigppcTChiIz12O8h8ttmBnX/HR/Mh4Zzdg6q/M7v/P9dDrndAgAVEy3D4DQYQIo5bcvK08qForjCgylSYU733OIHKicbdsHRfpvEPnPpKtFV2bL1cbFdklVctnB5/w30EybFYDWH1BfTjdEt4Nf41dLGvWNtnfNn0CyWHA97E4eLpX2ZLfeyu+AQIbEIgzl93sLv36yb00Lk2W9Cb2PAYE4cOjMMCJTrQNHOAbcFyzmjixTVUCgjx5Sr0spjlaM49o/gr6MhTc3j6X+J1AwF4RcTRaORRtuXQLyYrNx5rjAs0eC3XnHmhOrjMGNk62PbD/3qEtvwuLRKhJV8UzAi6dQEA5RIRG4erLjhb5AG5GM9fNN3yC588a0OkxZFlhSeFAYqlMMTDxvNaM5rQaEALOWZRb7taIWXT34hxOogsNhc1xCHRqJHI0eo3sTaNBWsEWcsy5/nr/v8WuU0bh3iZCoBNkX3Q64PW4oiAKEEEgeyQfIzzPy43UgODA3jWWwhfT3sH0HUcAJSgdXjOMjr6mqzRNEvWzYAAAAAElFTkSuQmCC",xi="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAjNJREFUOE+t019IU1EcB/DvOfduM4oxEafMCJOMZhFGuFWrtqgckg899s/wZRAixKTHHvaQD4K0zaeEoB6iQT2EJkGGJINiPskQ9WG5ibi1OZt3c86t3T9xV8sWE7zReTmcwzmf8/v9zjkEv1oyufEGhFwvj//uY2vrfL7A3zKfOva62hqyA3EhEBzZDYrGk9jcyolqteZGNUwRlN7cAqWkKqYYkiOWMQ3L3jSdPv6qnME/QWXszzT3DOW28+Ay2YoSMpTGThhbmuTJPUNVL0HCl/p6XesO5AJdvZcIaVhNy263Vm2+wBfCB580tMIFkcAFaqwzPntxzXflkLbZoARaySzH7k7cnppPzfcSi8/yQBTEh1cP2xmdRndACcTlueyH5fcCVdFHxP7W7gFw36w3w9pkRbqQRiQTganBBF/Ih6XMEnqO9qBD34HZ9VksbizCZrCB+84hnA5jcnVSPtv7GyIg8J73ooapQZ+/D6O2UXiCHsyl5jB0ZggSJAQSAYxFxjByYQQqqkK/vx+8xFdC+n16uC1uyODA5wEMmgcxHhkvQXI0tepadDd3wzHtwPC54dI65ycnEtuJn1DnROdjIhGnnBpL2VKJBEmAYb8BWrUW8Vwc0Wy0lOpCagEiRDCEAQVFUSpiJjEDiUhu0vWuq03kRcelxsu9KqLSKSl2USpyH+NTzwE8/c8PEsDX+LcgyzInFUXEC0FDY117xRfxB4IvKSEXlUCCKE5bz7bfkff8AJUd+tQHKKIcAAAAAElFTkSuQmCC",Si="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAYtJREFUOE/V1N8rg1EYB/Dv8573XRslNxZJ4WJKQpGUH1tpa1lzIeQCRbQbd0r5G7jwF7x/gCghknIxZZKi9Ia52PwolE0klvY6et8sM3vXu905d+ec53w6z3meDgFAYrx+mhPNA2TV5kaDtfuepeYeF7V5TjJjSFt4n2g4BdCQC9H2mHsUoj/wSMe7HdTVH06PT0HnAOrMQNLwLPjr0zXtbXZQ78ht6kxBkHaYv8TOKLTaSd7JuDbPCxLavLAE5n8uHr8/wv6ak/yBt7wgCAyiLwCqbQRI0EEeVXakvilPflDqLpx/56IvXNlkpbow6FdVeNQmKzX/EbJYgY9EjhbLlZpkAZXXgN9cQBycgXq4BX4bBtRkFtAAYu4xoLgEnwcbepsJjhZ8Rk7BWj3gsTuoweUMzAhyDYFK7VCPtsEanUgGV0BllWBNLvCHK6ihdXOQHiUwkL0KrHsAyaUFUEUt+H0E0PrnzzBTfiYavEu69hsy9Y1kLx2/tMmKQ29Isx9blrQSHJgrkpXFL3awshPlJ9QIAAAAAElFTkSuQmCC",Ii="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAa5JREFUOE/llEsoRGEUx//fXOY9TNnO1NzkmYVZahQzHgvUTRaISYlElOyUhZ2t7OwUG4ppNiS5G9kISYQZuYNJYiI1M8y9xtW9zMV4zdg6q++8fp3v+845BACCLtugCDIGQCvp34m+mrkzldVUqp31O8kxRDKcu2gfgLyfIJLP2NSJLHd/SFhbdmjrW6QcRWRQ0EVzImBLBWTuG8HT3e1ZbHXeoW/qDiZy/gSSkp9uQgdhz3R5dtfQjaSnBdI5G5AzMqEUHr++3KQ8kxWkZzSaFggUBVPbANRFpSAqlQzkfXsr2W29temBlFrExGUky6mV5Wx/BH34rYCF5ej/BFIX2yHyPITjfWTmlYBQFHj/PjTFdoAQPF4GEb+6eN/Rn9+IqDUwNnZA4I4AFYXM3ELEttaRQRcgtr0OfU0jwgtTEKPh30HmgVEIp37cry3D1NoLIeADv7sB4eQQBsaNiHf6wyQR4OuK9FUMIktzcrChrhmRxVkl0cC0I+Kd+R0kz4xWB/HhXg4mGh3E2Ms52fduWN8qSnWNfLMd/FaWy39tyNQW2xegBwJx2MIGxp8BbRzLE1bgY2QAAAAASUVORK5CYII=",Bi="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAahJREFUOE9jZGBgYHjipFDwn4GxnYGBgQPExwW4XPw/8lq6OrA5el9AV8MIEnjspHiLgYFBFZ8hIDme4CQGvticN78P77Tm8I4A6YEDsEFPnBTv/2dgUCDGIIGsGoZ/H98/+rl3rTVXcOoTmB6yDAJp/vfuzfUv6xfb8KcUvQPxSTKI09GXQbhmItzhf1+/OMO8fqY9Y3rDN5IMYmBmYeCLy2Ng09RnYGBkAhv4+96NrXyhKT6kGYQlEBkZGB7I7LuvOGoQ7hSGM4zYtAwZ/v/8wfD77nUGVjUdBoa/f8FsRjZ2BjYNfYb/f/4w/Lp2DjlFYwY2SDFPYDzD7/s3wUmMVVWb4cfJ/QzsuqYQMSZmhj+P7zL8ff2CsEECuQ0Mv+/fYvh+dBcDp6Uzw5cNixh4QpIZft+5Bk4/f548YPh+eAdhg7ic/Rm+bl8FVsifUcXw6/oFBiYBIYY/D+8w/H3/BkyjZVbs6Qjkvf+/foLVMgmLMTD8/sXw79MHBgYWVgaGf38ZGP79w20QscUIjri7Lbvvvho0ZRNXsGEx6Acjw/9KmX0PJgAArdzVEzFK/2MAAAAASUVORK5CYII=";function Ti(s){for(var o=new RegExp("[`~!@#$^&*()=|{}':\";',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：“'。，、？\\s\\n]"),l="",t=0;t<s.length;t++)l+=s.substr(t,1).replace(o,"");return l}function En(s){const o=s.split(".");return o.length>1?o.pop():""}function Mi(s){const o=s.split(".");return o.length>0?o[0]:""}function Ko(s){switch(s.split(".").pop().toLowerCase()){case"txt":return Po;case"docx":case"doc":return Ci;case"csv":return ki;case"xlsx":return xi;case"md":return Po;case"html":return Si;case"pptx":case"ppt":return Ii;case"pdf":return Bi;default:return Po}}function Ze(s,o=100){let l;return function(...t){const d=this;clearTimeout(l),l=setTimeout(()=>s.apply(d,t),o)}}const Di={key:0,class:"rounded-md mt-[20px] border border-gray-200"},Vi={class:"flex items-center mb-2 ml-3"},Ei={class:"mr-1 text-blue-500"},Li=e("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 18 18",fill:"none"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M3 7.875C3 4.97549 5.35049 2.625 8.25 2.625C11.1495 2.625 13.5 4.97549 13.5 7.875C13.5 8.00246 13.5325 8.12781 13.5944 8.23923L14.9704 10.7161L14.1029 10.8896C13.7523 10.9597 13.5 11.2675 13.5 11.625V13.125H13.1871L10.9983 12.7602C10.7991 12.727 10.595 12.7755 10.432 12.8948C10.2691 13.0141 10.1611 13.194 10.1325 13.3939L9.84953 15.375H5.4943L4.86146 12.0902C4.83179 11.9362 4.75459 11.7954 4.64072 11.6876C3.62933 10.7297 3 9.37647 3 7.875ZM8.25 1.125C4.52206 1.125 1.5 4.14706 1.5 7.875C1.5 9.71622 2.23809 11.3862 3.4327 12.6032L4.13854 16.2669C4.20655 16.6199 4.5155 16.875 4.875 16.875H10.5C10.8732 16.875 11.1897 16.6006 11.2425 16.2311L11.5089 14.366L13.0017 14.6148C13.0424 14.6216 13.0837 14.625 13.125 14.625H14.25C14.6642 14.625 15 14.2892 15 13.875V12.2399L16.2721 11.9854C16.5069 11.9385 16.7052 11.7822 16.8057 11.5648C16.9063 11.3474 16.8969 11.0951 16.7806 10.8858L14.9971 7.67545C14.8916 4.03981 11.9112 1.125 8.25 1.125ZM8.25 6C7.62869 6 7.125 6.50369 7.125 7.125C7.125 7.53921 6.78921 7.875 6.375 7.875C5.96079 7.875 5.625 7.53921 5.625 7.125C5.625 5.67526 6.80026 4.5 8.25 4.5C9.69974 4.5 10.875 5.67526 10.875 7.125C10.875 8.31412 10.0843 9.31859 9 9.64129V10.125C9 10.5392 8.66421 10.875 8.25 10.875C7.83579 10.875 7.5 10.5392 7.5 10.125V9C7.5 8.58579 7.83579 8.25 8.25 8.25C8.87131 8.25 9.375 7.74631 9.375 7.125C9.375 6.50369 8.87131 6 8.25 6ZM8.25 11.625C8.66421 11.625 9 11.9608 9 12.375V12.75C9 13.1642 8.66421 13.5 8.25 13.5C7.83579 13.5 7.5 13.1642 7.5 12.75V12.375C7.5 11.9608 7.83579 11.625 8.25 11.625Z",fill:"#343A3F"})],-1),zi={class:"text-[#343A3F] font-medium text-[14px] leading-[22px] font-pingfang relative top-[-1px]"},Fi=["innerHTML"],Ln=ye({__name:"ThinkingBox",props:{content:{type:String,default:""},completed:{type:Boolean,default:!1}},setup(s){const o=s,l=et(()=>o.content?Xe(o.content):"");return(t,d)=>{const m=_("el-icon");return s.content?(a(),c("div",Di,[e("div",Vi,[e("div",Ei,[i(m,{size:"17"},{default:p(()=>[Li]),_:1})]),e("div",zi,S(s.completed?"已深度思考":"思考中..."),1)]),e("div",{class:"pl-3 border-0 border-solid border-l-2 border-[#E5E5E5] text-[#697077] text-[14px] font-normal leading-[22px] font-pingfang",innerHTML:l.value},null,8,Fi)])):z("",!0)}}});function zn(){const s=n(new Map),o={startTags:["<think>","<reasoning_content>","<reasoning>"],endTags:["</think>","</reasoning_content>","</reasoning>"]},l=h=>(s.value.has(h)||s.value.set(h,{isThinking:!1,thinkingContent:"",isCompleted:!1}),s.value.get(h)),t=h=>{for(const g of o.startTags)if(h.includes(g))return g;return null},d=h=>{const g=o.startTags.indexOf(h);return g!==-1?o.endTags[g]:null};return{processText:(h,g)=>{const x=l(g);let y={normalText:"",thinkingText:"",thinkingComplete:!1};if(x.isThinking){const w=o.endTags;let T=!1,A="",C=-1;for(const L of w){const B=h.indexOf(L);B!==-1&&(C===-1||B<C)&&(T=!0,A=L,C=B)}if(T){const L=h.split(A);y.thinkingText=L[0],y.normalText=L[1]||"",y.thinkingComplete=!0,x.isThinking=!1,x.isCompleted=!0}else y.thinkingText=h}else{const w=t(h);if(w){x.isThinking=!0;const T=h.split(w);y.normalText=T[0];const A=T[1]||"",C=d(w);if(C&&A.includes(C)){const L=A.split(C);y.thinkingText=L[0],y.normalText+=L[1]||"",y.thinkingComplete=!0,x.isThinking=!1,x.isCompleted=!0}else y.thinkingText=A}else y.normalText=h}return x.thinkingContent+=y.thinkingText,y},getThinkingContent:h=>l(h).thinkingContent.replace(/\n/g,"<br />"),hasThinkingContent:h=>l(h).thinkingContent.trim().length>0,isThinkingCompleted:h=>l(h).isCompleted,resetThinking:h=>{h?s.value.delete(h):s.value.clear()}}}const Ds="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAA+VBMVEUAAAAkgfUofPQhhvckgvYrePMvcvAnffMdivkihfczbO4ajvkpevMtdfIyb/Ajg/ccjPkmf/UtdfIudfIXk/spevQihvckgvUngfYnffQmgPb6/P8zbe91rPg0au4rePMYlP0djPkeiPgxcO8nfvQofvclePImgPonc+b1+f/x9/7r8v3q8/7I4P2mzfyWwvq82f2uzvuNuvo6i/Zcnvdon/Uxb+87kPhbpfklgfUVlv1RnflJkvYzbe4cjfo2au4divoxcfAzbO4Vlv0UlfsnfvYjhPYsd/I2ae0kgfUZkfobjvwybu02ae4Tkvs2ZesXlvgwYO8Unf88ia2sAAAAU3RSTlMAmr+xsb+/srGav7Gamr++p7+/tbGspaGcm4/6tbSzsKOampo4HRsXCvb18e/r4NjUzcnDv7Wxrq2sqKekpJyTjIyLhn91dHRya2hWVktEJiIQDQwhvcIAAAC3SURBVBjTbctVAoJAFEDRNxiIzgiKUrZSdnd35/4XIwy/3L/zAvz6HIqCIEna1fRszFKxSCA4RUiz6GBPzcoI47Prl+c0UjEmOsCvSF1QlBwmZGvDzdsrqFsnpFJ5gOB40ONQLjNMF8TsElaCJE+qnUWtzYSS4Xge7qVSmZs3Ms0EEwrHoxewN2WOQ+NW3z2I5r8AumOZ5xl68ASnE1IDvPewBjdLUx27D1kDaOYxyLKiONq9wac/JzES++Z5PgAAAAAASUVORK5CYII=",Tt="data:image/png;base64,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",Vs="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAABCAYAAACc0f2yAAAAAXNSR0IArs4c6QAAAFdJREFUGFddw0EKgkAAAMBZMzMMCbz0hE79/yk+oUsgoqRmbRcFcWBCjPGBO26ocEWJCwqclzkynHDcPGCdIkHYhbj7w4zv5gfrERMGvJc9OrRo8MIT9R9YNBstURfjYAAAAABJRU5ErkJggg==",Ui="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAWCAMAAADzapwJAAAAh1BMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAD3YishAAAALHRSTlMA+ezm/K6YHQzEXyshv0MG893Z0rmlSznjzZyDb1pXM/B6aSYX2LKQiVtQFbnmejoAAADeSURBVBjTZc9XcoUwDAXQ64KNTe/waK+la//rC+OYhCHny9aMdCV4sR2VGq3GQWYr8gabwVsruqo5en5Fi7pStcJpGbuF8MIbYy02HRMRDiLBOiAr84dP9S2P/CWDpQk/+MU/JrKo830uBfv8vEZvcC7D9GDu0wnOiXEuOmwCBjG4+MQY4sYkbqmhRCPj8xAtG6S0LyD3sqIUqGmG8/4JZ6EawFPIOw7ustfuupJS7NKEivgvAsjWj+lSkFQhvLIC5oI2xVuMX4nQCfFxSTWOXhkn33sUkGjxX9voU+Ub9MgPl/gQXesAAAAASUVORK5CYII=",Pi="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAWCAMAAADzapwJAAAAk1BMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAB6eSN1AAAAMXRSTlMA/vqVicn159B8bTIYCeKumJCFZQ3utqWcXVZSOhzy1ryzgXVyY1hNRj8S2sagZicj8kBvDAAAAN1JREFUGNNdkemWgjAMhW9CpWWRVVAR92UcZ33/p7NUSpH7I8n5mqTnnsCqDQtxuc8x1uIkabdXFe2y2NFP6YuoK/7P0r9auubj0DPPSfS97Jm8CUz6pdC8JzWMvNkrF/ynY+Bv3zG+Uh2SHBO8phgRtVO84BJXxhRj5iFIBqxslaYj3JCw+IiSYVVS//t3hpYiOLuZceJrQ7LAoIZXnV3STkT1cPyj4+qgq0e1hNNdIHyt3VAAjAf6rWfK3FUuPAzffBnGxvZN0ckNbldM+0OtmOro/ZqN+Fnmpb3SE+ySCzEAL2GGAAAAAElFTkSuQmCC",Es="/nq/static/gif/load1-BGgtz7pR.gif",Jt="/nq/static/gif/loading-BoJ2QU73.gif",Ri="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAAAXNSR0IArs4c6QAAA3BJREFUaEPtmjtoFFEUhr+zu4qKIvgoRLCx0KCFNlooGC2Mu4mkcTYiCvFBGsFCCxEsooXaaCEqiE+IQjKbICLZUUHUUg1WPksRLUSwUAkxOkdnsxvGuDuZnZ0ZQ7LTzcw59/z/Pf+dO/eeK0ySS/zwMO7oYlKswWaJCr58/LTrZSOKivBOf/Est1U+jNeeJyjD1JnM5iRwAOIhUAawAmf5xpFcVgYrEfImktduoG283ojpfU8uI9urJpK1tEmVuy7HtwhvYgI9EkZZDiwrxRRhi5mWe+UwVMyIYelplINFJ2vFU1o6O8WOk4hhapLZ3AHShbjCmVxaDlVHJK8O880j/nSYGbkUJ4lSLMPSDpSLxfv7uYw0VUvkEbCh2BO7c2m5/p+ItKNcK8Z+nMtIY50IQj0jtcrRsLQurUInGnmtONiz/Zq1EzQk7HBnezuBJmxem81iur5a0WTEIaFCT62S8fIXpa1EJjJpZfO6R+FKpERgr5mRqwVlRDVGGh9qasEgB0VpCPtP2PnjVeH155mcebRRfkZKJMpMlGs7sozUiQTsgXpGRr/fHvNIwM4N5FbPyJTJSGenJl6uZV9hHgl5Q0IYmUdWPOFyaTUambSylu5UpSuQ4H06ibDLTMuNSCfESUNk0kjLpzpCM4tsjISG0GdDdSJTZh7xqYjQzOrSmjLSCrrUTQjDNnT3puVBNbqLTFo1bj4MzkiysKtJvpdd1pqaHJ7GHPe71Ax2iHK++Cy8vd8at4O+fINFVkaG3GCNft2CcBRYDczyyFh4RJwgQTboVBhO2NzuaZEXbqDZvB5WOOVTbuES8Rl0XDOjX1chPANS4xo7BsLDXFo2lbOtXLGKYambtfSEKkeKwF6hbB/+wXvnPjGduUnhJrCuBFzhUm9GOiYcESOv+VJZTYX9vWm54IBsy+tSm0LJrcEFeuBnkuZbTfJpIhL5Z6N8m6WNovT9KUnPG82EYMpX2gOVp712433p2YfR2BjAdJRzwLSiu4py3MxwDBGn3l7x8hojkRdD/yICztdspQvpoCjt7hJDMCIxlKfHEHHj/GgrrX3NMuAjsQWTihmJ5cCAsh6YPwbsQDJFa/dm+eiXhCcR56UR8xEO9TGoK5GbKIdqfA/qQERKTlEecxIYspXnfc3ypBopjbWN5exVLQD9+v4GJOyRUb83TrkAAAAASUVORK5CYII=",ho="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACkAAAAoCAYAAABjPNNTAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHcSURBVHgB7ZftTcMwEIavEv/JCB6hG5ANYASP0A3aDegGgQlgg8AEZQN3g47wYquu5Prbdb5+5JEsRJq7vH7du1yJVlamB0ArVycXpyUhBTVy7eW64J4DzYkWxuXqEaanOdDH+e5xzQenqdCu7RKuXeAKZzQ22rUvxF3r5TrojQjj+jeNBcJFYLt2VJuwNmTyRkOihbWIH+fNNXXsjSdHZ9wnaCi0sD3Srqnj3EbyMCumoxqQVwQ319rMnNyKZfQIyGsdJ+1aU5hbmJsriTWL4BQR5hRB4TO2Vj5eEsyQbh3eIigU2ZkbLg0+BsQ97FrgOcLIHS2YJ8+1v8C9r3I9y4TnzWZzpjqBXP5hxqVPKkUfp4gceVfjKu47RF1vxLVFxIpHoHAYgNsbi+JjiVUb+kiI7ZDR5+B+55MxpWKZfoiIiGWJHGZs3RsmQzAPiD1GYuxhoqUpgDtgqB7bBO7tTNdpSjwOHTz3sFzHxxTam256Ph9mmKgUGf2+oWaYGJKQEM8GOM0FruOa4ybmLBiPSDXiXezisK6N2xtzwP0bRYnbWe5uaW7gDrMmJ1oKCP8O4rQU4FbzDUZLAu6IN3/B2MB9w7T0IE80EvInhpo/1aDxItev/P+HVlbS/AMeXns9r0YtdAAAAABJRU5ErkJggg==",Ls="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADQAAAA0CAYAAADFeBvrAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFFSURBVHgB7dnRrcIwDIVh607ACIzCKN3kZlNGMY1oxBGioXVix0H+pT4hxfkAQasQRVEURb8UM1/Wa1mvGw0qz972cKWW8gLrdedX/2Rcngnz814uJG17V94zQ71hSgtJ2z5qHoHawXDzV39dIFmjKphEPbJEqWNgkDrKDAMD1VDmGBjcHTUMAxvohhqOgY00o9xgYENilDsMbOw0yi2mdAblHlM6gpoGU6qhpsOUKqj5MKWDqEQz9QWVSKk/0ouFr/mr8gOAmT/OizqImQNVwSQe8OTbFB/4n5kGxSf+NN2jWHAH4BbFDbcz7lDc4d7MDaoHBtYai+qJgTXHoDQwsLYtShMDM2xQFhiYpYuyxMBMHRTvH6ckUq6CupE0/nzglcioHdRC0vh5vnofgYE9ICrv5Uot8fOc1cuhsfx8NYqiKPLYAySVDZ5lOWmRAAAAAElFTkSuQmCC",fo="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAYAAADhAJiYAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAQzSURBVHgBxVjNVttGFL53ZOOYhNQNFEROz6l4gri77mqeIHTZVc0TuFl2Bey6KzyB7SeIs+zKzq670CdAPSdpFNpQNRCIbTSTe0caRyKybGOUfOfI0vx/c+fO/THCNVGxHacAl44C5cTrEdC9hILre64L1wBO29FxnMqbi36dBjxUCqsAqjJhah9R9UCKJ0Ur6Hme58JNEFqxv64FMmhQxy2YAyS5VlHIvUnExhJiiZyeD36jI6mnjHJBYY++/kIh3XiTkoKP8QEiVEGBAzMSSyXEUpFSPk4eC/oA8kAKaPlTir9i245QyJJtXCHnCqG2//W83kRC99bWefB+rIdLurD32vNaMAeWbbtOureTJIa7J8d/78X7WQkyq/d36PXrqDvCgSyXfvzv+fM/YE5cnJ0dloorbRRBmTb5XVRdK99Z+v/i7dnH8/Mx3VtdVx8eexdyAs8dX2vFtmumTUvIprO+lIp0BiKdUXsnx94u5ASSSK98+w6pC9b0agpra1+ttH3ff6cJLSzebcDoWmPr5PjlI8gZmtTi0pfR8VX6Q9m/eHvaw0rFqYiF/jOqdFiBJarNaW/RvAjXHhxFt9m/u1jaEFZxuKXJhOh8KjIM33fJlMCB4ceeoACoHpoOJJ2DrAlY1wZSPJvsNq4A4dHJq5f7aU1SyJaQuKMLCh8IZaRDxzVJOkSmPjOZED+Na4jWdMOSqhXopxryMZXjwbuxFH4Ps0KqdlYz2TtXhQaTIogZEO1mE3KECH0UyWlkgz4rfJaQS0+qZ76Kayi1axXFD/+8eHGY1SmKr7QeCyo9jeorcROehmsotRMMVWYcxS5rNKfCw4KwVEdKbERM+fr1xg2Wg4V9MmQOef9vYArQnv+UKFuZfaQc3UAhZFuHHxRyHJkjozhlMy1OyQOhCuCRLtBxka3aEPqb4h3TiaTV5GgRcgavQWS6pmw4aEIcfJEt6Jm+b8773TxJ6YSB1oCRy8KWCQCF6RSg2taxcogqD2CRwg0jRqYaciGHTjG2aU+EsFEM3I2ZAHeBdMq7IYcbxupBE2Lu6mp0IeIDuIE7xCTlDBU2YU4wkeW19S6R6WaRCatToCUlxWPj56RQG7OEJXwsZ+8uq5S5sN/bMvMYkDnoqMXStu/q8COBVF/Gi5MpaNNIPZEVWDV6tdIW5hiGwwa6JU60GF8KJ21ebYkxPf3JJKRJSNELUIY7wmT+HirmoEHPz6B9IKWGCsaCbzBf62ns21hCQyvwKXAyxZFlXl69v0VEmuNdiHbWLj2HlNU+DW7d6qQdzVjyWY2covBbITz5olyqn573myqR43/IZrl0E+HvVITCXF5/OaYtSzHnwYQAjSVAR5NMf33axTalwB3IASK7WSV2z8pJVvXb1zmRYUwjoeib/nB4lV82a2BlNZaWbv9O/+cghSS/EJkWfAK8B+WMERo0ZZ72AAAAAElFTkSuQmCC",_o="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAAAXNSR0IArs4c6QAAAVxJREFUOE/tlDFPAkEQhd9sAVGwtcDEBBsTLC0pxOLmaKz8A4TKaKw0odROjXbGRBuMP8DYsnuFWlDaYqw0FpjQChoodsyeYEQIKli67WS+mXkzbwl/9OgrR9LpCcRiRQDzIOqOiwiAGzQaeSqXnz/n9oKY10GUA7AJa21XIaUUgH2InJExhz0gSaUimJwcDwORSAFE02g21/pOHY0eQeQRrdZeGK/VXqhSaYUdie9fAsgMKdcVab34DnIdJRJjQ4Gq1dePjjoAYV6FyBMFwYUwJwEUyJiVsBjzCZTaoVLpQZiXQDRFWh93crvEFt8vuvnJmG3JZjMQOSWtHdCNfw9rcxQE18K8BSBJxrilhO8fNIpGnrcApZzYM6OJ7TwXjzNpfd4GLaNeN85jv9raoOP8CegAwBys3R145UTOj7ek9Ub/O/K8BJRyX8jsN3a5g7V5CoJqX9BQXmsnvQFRSQUiLrP8aAAAAABJRU5ErkJggg==",Ni="data:image/png;base64,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",zs="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAsCAYAAAAehFoBAAAAAXNSR0IArs4c6QAAAUFJREFUWEft2T1KA1EUhuEnguIK7LUSS0G08qeyVMEd2LoDBW3cgdi5A8EswZ9SsNVC7OMKREGdEyYoIuEmFnHwXBhImHPvvHNy7vlm8rV8jmnsYwGzGP9ybhQfX3GPm4rlCI8B0apJdnCMyVGQFVzzGbs4DeBFXNcZfcMZbvFSsNCgIeuII8YJHvosMIF5bGMMkfHlAG5jo564VX8fFKQ0/hAHdfAaLgombuK8jmsHcKe62yncYa5ggd+EDAMc1wu22FdPAfxeE1xWd7z6G5qCucMCxy+x0tt0Cdwn05nhrOFv5ZElkSWRJZFK11+fs0tkl8gukV0iu0Q3A/nGUfA61w1J4UjhSOFI4UjhSOEo1YwUDvl36w/F8j+fJZpkGXTCMgjDI4yPGI0wZZZw1STbKzLbKGOxt5lnKvNu769btx//5OGqrnWpNQAAAABJRU5ErkJggg==",qi={name:"IndexView",components:{ArrowDownBold:ma,ArrowUpBold:ua,CopyDocument:da,RefreshRight:ra,Delete:It,DocPreview:ci,Download:ca,ThinkingBox:Ln},setup(){const s=Ce(),o=De(),l=n(!1),t=n("message"),d=n(""),m=n(!1),u=n(null),r=n(null),v=n(""),k=n("user"),h=n(null),g=n([]),x=n(!1),y=n(!1),w=n(!1),T=n(""),A=n(!0),C=n([]),L=n(null),B=n(null),P=n("");let b=0,M=null;const D=n("");let V=0,f=null;const I=n(!1),U=n([]),J=n(null),O=n(o.query.qid),Y=n(null),R=n(null),j=n(null),ee=n(1),K="开始参考资料分析",H=n(!1),ne=n(!1),re=n(""),G=n(!1),ve=n(!0),Z=n(""),se=n([]),$=n(null),ae=n(!1),te=n("永久删除对话"),de=n("本条会话数据将被永久删除，不可恢复及撤销。确定要删除吗？"),he=n(null),Oe=n(),Ge=n(!1),Nt=n(""),ft=n(""),kt=n(""),_t=n(""),to=n(!1),rt=n(!1),Vt=n(null),xt=n(!0),{serverConfig:gt}=lt(),qt=gt.VITE_API_BASE_URL,{processText:So,getThinkingContent:Io,hasThinkingContent:oo,isThinkingCompleted:so}=zn(),At=()=>{if(!Oe.value)return E({message:"还没有可下载的文件",type:"warning"});const q=document.createElement("a"),pe=Oe.value.file_url,fe=pe.split("."),xe=fe.length>1?fe[fe.length-1]:"";q.href=qt+pe,q.download=Oe.value.name+"."+xe,document.body.appendChild(q),q.click(),document.body.removeChild(q)},Bo=q=>{Oe.value=q,Ge.value=!0},Et=()=>{if(u.value){clearTimeout(u.value),u.value=null;return}d.value=="left"?d.value="":d.value==""&&(d.value="left")},To=()=>{u.value=setTimeout(()=>{u.value=null},0),d.value=="left"?(d.value="right",m.value=!0):d.value=="right"&&(d.value="",m.value=!1)},no=q=>{r.value=q},ao=()=>{r.value=null},Zo=async()=>{try{const q=await yn();q.data.error==="0"&&(v.value=q.data.nickname,k.value=q.data.role,_t.value=q.data.id,h.value=q.data.department,g.value=q.data.permissions,x.value=ze(g.value,["create_aiserver","del_aiserver","manage_aiserver","create_docbase","manage_docbase","manage_siteuser","create_tool","manage_tool"]))}catch{localStorage.removeItem("token"),s.push({name:"Login"}),console.log("获取用户信息失败")}},$o=()=>{te.value="退出登录",de.value="退出登录后，数据保留。确定要退出吗？",$.value="logout",ae.value=!0,l.value=!1};Pe(T,q=>{q!==""?y.value=!0:y.value=!1});const Yo=q=>{L.value&&q.deltaY<0&&(xt.value=!1)},dt=()=>{pt(()=>{const q=L.value;q&&(q.addEventListener("wheel",Yo),xt.value&&(q.scrollTop=q.scrollHeight)),Vt.value?Vt.value.focus():console.error("inputTxtRef is null")})};Pe(C,async()=>{for(let q=C.value.length-1;q>=0;q--){const pe=C.value[q].attachments_data;if(pe&&pe.length>0){Oe.value=pe[pe.length-1],pe[pe.length-1].file_url.endsWith(".docx")&&(Ge.value=!0);break}}await pt(),document.querySelectorAll("pre code").forEach(q=>{q.dataset.highlighted||(Ke.highlightElement(q),q.dataset.highlighted="yes")})});const Kt=()=>{let q=`Hello，${v.value}`;b<q.length?(P.value+=q[b],b++,setTimeout(Kt,100)):(Mo(),clearInterval(M))},Mo=()=>{let q="";R.value?q=R.value:q="很高兴见到你，开始探索吧！",V<q.length?(D.value+=q[V],V++,setTimeout(Mo,100)):clearInterval(f)},jo=()=>{I.value=!I.value},lo=async()=>{try{J.value=Re.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});const q=await Ga();if(U.value=q.data,J.value.close(),o.query.qid){const pe=parseInt(o.query.qid),fe=U.value.findIndex(xe=>xe.id===pe);fe!==-1?(O.value=U.value[fe].id,Y.value=U.value[fe].title,R.value=U.value[fe].opening_statement):q.data.length>0&&(O.value=q.data[0].id,Y.value=q.data[0].title,R.value=q.data[0].opening_statement)}else q.data.length>0&&(O.value=q.data[0].id,Y.value=q.data[0].title,R.value=q.data[0].opening_statement)}finally{J.value.close()}},St=(q,pe,fe)=>{O.value=q,Y.value=pe,R.value=fe,C.value=[],j.value=null,I.value=!I.value,b=0,P.value="",V=0,D.value="",Kt(),Ge.value=!1},Jo=()=>{C.value=[],j.value=null,t.value="message",b=0,P.value="",V=0,D.value="",Kt(),Ge.value=!1};Xe.setOptions({highlight:function(q,pe){return Ke.getLanguage(pe)?Ke.highlight(q,{language:pe}).value:Ke.highlightAuto(q).value}});const Go=q=>Xe(q),Wo=()=>{let q=0;re.value="";const pe=setInterval(()=>{q<K.length?(re.value+=K[q],q++):(clearInterval(pe),G.value=!0)},100)},Xo=q=>{if(q.includes("}{")){const pe=q.split("}{");return pe.map((xe,Ve)=>{let qe;return Ve===0?qe=xe+"}":Ve===pe.length-1?qe="{"+xe:qe="{"+xe+"}",qe}).map(xe=>JSON.parse(xe))}else return[JSON.parse(q)]},es=q=>{try{const fe=Xo(q).map(xe=>{try{const Ve=xe;if(Ve.error)return{topic_id:null,content:[{content:""}],taskmessage_id:"",error:Ve.error};if(Ve.topic_id){const qe=Ve.topic_id;j.value=qe;const yt=Ve.content||"",Zt=Ve.taskmessage_id;return{topic_id:qe,content:[{content:yt}],taskmessage_id:Zt}}else return Ve}catch{debugger}});return fe.length===1&&fe[0].error||fe.length===1&&typeof fe[0].topic_id=="number"?fe[0]:{topic_id:null,content:fe}}catch{return{topic_id:null,content:[{content:""}],taskmessage_id:""}}},Do=q=>{if(q.key==="Enter"){if(T.value.trim()===""){q.preventDefault();return}q.shiftKey||q.ctrlKey?(T.value+=`
`,q.preventDefault()):Ht()}},Ht=async()=>{if(!O.value){E.error("请先选择应用");return}xt.value=!0;const q=T.value,pe={role:"user",content:q,taskmessage_id:""};C.value.push(pe),Z.value="";let fe={role:"assistant",content:"",taskmessage_id:"",thinkingContent:"",thinkingCompleted:!1};C.value.push(fe),dt(),ne.value=!1,T.value="",H.value=!0,Wo(),ve.value=!1,A.value=!1,rt.value=!0,w.value=!1;try{const xe=await bn(q,ee.value,O.value,j.value);if(ee.value==1)if(xe&&xe.getReader){const Ve=xe.getReader(),qe=new TextDecoder;let yt=!1;(async()=>{for(;!yt;){const{value:nt,done:ps}=await Ve.read();if(yt=ps,nt){const co=qe.decode(nt,{stream:!0}),{content:Eo,taskmessage_id:Lo,error:F}=es(co);if(F=="1"){E.error("应用不存在"),H.value=!1,G.value=!1;return}let ce="";Eo.map(Se=>{if(Se.type==Ms.StartTool)H.value=!0,ne.value=!0,G.value=!0,re.value=Se.content;else if(Se.content!==""){H.value&&(H.value=!1),G.value&&(G.value=!1);const Te=fe.taskmessage_id,Ue=So(Se.content||"",Te);ce+=Ue.normalText,oo(Te)&&(fe.thinkingContent=Io(Te),fe.thinkingCompleted=so(Te))}}),Z.value+=ce;try{fe.content=Z.value,Lo&&(fe.taskmessage_id=Lo,pe.taskmessage_id=Lo),C.value=[...C.value],dt()}catch{}}}if(H.value=!1,G.value=!1,A.value=!0,Vt.value.focus(),dt(),fe.taskmessage_id){const nt=await Fl(fe.taskmessage_id);nt.data.error!=="1"&&(fe.attachments_data=nt.data)}C.value=[...C.value],ve.value=!0,rt.value=!1})().catch(nt=>{H.value=!1,G.value=!1,A.value=!0,C.value.pop(),C.value.push({role:"assistant",content:"当前请求网络可能有问题，请重新发起对话"}),dt()})}else throw new Error("Invalid response format for stream mode");else if(A.value=!0,xe.data.error=="1")E.error("应用不存在");else if(xe.data.error=="0"){H.value=!1,rt.value=!1,G.value=!1;const{topic_id:Ve,content:qe,taskmessage_id:yt}=xe.data;C.value.push({role:"assistant",content:qe,taskmessage_id:yt}),j.value=Ve,pe.taskmessage_id=yt,dt(),ve.value=!0}}catch{H.value=!1,G.value=!1,A.value=!0,rt.value=!1,C.value.length>0&&C.value[C.value.length-1].role==="assistant"&&C.value.pop(),C.value.push({role:"assistant",content:"当前请求网络可能有问题，请重新发起对话"}),dt()}},ts=q=>{try{var pe=/([\n\r])+/g;const fe=q.replace(pe,`
`),xe=document.createElement("textarea");xe.value=fe,document.body.appendChild(xe),xe.select(),document.execCommand("copy"),document.body.removeChild(xe),E.success("内容已复制")}catch{E.error("复制失败")}},os=async(q,pe)=>{(await en(O.value,j.value,q)).data.error=="0"?(C.value=C.value.filter(xe=>xe.taskmessage_id!==q),T.value=pe,Ht()):(T.value=pe,Ht())},ss=async q=>{const pe=await en(O.value,j.value,q);pe.data.error=="0"?C.value=C.value.filter(fe=>fe.taskmessage_id!==q):pe.data.error=="1"?E.error("当前应用不存在，删除失败"):pe.data.error=="2"?E.error("当前对话不存在，删除失败"):pe.data.error=="3"&&E.error("当前聊天内容不存在，删除失败")},Vo=()=>{O.value?(t.value="topic",ut()):E.error("应用不存在,暂无历史对话")},ut=async()=>{J.value=Re.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});const q=await Wa(O.value);se.value=q.data,J.value.close()},ns=q=>{if(q){let pe=q.split(" ")[0],fe=q.split(" ")[1],xe=pe.split("-")[1],Ve=pe.split("-")[2];return`${xe}-${Ve} ${fe}`}else return""},as=q=>{j.value=q,t.value="message",is()},ls=()=>{Oe.value=void 0,Ge.value=!1},is=async()=>{J.value=Re.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});const q=await Xa(j.value);ls(),C.value=q.data,J.value.close(),xt.value=!0,dt()},cs=q=>{he.value=q,$.value="del",ae.value=!0},rs=()=>{te.value="永久删除对话",de.value="本条会话数据将被永久删除，不可恢复及撤销。确定要删除吗？",ae.value=!1,he.value=null,$.value=null},ds=async()=>{if(ae.value=!1,$.value=="del"){const q=await Tl(O.value,he.value);q.data.error=="0"?(E.success("删除成功"),ut()):q.data.message?E.error("删除失败，"+q.data.message):E.error("删除失败")}else $.value=="logout"&&(localStorage.removeItem("token"),s.push({name:"Login"}))},us=()=>{l.value=!l.value},Qt=n(!0);let Ot=0;const io=()=>{const q=L.value.scrollTop;Math.abs(q-Ot)>1&&(Ot==0?Qt.value=!0:q>Ot?Qt.value=!1:Qt.value=!0),Ot=q};ge(()=>{Zo(),setTimeout(()=>{Kt()},1e3),lo(),L.value&&L.value.addEventListener("scroll",io)}),ks(()=>{L.value&&L.value.removeEventListener("scroll",io)});const ms=Ze(async()=>{if(ft.value!=kt.value)E.error("两次密码不一致");else{const q=await Vn(_t.value,Nt.value,ft.value);q.data.message?E.error(q.data.message):q.data.error=="0"&&(E.success({message:"修改成功",duration:500}),setTimeout(()=>{localStorage.removeItem("token"),s.push({name:"Login"})},500))}});return{direction:d,navSlide:To,isDialogVisible:l,isSlide:m,changeDirection:Et,isButtonClicked:r,handleMouseDown:no,handleMouseUp:ao,nickname:v,getFirstLetter:He,siteuserRole:k,siteuserDepartment:h,canPerformAction:x,logout:$o,canSendMessage:y,userContent:T,canWriteIpt:A,taskmesssageList:C,scrollToBottom:dt,scrollContainer:L,innerRef:B,displayedText1:P,displayedText2:D,isShowQueryapp:I,showQueryappList:jo,queryAppList:U,selectedQueryAppId:O,selectedQueryAppTitle:Y,selectQueryAppOpening:R,selectQueryAppItem:St,createNewMessage:Jo,selectedTopicId:j,renderMarkdown:Go,showLoading:H,showEllipsis:G,displayedTextLoading:re,handleKeyDown:Do,sendTaskmessageHandler:Ht,showOperate:ve,copyContent:ts,againSend:os,delMessage:ss,showDom:t,showTopic:Vo,topicList:se,formattedDateTime:ns,selectTopicItem:as,delTopic:cs,handleClose:rs,showModal:ae,modalMessage:de,handleConfirm:ds,modalTitle:te,showSiteuserInformation:us,handleScroll:io,isCommonTopVisible:Qt,showTool:ne,curPreview:Oe,showPreview:Ge,attachmentClick:Bo,baseUrl:qt,oldpassword:Nt,newpassword:ft,newpassword1:kt,modifyPassword:ms,showModifyPasswordModal:to,closeModal:()=>{to.value=!1,Nt.value="",ft.value="",kt.value=""},isRendering:rt,downloadFile:At,inputTxtRef:Vt,focusIpt:w}}},ke=s=>(be("data-v-33f17dc9"),s=s(),we(),s),Ki={class:"common-layout",style:{position:"relative"}},Hi={key:0,class:"information-div"},Qi={class:"information-top font14"},Oi={class:"siteuser-headimg"},Zi={class:"overflow-one"},$i={class:"information-center"},Yi={key:0,class:"information-item font12"},ji=ke(()=>e("div",null,"部门",-1)),Ji={class:"overflow-one"},Gi={class:"information-item font12"},Wi=ke(()=>e("div",null,"职位",-1)),Xi={class:"overflow-one"},ec=ke(()=>e("div",{class:"information-item font12"},[e("div",null,"部门"),e("div",{class:"overflow-one"},"暂无部门")],-1)),tc=ke(()=>e("div",{class:"information-item font12"},[e("div",null,"职位"),e("div",{class:"overflow-one"},"暂无岗位")],-1)),oc={class:"information-item information-item1 font12"},sc=ke(()=>e("div",null,"权限",-1)),nc={key:0},ac=ke(()=>e("img",{src:Ds,alt:""},null,-1)),lc={key:1},ic={class:"information-item information-item1 font12"},cc=ke(()=>e("div",null,"后台",-1)),rc={key:1},dc={class:"information-bottom font12"},uc={class:"layout-nav"},mc=ke(()=>e("div",{class:"layout-nav-item layout-nav-item1"},[e("img",{src:Tt,alt:""})],-1)),pc=ke(()=>e("img",{src:Vs,alt:"",style:{height:"1px",width:"50px","object-fit":"contain","object-position":"center",position:"absolute",top:"75px",left:"15px"}},null,-1)),vc={class:"layout-nav-item layout-nav-item2"},hc=ke(()=>e("img",{src:Ui,alt:""},null,-1)),fc=[hc],_c=ke(()=>e("img",{src:Pi,alt:""},null,-1)),gc=[_c],Ac=ke(()=>e("img",{src:Vs,alt:"",style:{height:"1px",width:"50px","object-fit":"contain","object-position":"center",position:"absolute",bottom:"75px",left:"15px"}},null,-1)),yc={class:"layout-nav-item layout-nav-item3"},bc={class:"el-main-left"},wc={class:"container container-message"},Cc=ke(()=>e("div",{class:"container-top-mengban"},null,-1)),kc={key:0,class:"container-queryapp-div"},xc={class:"font18 container-queryapp-nav-title"},Sc={class:"container-queryapp-list overflow-y"},Ic=ke(()=>e("div",{class:"font14"},"我的智能体",-1)),Bc=["onClick"],Tc=ke(()=>e("div",{class:"container-queryapp-item-img"},null,-1)),Mc={class:"container-queryapp-item-title font14"},Dc={class:"container-center"},Vc={class:"conatiner-message-div flex w-full h-full flex-col justify-between"},Ec={ref:"innerRef"},Lc={key:0,class:"opening-statement-div"},zc={key:1,style:{"margin-top":"90px"}},Fc={class:"chat-item chat-item-opening"},Uc=ke(()=>e("img",{src:Tt,alt:"",class:"chat-item-ai-headimg"},null,-1)),Pc={class:"chat-item-content opening-statement"},Rc={class:"opening-statement-title font18"},Nc=["innerHTML"],qc={key:0,class:W(["chat-item","taskmessage-user"])},Kc={class:"siteuser-headimg font16"},Hc={class:"chat-item-content taskmessage-user-content"},Qc=["innerHTML"],Oc={key:0,class:"chat-item-top-operate"},Zc={key:1,class:W(["chat-item","taskmessage-ai"])},$c={class:"chat-item-ai-headimg-div"},Yc={key:0,src:Es,alt:"",style:{width:"25px",height:"25px"}},jc={key:1,src:Tt,alt:"",class:"chat-item-ai-headimg"},Jc={class:"chat-item-content taskmessage-ai-content"},Gc=["innerHTML"],Wc={key:1,class:"font14",style:{}},Xc={key:0,src:Jt,alt:"",style:{width:"18px",height:"18px"}},er={key:2,class:"flex gap-3 flex-wrap",style:{"margin-top":"10px"}},tr=["onClick"],or=ke(()=>e("div",{class:"attachment-box-left"},[e("img",{src:Ri,class:"w-[25px] h-[25px]"})],-1)),sr={class:"attachment-box-right"},nr={class:"attachment-title"},ar=ke(()=>e("div",{class:"attachment-desc"}," 点击打开文档 ",-1)),lr={key:3,class:"chat-item-top-operate-ai"},ir={key:0,class:"taskmessage-add"},cr=["disabled"],rr=ke(()=>e("img",{src:ho,alt:""},null,-1)),dr=[rr],ur={key:1},mr={class:"flex flex-1 items-center"},pr={class:"flex items-end"},vr={class:"h-[52px] flex items-center"},hr=["disabled"],fr=ke(()=>e("img",{src:ho,alt:""},null,-1)),_r=[fr],gr=ke(()=>e("div",{class:"conatiner-tips-div font14"}," 内容由AI生成，无法确保真实准确，仅供参考，请阅读并遵守《AiALIGN用户协议》 ",-1)),Ar={class:"container container-topic"},yr=ke(()=>e("img",{src:Ls,alt:""},null,-1)),br=[yr],wr=ke(()=>e("div",{class:"container-topic-top"},[e("div",{class:"font24"},"历史对话")],-1)),Cr={class:"container-topic-list overflow-y"},kr=["onClick"],xr={class:"topic-item-left"},Sr=ke(()=>e("img",{src:fo,alt:"",class:"topic-item-img"},null,-1)),Ir={class:"topic-item-title overflow-one"},Br={class:"topic-item-time"},Tr=["onClick"],Mr=ke(()=>e("button",null,[e("img",{src:_o,alt:""})],-1)),Dr=[Mr],Vr={class:"el-main-right-container relative overflow-hidden",style:{"min-width":"400px"}},Er={style:{position:"absolute",top:"0",left:"0",padding:"20px 20px 0"},class:"font18 font-zhongcu flex flex-row justify-between align-center items-center w-full"},Lr=ke(()=>e("div",null,"工作台",-1)),zr={class:"cursor-pointer hover:bg-[#EFEFEF] p-[4px] rounded-[5px] flex items-center"},Fr={key:0,class:"h-full overflow-y-scroll hidden-scrollbar"},Ur={key:1,class:"w-full flex justify-center mt-[188px]"},Pr=ke(()=>e("div",{class:"w-[144px] h-[110px] flex flex-col items-center justify-center"},[e("img",{src:Ni,class:"w-[85px] h-[85px]"}),e("div",{class:"font18 font-zhongcu",style:{color:"#666666",width:"10em"}}," 本轮会话暂无工作输出 ")],-1)),Rr=[Pr],Nr=ke(()=>e("img",{src:zs,class:"w-[22px] h-[22px]"},null,-1)),qr=[Nr],Kr={key:0,class:"new-modal-overlay"},Hr={class:"new-modal-container"},Qr=ke(()=>e("div",{class:"new-modal-top"},[e("div",{class:"font18 font-zhongcu"},"修改密码")],-1)),Or={class:"new-modal-center"},Zr={class:"new-modal-center-item"},$r=ke(()=>e("label",{class:"font16 font-zhongcu"},"旧密码",-1)),Yr={class:"new-modal-center-item"},jr=ke(()=>e("label",{class:"font16 font-zhongcu"},"新密码",-1)),Jr={class:"new-modal-center-item"},Gr=ke(()=>e("label",{class:"font16 font-zhongcu"},"确认新密码",-1)),Wr={class:"new-modal-bottom"},Xr=ke(()=>e("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu"}," 提交 ",-1));function ed(s,o,l,t,d,m){const u=_("router-link"),r=_("el-tooltip"),v=_("el-aside"),k=_("ArrowDownBold"),h=_("el-icon"),g=_("ArrowUpBold"),x=_("CopyDocument"),y=_("el-button"),w=_("RefreshRight"),T=_("Delete"),A=_("ThinkingBox"),C=_("el-input"),L=_("Download"),B=_("DocPreview"),P=_("el-main"),b=_("el-container"),M=_("newComfirmsModal");return a(),c(N,null,[e("div",Ki,[i(dn,{name:"fade-move"},{default:p(()=>[t.isDialogVisible?(a(),c("div",Hi,[e("div",Qi,[e("div",Oi,S(t.getFirstLetter(t.nickname)),1),e("div",Zi,S(t.nickname),1)]),e("div",$i,[t.siteuserDepartment?(a(),c(N,{key:0},[t.siteuserDepartment.name?(a(),c("div",Yi,[ji,e("div",Ji,S(t.siteuserDepartment.name),1)])):z("",!0),e("div",Gi,[Wi,e("div",Xi,S(t.siteuserDepartment.position_name),1)])],64)):(a(),c(N,{key:1},[ec,tc],64)),e("div",oc,[sc,t.siteuserRole=="super"?(a(),c("div",nc,[Q(" 超级管理员 "),ac])):(a(),c("div",lc,"普通用户"))]),e("div",ic,[cc,e("div",null,[t.canPerformAction?(a(),X(u,{key:0,to:{name:"QueryAppList"},class:"link"},{default:p(()=>[Q("智能体管理")]),_:1})):(a(),c("div",rc,"暂无权限"))])])]),e("div",dc,[e("button",{type:"button",class:"logout-btn",onClick:o[0]||(o[0]=(...D)=>t.logout&&t.logout(...D))}," 退出登录 "),e("button",{class:"link-edit font10",style:{border:"none",background:"none"},onClick:o[1]||(o[1]=D=>{t.isDialogVisible=!1,t.showModifyPasswordModal=!0})}," 修改密码 ")])])):z("",!0)]),_:1}),i(b,{onClick:o[22]||(o[22]=D=>{t.isShowQueryapp=!1,t.isDialogVisible=!1})},{default:p(()=>[i(v,{class:W(t.isSlide?"el-slide-active1":"el-slide-active2")},{default:p(()=>[e("div",uc,[mc,pc,e("div",vc,[i(r,{class:"box-item",effect:"dark",content:"新建对话",placement:"right"},{default:p(()=>[e("button",{class:"layout-nav-item2-item",onMousedown:o[2]||(o[2]=D=>t.handleMouseDown("1")),onMouseup:o[3]||(o[3]=(...D)=>t.handleMouseUp&&t.handleMouseUp(...D)),onClick:o[4]||(o[4]=(...D)=>t.createNewMessage&&t.createNewMessage(...D))},fc,32)]),_:1}),i(r,{class:"box-item",effect:"dark",content:"历史对话",placement:"right"},{default:p(()=>[e("button",{class:"layout-nav-item2-item",onMousedown:o[5]||(o[5]=D=>t.handleMouseDown("2")),onMouseup:o[6]||(o[6]=(...D)=>t.handleMouseUp&&t.handleMouseUp(...D)),onClick:o[7]||(o[7]=(...D)=>t.showTopic&&t.showTopic(...D))},gc,32)]),_:1})]),Ac,e("div",yc,[e("div",{class:"siteuser-headimg font16",onClick:o[8]||(o[8]=_e((...D)=>t.showSiteuserInformation&&t.showSiteuserInformation(...D),["stop"]))},S(t.getFirstLetter(t.nickname)),1)])]),e("div",{class:"layout-nav-hot",onMouseenter:o[9]||(o[9]=(...D)=>t.changeDirection&&t.changeDirection(...D)),onMouseleave:o[10]||(o[10]=(...D)=>t.changeDirection&&t.changeDirection(...D)),onClick:o[11]||(o[11]=(...D)=>t.navSlide&&t.navSlide(...D))},[e("div",{class:W(["layout-nav-line",t.direction==="left"?"left":t.direction==="right"?"right":""])},null,2),e("div",{class:W(["layout-nav-line",t.direction==="left"?"left":t.direction==="right"?"right":""])},null,2)],32)]),_:1},8,["class"]),i(P,null,{default:p(()=>[e("div",bc,[me(e("div",wc,[Cc,t.queryAppList.length>0?me((a(),c("div",kc,[e("div",{class:"container-queryapp-nav",onClick:o[12]||(o[12]=_e((...D)=>t.showQueryappList&&t.showQueryappList(...D),["stop"]))},[e("div",xc,S(t.selectedQueryAppTitle),1),t.isShowQueryapp?(a(),X(h,{key:1,class:"flex-shrink-0"},{default:p(()=>[i(g)]),_:1})):(a(),X(h,{key:0,class:"flex-shrink-0"},{default:p(()=>[i(k)]),_:1}))]),me(e("div",Sc,[Ic,(a(!0),c(N,null,le(t.queryAppList,D=>(a(),c("div",{class:"container-queryapp-item",key:D.id,onClick:V=>t.selectQueryAppItem(D.id,D.title,D.opening_statement)},[Tc,e("div",Mc,S(D.title),1)],8,Bc))),128))],512),[[bt,t.isShowQueryapp]])],512)),[[bt,t.isCommonTopVisible]]):z("",!0),e("div",Dc,[e("div",Vc,[e("div",{ref:"scrollContainer",class:"taskmessage-div overflow-y",onScroll:o[13]||(o[13]=(...D)=>t.handleScroll&&t.handleScroll(...D))},[e("div",Ec,[t.taskmesssageList.length<=0?(a(),c("div",Lc,[e("div",null,S(t.displayedText1),1),e("div",null,S(t.displayedText2),1)])):(a(),c("div",zc,[e("div",Fc,[Uc,e("div",Pc,[e("div",Rc," 你好，我是"+S(t.selectedQueryAppTitle),1),t.selectQueryAppOpening?(a(),c("div",{key:0,class:"opening-statement-desc font14",innerHTML:t.renderMarkdown(t.selectQueryAppOpening)},null,8,Nc)):z("",!0)])]),(a(!0),c(N,null,le(t.taskmesssageList,(D,V)=>(a(),c(N,{key:V},[D.role==="user"?(a(),c("div",qc,[e("div",Kc,S(t.getFirstLetter(t.nickname)),1),e("div",Hc,[e("div",{class:"font14",innerHTML:t.renderMarkdown(D.content)},null,8,Qc),V!==t.taskmesssageList.length-1||t.showOperate?(a(),c("div",Oc,[i(r,{class:"box-item",effect:"dark",content:"复制",placement:"bottom"},{default:p(()=>[i(y,{onClick:f=>t.copyContent(D.content)},{default:p(()=>[i(h,{size:"18"},{default:p(()=>[i(x)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),i(r,{class:"box-item",effect:"dark",content:"重新生成",placement:"bottom"},{default:p(()=>[i(y,{onClick:f=>t.againSend(D.taskmessage_id,D.content)},{default:p(()=>[i(h,{size:"18"},{default:p(()=>[i(w)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),i(r,{class:"box-item",effect:"dark",content:"删除",placement:"bottom"},{default:p(()=>[i(y,{title:"",onClick:f=>t.delMessage(D.taskmessage_id)},{default:p(()=>[i(h,{size:"18"},{default:p(()=>[i(T)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)])):z("",!0)])])):D.role==="assistant"?(a(),c("div",Zc,[e("div",$c,[t.isRendering&&V===t.taskmesssageList.length-1?(a(),c("img",Yc)):(a(),c("img",jc))]),e("div",Jc,[D.thinkingContent?(a(),X(A,{key:0,content:D.thinkingContent,completed:D.thinkingCompleted,style:{"margin-top":"0","margin-bottom":"10px"}},null,8,["content","completed"])):z("",!0),e("div",{class:"font14",innerHTML:t.renderMarkdown(D.content)},null,8,Gc),t.showLoading&&V==t.taskmesssageList.length-1?(a(),c("div",Wc,[Q(S(t.displayedTextLoading)+" ",1),t.showEllipsis?(a(),c("img",Xc)):z("",!0)])):z("",!0),D.attachments_data?(a(),c("div",er,[(a(!0),c(N,null,le(D.attachments_data,(f,I)=>(a(),c("div",{key:I},[e("div",{class:"attachment-box",onClick:_e(U=>t.attachmentClick(f),["stop"])},[or,e("div",sr,[e("div",nr,S(f.name),1),ar])],8,tr)]))),128))])):z("",!0),V!==t.taskmesssageList.length-1||t.showOperate?(a(),c("div",lr,[i(r,{class:"box-item",effect:"dark",content:"复制",placement:"bottom"},{default:p(()=>[i(y,{title:"",onClick:f=>t.copyContent(D.content)},{default:p(()=>[i(h,{size:"18"},{default:p(()=>[i(x)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)])):z("",!0)])])):z("",!0)],64))),128))]))],512)],544),t.taskmesssageList.length===0?(a(),c("div",ir,[e("div",{class:W(t.focusIpt?"":"taskmessage-ipt-no")},[i(C,{modelValue:t.userContent,"onUpdate:modelValue":o[14]||(o[14]=D=>t.userContent=D),autosize:{minRows:3,maxRows:6},type:"textarea",placeholder:"输入问题",resize:"none",disabled:!t.canWriteIpt,maxlength:"20000",onKeydown:t.handleKeyDown,onFocus:o[15]||(o[15]=D=>t.focusIpt=!0),ref:"inputTxtRef"},null,8,["modelValue","disabled","onKeydown"]),e("button",{class:W(t.canSendMessage?"":"send-btn-no"),disabled:!t.canSendMessage,onClick:o[16]||(o[16]=(...D)=>t.sendTaskmessageHandler&&t.sendTaskmessageHandler(...D))},dr,10,cr)],2)])):(a(),c("div",ur,[e("div",{class:"chat-input-box",style:un({borderColor:t.focusIpt?"":"#d9d9d9"})},[e("div",mr,[i(C,{modelValue:t.userContent,"onUpdate:modelValue":o[17]||(o[17]=D=>t.userContent=D),autosize:{minRows:1,maxRows:6},type:"textarea",placeholder:"输入问题",resize:"none",disabled:!t.canWriteIpt,maxlength:"20000",onKeydown:t.handleKeyDown,onFocus:o[18]||(o[18]=D=>t.focusIpt=!0),ref:"inputTxtRef"},null,8,["modelValue","disabled","onKeydown"])]),e("div",pr,[e("div",vr,[e("button",{class:W(t.canSendMessage?"":"send-btn-no"),disabled:!t.canSendMessage,onClick:o[19]||(o[19]=(...D)=>t.sendTaskmessageHandler&&t.sendTaskmessageHandler(...D))},_r,10,hr)])])],4)]))])]),gr],512),[[bt,t.showDom=="message"]]),me(e("div",Ar,[e("div",{class:"container-topic-closed",onClick:o[20]||(o[20]=D=>t.showDom="message")},br),wr,e("div",Cr,[(a(!0),c(N,null,le(t.topicList,D=>(a(),c("div",{class:"topic-item font14",key:D.id,onClick:V=>t.selectTopicItem(D.id)},[e("div",xr,[Sr,e("div",Ir,S(D.title),1)]),e("div",Br,S(t.formattedDateTime(D.last_request)),1),e("div",{class:"topic-item-del",onClick:_e(V=>t.delTopic(D.id),["stop"])},Dr,8,Tr)],8,kr))),128))])],512),[[bt,t.showDom=="topic"]])]),me(e("div",{class:W(["el-main-right overflow-hidden",{open:t.showPreview}]),style:{}},[e("div",Vr,[e("div",Er,[Lr,e("div",zr,[i(h,{onClick:t.downloadFile},{default:p(()=>[i(L)]),_:1},8,["onClick"])])]),t.curPreview?(a(),c("div",Fr,[i(B,{"docx-src":`${t.baseUrl}${t.curPreview.file_url}`},null,8,["docx-src"])])):(a(),c("div",Ur,Rr))])],2),[[bt,t.showDom=="message"]]),me(e("div",{class:"fixed top-[22px] rounded-[5px] right-[16px] p-[4px] z-0 block cursor-pointer hover:bg-[#EFEFEF]",onClick:o[21]||(o[21]=()=>{t.showPreview=!t.showPreview})},qr,512),[[bt,t.showDom=="message"]])]),_:1})]),_:1})]),i(M,{show:t.showModal,title:t.modalTitle,message:t.modalMessage,onClose:t.handleClose,onConfirm:t.handleConfirm},null,8,["show","title","message","onClose","onConfirm"]),t.showModifyPasswordModal?(a(),c("div",Kr,[e("div",Hr,[Qr,e("form",{onSubmit:o[27]||(o[27]=_e((...D)=>t.modifyPassword&&t.modifyPassword(...D),["prevent"]))},[e("div",Or,[e("div",Zr,[$r,i(C,{modelValue:t.oldpassword,"onUpdate:modelValue":o[23]||(o[23]=D=>t.oldpassword=D),type:"password",placeholder:"旧密码",class:"field-ipt font14 font-zhongcu","show-password":"",required:""},null,8,["modelValue"])]),e("div",Yr,[jr,i(C,{modelValue:t.newpassword,"onUpdate:modelValue":o[24]||(o[24]=D=>t.newpassword=D),type:"password",placeholder:"新密码",class:"field-ipt font14 font-zhongcu","show-password":"",required:""},null,8,["modelValue"])]),e("div",Jr,[Gr,i(C,{modelValue:t.newpassword1,"onUpdate:modelValue":o[25]||(o[25]=D=>t.newpassword1=D),type:"password",placeholder:"确认新密码",class:"field-ipt font14 font-zhongcu","show-password":"",required:""},null,8,["modelValue"])])]),e("div",Wr,[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu",onClick:o[26]||(o[26]=(...D)=>t.closeModal&&t.closeModal(...D))}," 取消 "),Xr])],32)])])):z("",!0)],64)}const sn=ue(qi,[["render",ed],["__scopeId","data-v-33f17dc9"]]),td="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAL4AAAABCAYAAABg6BQ9AAAAAXNSR0IArs4c6QAAAJZJREFUKFOFjMkKAkEQQ9+468Gb+P/f520O7toS6YJQFPRAyEs6U1Nr7QycgCNwAPbADth2bQBpnbQCJPXLzspiV3QL64PdMytnTdYFV67O+8zK+mJXebz7ruJ+6n9rxDYZYrPFiPUem8w6E13l+f1re2f9G7ly78SVPtYHuzu/AWWXuuhfnZXFricgPbruwA24AjNw+QEYRlAWe43P7wAAAABJRU5ErkJggg==",od="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQBAMAAADt3eJSAAAAD1BMVEUAAAAxNz0uNDsxNz0qNUCCX+8dAAAABXRSTlMA0SerGD8edVwAAAAaSURBVAjXYyARsBgwO4AZTAKMCngYCMWkAQB7wwG72fcW1AAAAABJRU5ErkJggg==",sd="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQBAMAAADt3eJSAAAAD1BMVEUAAAAxNz0uNDsxNz0qNUCCX+8dAAAABXRSTlMA0SerGD8edVwAAAAcSURBVAjXYyARsCgpOYAZzIKCBpjScCmEYtIAAHulAbtu+rskAAAAAElFTkSuQmCC",nd=ye({name:"BaseNavComponent",components:{ArrowLeftBold:$e},props:{selectedBar:{type:String,required:!0}},emits:["data-permissions","nicknameOne","nickname"],setup(s,{emit:o}){const l=n(""),t=n(""),d=Ce(),m=n(!1),u=n("user"),r=n([{key:"applications",title:"应用管理",iconClass:"sidebar-item-img sidebar-item-img1",childrenVisible:!1,children:[{key:"queryapp",title:"应用"},{key:"tool",title:"工具库"},{key:"template",title:"文件模板"}]}]),v=async()=>{try{const x=await yn();x.data.error==="0"&&(l.value=He(x.data.nickname),t.value=x.data.nickname,u.value=x.data.role,o("data-permissions",x.data.permissions),o("nicknameOne",l.value),o("nickname",t.value),m.value=ze(x.data.permissions,["manage_siteuser"]))}catch(x){localStorage.removeItem("token"),d.push({name:"Login"}),console.error("获取用户信息失败:",x)}};return ge(async()=>{await v(),m.value?(r.value.push({key:"organization",title:"组织架构",iconClass:"sidebar-item-img sidebar-item-img4",childrenVisible:!1,children:[{key:"zuzhi",title:"组织"},{key:"siteuser",title:"人员"},{key:"department",title:"部门"}]}),["queryapp","kbase","tool","template"].includes(s.selectedBar)?(r.value[0].childrenVisible=!0,r.value[1].childrenVisible=!1):(r.value[0].childrenVisible=!1,r.value[1].childrenVisible=!0)):["queryapp","kbase","tool","template"].includes(s.selectedBar)?r.value[0].childrenVisible=!0:r.value[0].childrenVisible=!1}),{sidebarItems:r,toggleChildren:x=>{const y=r.value.find(w=>w.key===x);y&&(y.childrenVisible=!y.childrenVisible,r.value.forEach(w=>{w!==y&&(w.childrenVisible=!1)}))},changeBar:x=>{const y={kbase:"KBaseList",queryapp:"QueryAppList",tool:"ToolList",template:"FileTemplateList",zuzhi:"Organization",siteuser:"SiteuserList",department:"DepartmentList"};y[x]&&d.push({name:y[x]})},nicknameOne:l,goBack:()=>{d.push({name:"Index"})},showDepartment:m,nickname:t,siteuserRole:u}}}),Fs=s=>(be("data-v-5c52cc14"),s=s(),we(),s),ad=Fs(()=>e("div",{class:"el-aside-top"},[e("img",{src:Tt,alt:"",class:"el-aside-top-left-img"}),e("div",{class:"el-aside-top-right"},[e("div",{class:"font-zhongcu font16"},"玉衡·智生"),e("div",{class:"font12"},"Agent编辑器")])],-1)),ld=Fs(()=>e("img",{src:td,alt:"",style:{"margin-top":"10px",width:"100%","margin-bottom":"30px"}},null,-1)),id={class:"el-aside-center"},cd=["onClick"],rd={class:"sidebar-item-left"},dd={key:0,src:od,alt:""},ud={key:1,src:sd,alt:""},md={class:"sidebar-children"},pd=["onClick"],vd={class:"el-aside-bottom"},hd={class:"siteuser-headimg font14 font-zhongcu"},fd={class:"el-aside-bottom-right"},_d={class:"el-aside-bottom-right-siteuser"},gd={class:"overflow-one font16 font-zhongcu"},Ad={key:0,class:"font12 el-aside-bottom-right-super"},yd=Fs(()=>e("img",{src:Ds,alt:"",style:{width:"16px"}},null,-1)),bd={key:1,class:"font12"};function wd(s,o,l,t,d,m){const u=_("ArrowLeftBold"),r=_("el-icon"),v=_("el-aside");return a(),X(v,{class:"el-aside"},{default:p(()=>[ad,ld,e("div",id,[(a(!0),c(N,null,le(s.sidebarItems,(k,h)=>(a(),c("div",{class:"sidebar-div",key:h},[e("div",{class:W(["sidebar-item font14",k.childrenVisible?"sidebar-item-selected":""]),onClick:g=>s.toggleChildren(k.key)},[e("div",rd,[e("div",{class:W(k.iconClass)},null,2),e("div",null,S(k.title),1)]),k.childrenVisible?(a(),c("img",dd)):(a(),c("img",ud))],10,cd),me(e("div",md,[(a(!0),c(N,null,le(k.children,g=>(a(),c("div",{key:g.key,class:W(["sidebar-children-item font12",s.selectedBar===g.key?"sidebar-children-item-selected":""]),onClick:_e(x=>s.changeBar(g.key),["stop"])},S(g.title),11,pd))),128))],512),[[bt,k.childrenVisible]])]))),128))]),e("div",vd,[e("div",hd,S(s.nicknameOne),1),e("div",fd,[e("div",_d,[e("div",gd,S(s.nickname),1),s.siteuserRole=="super"?(a(),c("div",Ad,[Q(" 超级管理员 "),yd])):(a(),c("div",bd,"普通用户"))]),e("div",{class:"el-aside-bottom-back",onClick:o[0]||(o[0]=(...k)=>s.goBack&&s.goBack(...k))},[i(r,{color:"#666666",size:"16"},{default:p(()=>[i(u)]),_:1})])])])]),_:1})}const Me=ue(nd,[["render",wd],["__scopeId","data-v-5c52cc14"]]),Cd={props:{breadcrumbs:{type:Array,required:!0}},setup(){return{ArrowRight:mn}}};function kd(s,o,l,t,d,m){const u=_("el-breadcrumb-item"),r=_("el-breadcrumb");return a(),X(r,{"separator-icon":t.ArrowRight,class:"font16"},{default:p(()=>[(a(!0),c(N,null,le(l.breadcrumbs,(v,k)=>(a(),c(N,{key:k},[v.path?(a(),X(u,{key:0,to:{path:v.path},class:"breadcrumb-link"},{default:p(()=>[Q(S(v.name),1)]),_:2},1032,["to"])):(a(),X(u,{key:1},{default:p(()=>[Q(S(v.name),1)]),_:2},1024))],64))),128))]),_:1},8,["separator-icon"])}const Ee=ue(Cd,[["render",kd],["__scopeId","data-v-532b22c5"]]),xd={props:{kid:{type:Number,required:!0}},emits:["showSearchModal"],setup(s,{emit:o}){const l=n([]),t=n(""),d=n(null),m=()=>{t.value="",l.value=[],o("showSearchModal",!1)},u=Ze(async()=>{if(t.value==""||!t.value){E.error("请输入要测试的文本");return}d.value=Re.service({lock:!0,text:"搜索中",background:"rgba(0, 0, 0, 0.5)"}),l.value=[];try{const r=await Bs(s.kid,t.value);r.data.message?E.error(r.data.message):(l.value=r.data,r.data.length<=0&&(d.value.close(),E.error("暂无搜索结果")))}catch{d.value.close(),E.error("搜索失败")}finally{d.value.close()}});return{results:l,closeModal:m,searchTest:u,content:t,getFileIcon:Ko}}},Fn=s=>(be("data-v-2299a871"),s=s(),we(),s),Sd={class:"new-modal-overlay"},Id={class:"new-modal-container"},Bd={class:"new-modal-top"},Td={style:{width:"40%"}},Md=Fn(()=>e("div",{class:"font18 font-zhongcu"},"搜索测试",-1)),Dd={class:"upload-step4-left-btn-div font14"},Vd={style:{width:"57.5%"}},Ed=Fn(()=>e("div",{class:"font18 font-zhongcu",style:{"margin-bottom":"15px"}},"测试结果",-1)),Ld={key:0,class:"upload-step4-right-result overflow-y"},zd={class:"result-item-top font12"},Fd={class:"result-item-content font14"},Ud={class:"result-item-bottom font12"},Pd=["src"],Rd={class:"file-name"};function Nd(s,o,l,t,d,m){return a(),c("div",Sd,[e("div",Id,[e("div",Bd,[e("div",Td,[Md,me(e("textarea",{"onUpdate:modelValue":o[0]||(o[0]=u=>t.content=u),class:"upload-step4-left-txt overflow-y font14 font-zhongcu",placeholder:"输入要测试的文本"},null,512),[[Ae,t.content]]),e("div",Dd,[e("button",{onClick:o[1]||(o[1]=(...u)=>t.closeModal&&t.closeModal(...u)),class:"upload-step4-left-btn upload-btn common-cancel-btn"},"取消"),e("button",{onClick:o[2]||(o[2]=(...u)=>t.searchTest&&t.searchTest(...u)),class:"upload-step4-left-btn upload-btn common-confirm-btn"},"测试")])]),e("div",Vd,[Ed,t.results.length>0?(a(),c("div",Ld,[(a(!0),c(N,null,le(t.results,(u,r)=>(a(),c("div",{class:"result-item",key:r},[e("div",zd,"# "+S(r+1)+" | 语义检索 "+S(u.docfragment_score.toFixed(4)),1),e("div",Fd,S(u.docfragment),1),e("div",Ud,[e("img",{src:t.getFileIcon(u.document_type),alt:"file icon",class:"file-icon"},null,8,Pd),e("span",Rd,S(u.document_title)+"."+S(u.document_type),1)])]))),128))])):z("",!0)])])])])}const qd=ue(xd,[["render",Nd],["__scopeId","data-v-2299a871"]]),Kd={props:{sids:{type:Array,required:!0},dids:{type:Array,required:!1},type:{type:String,required:!0},closeModal:{type:Function,required:!0}},components:{ArrowRight:mn,CloseBold:Lt,ArrowLeftBold:$e},setup(s,{emit:o}){const l=n(0),t=n([]),d=n([]),m=n([]),u=n([]),r=n([]),v=n([]),k=n([]),h=()=>{s.closeModal()},g=f=>{y(f),w(f),T(f)},x=f=>{g(f)},y=async f=>{try{const I=await ul(f);t.value=I.data}catch{}},w=async f=>{try{const U=await pl(f);if(d.value=U.data,!f){const J=await Xs();for(var I=0;I<J.data.length;I++)d.value.push(J.data[I])}}catch{}},T=async f=>{try{const I=await hl(f);k.value=I.data}catch{}},A=f=>{if(L(f))P(f);else{const I=t.value.filter(U=>f==U.id);r.value=[...r.value,...I],l.value+=1}},C=f=>{if(B(f))b(f);else{const I=d.value.filter(U=>f==U.id);v.value=[...v.value,...I],l.value+=1}},L=f=>r.value.some(I=>I.id===f),B=f=>v.value.some(I=>I.id===f),P=f=>{const I=r.value.findIndex(U=>U.id===f);I!==-1&&(r.value=[...r.value.slice(0,I),...r.value.slice(I+1)],l.value-=1)},b=f=>{const I=v.value.findIndex(U=>U.id===f);I!==-1&&(v.value=[...v.value.slice(0,I),...v.value.slice(I+1)],l.value-=1)},M=()=>{s.type=="kbase"?o("valueFromChild",v.value):s.type=="queryapp"?o("valueFromChild",v.value,r.value):s.type=="tool"&&o("valueFromChild",v.value),s.closeModal()},D=async()=>{try{const I=await vl();m.value=I.data;const U=await Xs();for(var f=0;f<U.data.length;f++)m.value.push(U.data[f])}catch{}},V=async()=>{try{const f=await ml();u.value=f.data}catch{}};return ge(async()=>{y(),T(),w(),await D(),console.log(s.sids,"已选员工"),console.log(s.dids,"已选部门"),s.sids&&m.value.length&&(m.value.forEach(f=>{s.sids.includes(f.id)&&(new Set(v.value.map(U=>U.id)).has(f.id)||v.value.push(f))}),l.value=v.value.length+r.value.length),await V(),s.dids&&u.value.length&&(u.value.forEach(f=>{s.dids.includes(f.id)&&(new Set(r.value.map(U=>U.id)).has(f.id)||r.value.push(f))}),l.value=v.value.length+r.value.length)}),{emitCloseModal:h,selectedNum:l,departments:t,siteusers:d,selectedDepartments:r,selectedSiteusers:v,nextDepartment:g,getFirstLetter:He,selectedDepartmentAction:A,selectedSiteuserAction:C,delDepartmentAction:P,delSiteuserAction:b,emitSelectedData:M,isDepartmentSelected:L,isSiteuserSelected:B,departmentFathers:k,prevDepartment:x}}},Un=s=>(be("data-v-ebbfd5b6"),s=s(),we(),s),Hd={class:"new-modal-overlay"},Qd={class:"new-modal-container"},Od={class:"new-modal-top"},Zd={class:"font18 font-zhongcu"},$d={class:"new-modal-center font12"},Yd={class:"new-modal-center-left overflow-y"},jd={class:"department-left-title"},Jd=["disabled","onClick"],Gd={key:0},Wd={class:"department-list"},Xd={class:"department-item-left"},eu=["onChange","checked"],tu=Un(()=>e("div",{class:"department-img"},[e("div")],-1)),ou={class:"overflow-one"},su=["onClick"],nu={class:"department-item-left"},au=["onChange","checked"],lu={class:"siteuser-img"},iu={class:"overflow-one"},cu={class:"new-modal-center-right overflow-y"},ru={class:"department-right-title"},du={class:"selected-department-list"},uu={class:"selected-department-item-left"},mu=Un(()=>e("div",{class:"selected-department-img"},[e("div")],-1)),pu={class:"overflow-one"},vu={class:"selected-department-item-left"},hu={class:"selected-siteuser-img"},fu={class:"overflow-one"},_u={class:"new-modal-bottom"};function gu(s,o,l,t,d,m){const u=_("ArrowLeftBold"),r=_("el-icon"),v=_("ArrowRight"),k=_("CloseBold");return a(),c("div",Hd,[e("div",Qd,[e("div",Od,[e("div",Zd,[l.type=="kbase"?(a(),X(r,{key:0,color:"#000",size:"16",onClick:t.emitCloseModal,style:{position:"relative",top:"2px",cursor:"pointer"}},{default:p(()=>[i(u)]),_:1},8,["onClick"])):z("",!0),Q(" 添加人员 ")])]),e("div",$d,[e("div",Yd,[e("div",jd,[(a(!0),c(N,null,le(t.departmentFathers.slice(-2),(h,g)=>(a(),c("button",{key:h.id,disabled:g!==0,onClick:x=>g==0?t.prevDepartment(h.id):null,class:"prevdepartment-btn"},[Q(S(h.name),1),g==0&&t.departmentFathers.length>1?(a(),c("span",Gd,"/")):z("",!0)],8,Jd))),128))]),e("div",Wd,[(a(!0),c(N,null,le(t.departments,h=>(a(),c("div",{class:"department-item",key:h.id},[e("div",Xd,[l.type=="queryapp"?(a(),c("input",{key:0,type:"checkbox",onChange:g=>t.selectedDepartmentAction(h.id),checked:t.isDepartmentSelected(h.id)},null,40,eu)):z("",!0),tu,e("span",ou,S(h.name),1)]),e("button",{class:"department-item-right",onClick:g=>t.nextDepartment(h.id)},[i(r,null,{default:p(()=>[i(v)]),_:1})],8,su)]))),128)),(a(!0),c(N,null,le(t.siteusers,h=>(a(),c("div",{class:"department-item",key:h.id},[e("div",nu,[e("input",{type:"checkbox",onChange:g=>t.selectedSiteuserAction(h.id),checked:t.isSiteuserSelected(h.id)},null,40,au),e("div",lu,S(t.getFirstLetter(h.name)),1),e("span",iu,S(h.name),1)])]))),128))])]),e("div",cu,[e("div",ru," 已选："+S(t.selectedNum)+"个 ",1),e("div",du,[(a(!0),c(N,null,le(t.selectedDepartments,h=>(a(),c("div",{class:"selected-department-item",key:h.id},[e("div",uu,[mu,e("span",pu,S(h.name),1)]),i(r,{class:"selected-department-item-right",onClick:g=>t.delDepartmentAction(h.id)},{default:p(()=>[i(k)]),_:2},1032,["onClick"])]))),128)),(a(!0),c(N,null,le(t.selectedSiteusers,h=>(a(),c("div",{class:"selected-department-item",key:h.id},[e("div",vu,[e("div",hu,S(t.getFirstLetter(h.name)),1),e("span",fu,S(h.name),1)]),i(r,{class:"selected-department-item-right",onClick:g=>t.delSiteuserAction(h.id)},{default:p(()=>[i(k)]),_:2},1032,["onClick"])]))),128))])])]),e("div",_u,[e("button",{class:"new-modal-bottom-cancel font14 font-zhongcu",onClick:o[0]||(o[0]=(...h)=>t.emitCloseModal&&t.emitCloseModal(...h))}," 取消 "),e("button",{class:"new-modal-bottom-confirm font14 font-zhongcu",onClick:o[1]||(o[1]=(...h)=>t.emitSelectedData&&t.emitSelectedData(...h))}," 确认 ")])])])}const Us=ue(Kd,[["render",gu],["__scopeId","data-v-ebbfd5b6"]]),Ps="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADIAAAAyCAYAAAAeP4ixAAAAAXNSR0IArs4c6QAABBpJREFUaEPtmUeoVUkQhj9HZ0xjxJzBtBIFxQSDqDhG3DjmiAFFNyIIrsWtuDEgBsQEzriZMaGIixkEHTDiRhAzKmYdMaf+pRuaM/ee233uuU993IbHPe+c6qr6q6q7uqvqUEtGnVqCg1oJpD0wH+gPNAV+gC9A9ev+/P/950/AQ+AqcAw4Arwq4u0fgdH2ryfQGqgbGRn/AWeATcBlzXUemQTsAH6OZFiM/C6wxgoSSDdmG6OsNrK65STnHbAU2CogA4CTwE85MffZ/AXMACRwi/HYnArI+AiMEJA/gYmegLPAdUAEsqZ+0571TXzamLAaCrRKKKswe2pCYHrivd7JgPeA95EA5dFfvYg6JgWeAM0to53A3EimPrlifSawDmhZhM8LYJX10NsyZG2wYSUWzwXEj+FlxrIby2Dupvaw1paX/PEMGA6cy0GG1obAfBmVAiLe2kD2JxReAmzOAYRY1BgQGek20MEqri1THnr9vQGRvoeBsVbxf01+GZQTiEweUQIbDDROUeIDcB54kKD5A/jNvjsBjEx8b2ETcL0U3kqsp4A3CZro0FKmHhVgSW2n/YAbHm0aEIXZRZM02wbwljdlTH9jigKiJKmYDj2TaevdGwhkHHAoAIQjEeD7Hn0UEM1TRl4YIPAKMMSeuRx5mkea2C26TwBv7X6Tyw0tzW8HNEoRqOx+C9Ba8UepNaLDaOcSh0ZFxJ0CsqM9EmCwoiSlgJTDuwoki/W+KY/o1LqywBrR0VyK6n5RbMQAWWFO3fMKXCe0RnSm0qbjj6jQUjLUQa9hirLKHReKfA8F0tWcuq+lbPPaRHQ9UK5yIwqI8ocSnHaWQkPZVtdV7ViFRigQZfibKTfUR8YjHRPZPQqIlOtt7yjJI4pCSzfAv3MKrYH2OFM/wU9HFCVZnQIyh1aWBe7PCfVIFjnRHskixM2pAgmwXtUjAUb6H0k1tAKsVg2tACNVQyuLkaqhlcVqu4BZdqJKQ+OzMCkyp0Y9MsyUew6YFoPKPVNM5f/g9wpEejewjaKXOYIQqxr1SM66f73TbyWBqHOw3gnQxUm3rmb2xXZzSVpQSek58Zbe+7xa12O90GKc4Ak4bTtWfjNUdK75mXx2dO67yprqeG01BWzx8kdfYDGg/klavbcU3i5Ad49ov4SrQv4PoPt5nkNFO/VDXNFAlcI9FZCjG+QgV9OdZu7l20pUE7OAVGtNZVS129SbTKvoZ+Gvu7zqzUf94nQnYJEt86tN7Rqh+i327Jqk/ndVXMZ4FRGFmQD18jQ9bqszWZTXnOfWMLtdZSW0yh4rUI18hVWh8TswNZZhKfpKAVHBW42dZIfqEvBLoj5VSseg75UCIuHK6MttmKltrXBaa9dLkHIxRJUEEqNH2bRVIGWbMGcGnwGnsDDRdmO+SAAAAABJRU5ErkJggg==",Pn="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAMAAAAolt3jAAAAV1BMVEUAAABkZGRmZmZmZmZmZmZmZmZpaWlmZmZlZWVkZGRdXV1mZmZmZmZlZWVmZmZlZWVmZmZlZWVkZGRkZGRkZGRlZWVmZmZlZWVmZmZlZWVmZmZmZmZkZGTb2IcjAAAAHXRSTlMAF9DCuNoMdlElC9XLhF79mGdWS0g8N8rGsKNra4LdvnYAAABpSURBVAjXPcxXDoQwEATRMh4nHIAlbLz/ORcJM/331FIBzYlB97M+DupmPTzU2cDp0vkJsB+4fnv7JblZSLcX6tSKFHNZnKxTRfsjz+B21ttmIC0Q3xeRAJCGztGeNnlDXY8cQT2/NvgDTCUDVzn+r6AAAAAASUVORK5CYII=",Au="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAUxJREFUOE+d0r1KA0EQB/CZPZK0iZXga6RIZ2MaPxqV1IGwWYMgpBPBSjCFkEKQkJkQUqcQG7WKYKfkIfQNFAWL5O7+ciGRywdi3G5357czwyzTPxfHXalU2vE8r0pEWQBkjOkDqIvI7ez7P9Bae87Mu0R07Pt+Lwo0xuSNMTUAXVU9jeMRHGe6GAwGuU6n8x4PqFQqmSAInpi52mw27yZ3I1gulx+I6FJEbha1bK3dY+YDEcnPwg/f99fa7fbnIlgsFtOJROJFVTNLw2Qy+Soi6SnonOsFQXDVarWuF2V0zu2HYehUda7ULSKqe56XazQabzMjWjHGPBPRkareT2WMNtbaM2YuADgZDoejcaRSqY0wDGvMvApgW1Uf52B04JzbBFAFkGVmBtCPKiGiL2buAihM8NTP+e33WWvX4/jPcNxOhA9FpLAUjFf0DelAlg9B74KCAAAAAElFTkSuQmCC",yu="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA4AAAAOCAYAAAAfSC3RAAAAAXNSR0IArs4c6QAAAalJREFUOE+dUj2IE1EQ/r63j0AQIbEKWykHwUMU5DqLI6CF7YGBa1KYfbumUeQaObFYVPwBlcNYhJdsLCysrVIYQSzsrlJQU5pCrrGxSWB3Rx4kRy7G5l73Zuabb2a+j1h6QRCcUkrdAXB9lnqd5/nTJEl+L5Zy/mk2mye11rdFZAfAB5KPXE5E7gK4TPJFmqZ7/X7/j4uzVqvparV6U0R2Se7neX6v1+vtL3Y3xmwopR6KyAbJx6PRqM0oit4AOJ9l2a0kST7NAfV6vVCpVNhut6fzWBAEm57nvQTwhWEYTkXk0pzFGLNO8hXJmpsUwHsAN6y1P10Dx07ys2N0yXVr7ffZnj9E5C3J+5PJRBcKhSdKqc3xeHxhMBhMoyg6C+DbEWAYhlskn1lr1xZG9srl8oGIbHe73eFKoDHmmlLque/7Z+I4zh3Y7VoqlQ48z9vqdDofVwIbjcaJYrH4FcAQwIM0TbXW2slyzvf9i3Ecp4fA5eO0Wq3TWZbtkbwKwLG+A7Bjrf21fJyVcjiNZ1c9lPSIHMc2wH8sN3QOmVluF8CVfyx3XJP/BWKw6lW6WZ5oAAAAAElFTkSuQmCC",bu={name:"KBaseListView",components:{BaseNavComponennt:Me,ElIcon:tt,CirclePlus:vt,BreadCrumbComponent:Ee,Delete:It,searchTestComponent:qd,DepartmentPop:Us},setup(){const s=n("kbase"),o=n(""),l=n([]),t=n(!1),d=n(""),m=n(""),u=Ce(),r=n(!1),v=n(!1),k=n(null),h=n(!1),g=n("永久删除知识库"),x=n("该知识库数据将被永久删除，不可恢复及撤销。确定要删除吗？"),y=n(null),w=n(!1),T=n(!1),A=n([]),C=n([]),L=n(!1),B=n(1),P=n(18),b=n(0),M=n([{path:"/kbase-list",name:"应用管理"},{path:"",name:"知识库"}]),D=async($,ae)=>{k.value=Re.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});try{const te=await el($,ae);k.value.close(),l.value=te.data.data,b.value=te.data.total_num[0],te.data.data.length<=0&&(v.value=!0)}finally{k.value.close()}},V=()=>{t.value=!1,T.value=!1,d.value="",m.value="",A.value=[],C.value=[]},f=async()=>{try{const $=await tl(d.value,m.value,R.value);$.data.error=="0"?u.push({name:"DocList",params:{kid:$.data.id}}):$.data.error=="403"?E.error("暂无权限"):$.data.message&&E.error($.data.message),V()}finally{V()}},I=async()=>{try{const $=await ol(d.value,m.value,y.value,C.value.join("|"));$.data.error=="0"?E.success("设置成功"):$.data.error=="403"?E.error("暂无权限"):$.data.message&&E.error($.data.message),V()}finally{V(),D(B.value,P.value)}},U=$=>{r.value=ze($,["create_docbase"])},J=$=>{o.value=$},O=$=>{B.value=$,D(B.value,P.value)};ge(()=>{D(B.value,P.value)});const Y=$=>{u.push({name:"DocList",params:{kid:$}})},R=n("text-embedding-v3"),j=[{value:"bge-m3",label:"bge-m3"},{value:"text-embedding-v3",label:"text-embedding-v3"},{value:"text-embedding-3-large",label:"text-embedding-3-large"},{value:"custom-bce-embedding-base_v1",label:"CUSTOM-BCE-EMBEDDING"}],ee=$=>{h.value=!0,y.value=$},K=()=>{h.value=!1,y.value=null},H=Ze(async()=>{h.value=!1;const $=await xl(y.value);$.data.error=="0"?(E.success("删除成功"),y.value=null):$.data.message?E.error($.data.message):E.error("删除失败"),D(B.value,P.value)}),ne=$=>{y.value=$,w.value=!0},re=$=>{w.value=$},G=async $=>{try{const ae=await jt($);ae.data.error=="0"&&(d.value=ae.data.title,m.value=ae.data.description,ae.data.siteusers.forEach(te=>{A.value.push({id:te.id,name:te.name,nameOne:te.name.split("")[0]}),C.value.push(te.id)}))}catch{}};return{selectedBar:s,kbaseList:l,showModal:t,title:d,description:m,closeModal:V,createKnowledgeBase:f,handleDataPermissions:U,canPerformAction:r,embeddingTypeOptions:j,selectedEmbeddingType:R,showDefault:v,totalItems:b,handlePageChange:O,pageSize:P,currentPage:B,handleNickname:J,nickname:o,breadcrumbs:M,showDelModal:h,modalDelTitle:g,modalDelMessage:x,delKBase:ee,handleClose:K,handleConfirm:H,navigateToDocList:Y,selectedKid:y,showSearchModal:w,showSearchModalAction:ne,handleShowSearchModal:re,siteuserList:A,siteuserIds:C,editKbaseAction:async($="")=>{y.value=$,await G($),T.value=!0},showEditModal:T,getFirstLetter:He,showDepartmentPopModal:L,showDepartmentPop:()=>{L.value=!L.value},handleValueFromChild:$=>{const ae=$;A.value=[],C.value=[],ae.forEach(te=>{A.value.push(te),C.value.push(te.id)})},updateKnowledgeBase:I}}},Ne=s=>(be("data-v-7db1433d"),s=s(),we(),s),wu={class:"common-layout",style:{height:"100vh"}},Cu={class:"el-main-container"},ku={class:"el-main-center"},xu=Ne(()=>e("div",{class:"font18 font-zhongcu"},"我的知识库",-1)),Su={key:0,style:{width:"100%",height:"95%",display:"flex","align-items":"center","justify-content":"center","flex-direction":"column"}},Iu=Ne(()=>e("img",{src:Ps,alt:"",style:{width:"50px",height:"50px","margin-bottom":"10px"}},null,-1)),Bu=Ne(()=>e("div",null,"还没有知识库，快去创建一个吧",-1)),Tu=[Iu,Bu],Mu={class:"el-main-bottom kbase-list overflow-y"},Du={class:"el-main-bottom-list"},Vu=["onClick"],Eu={class:"el-col"},Lu={class:"el-col-left font18 font-zhongcu"},zu={class:"el-col-right"},Fu={class:"font14 font-zhongcu overflow-one el-col-right-title"},Uu={class:"font12 overflow-one el-col-right-desc"},Pu={class:"el-col-modal"},Ru={class:"el-col-modal-bottom"},Nu=Ne(()=>e("div",{class:"font12 el-col-modal-bottom-left"},[e("img",{src:Pn,alt:""}),Q("可编辑 ")],-1)),qu={class:"el-col-modal-bottom-right"},Ku=Ne(()=>e("img",{src:Au,alt:""},null,-1)),Hu=Ne(()=>e("img",{src:yu,alt:""},null,-1)),Qu={key:1,class:"el-col-link"},Ou={class:"el-col"},Zu={class:"el-col-left font18 font-zhongcu"},$u={class:"el-col-right"},Yu={class:"font14 font-zhongcu overflow-one el-col-right-title"},ju={class:"font12 overflow-one el-col-right-desc"},Ju=Ne(()=>e("div",{class:"el-col-modal"},[e("div",{class:"el-col-modal-bottom"},[e("div",{class:"font12 el-col-modal-bottom-left"},[e("img",{src:Pn,alt:""}),Q("仅使用 ")])])],-1)),Gu={class:"breadcrumb-container"},Wu={key:0,class:"new-modal-overlay"},Xu={class:"new-modal-container"},e1=Ne(()=>e("div",{class:"new-modal-top"},[e("div",{class:"font18 font-zhongcu"},"创建知识库")],-1)),t1={class:"new-modal-center"},o1={class:"new-modal-center-item"},s1=Ne(()=>e("label",{class:"font16 font-zhongcu"},"名称",-1)),n1={class:"new-modal-center-item"},a1=Ne(()=>e("label",{class:"font16 font-zhongcu"},"描述",-1)),l1={class:"new-modal-center-item"},i1=Ne(()=>e("label",{class:"font16 font-zhongcu"},"数据模型",-1)),c1={class:"new-modal-bottom"},r1=Ne(()=>e("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu common-confirm-btn"}," 保存 ",-1)),d1={key:1,class:"new-modal-overlay"},u1={class:"new-modal-container"},m1=Ne(()=>e("div",{class:"new-modal-top"},[e("div",{class:"font18 font-zhongcu"},"配置知识库")],-1)),p1={class:"new-modal-center"},v1={class:"new-modal-center-item"},h1=Ne(()=>e("label",{class:"font16 font-zhongcu"},"名称",-1)),f1={class:"new-modal-center-item"},_1=Ne(()=>e("label",{class:"font16 font-zhongcu"},"描述",-1)),g1={class:"new-modal-center-item"},A1=Ne(()=>e("label",{class:"font16 font-zhongcu"},"使用权限",-1)),y1={class:"field-ipt field-siteuser"},b1={class:"siteuser-div"},w1={class:"siteuser-headimg"},C1={class:"new-modal-bottom"},k1=Ne(()=>e("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu common-confirm-btn"}," 保存 ",-1));function x1(s,o,l,t,d,m){const u=_("BaseNavComponennt"),r=_("CirclePlus"),v=_("el-icon"),k=_("Delete"),h=_("el-button"),g=_("el-tooltip"),x=_("el-pagination"),y=_("BreadCrumbComponent"),w=_("el-main"),T=_("el-container"),A=_("newComfirmsModal"),C=_("el-option"),L=_("el-select"),B=_("searchTestComponent"),P=_("DepartmentPop");return a(),c(N,null,[e("div",wu,[i(T,{style:{height:"100vh"},class:"el-container-base"},{default:p(()=>[i(u,{selectedBar:t.selectedBar,onDataPermissions:t.handleDataPermissions,onNickname:t.handleNickname},null,8,["selectedBar","onDataPermissions","onNickname"]),i(w,{class:"el-main common-layout-bg"},{default:p(()=>[e("div",Cu,[e("div",ku,[xu,t.canPerformAction?(a(),c("button",{key:0,class:"el-main-center-add font18",onClick:o[0]||(o[0]=b=>t.showModal=!0)},[i(v,{size:"22",style:{position:"relative",top:"5px"}},{default:p(()=>[i(r)]),_:1}),Q(" 创建 ")])):z("",!0)]),t.showDefault?(a(),c("div",Su,Tu)):z("",!0),e("div",Mu,[e("div",Du,[(a(!0),c(N,null,le(t.kbaseList,b=>(a(),c(N,{key:b.id},[b.is_edit&&t.canPerformAction?(a(),c("div",{key:0,class:"el-col-link cursor",onClick:M=>t.navigateToDocList(b.id)},[e("div",Eu,[e("div",Lu,S(t.getFirstLetter(b.title)),1),e("div",zu,[e("div",Fu,S(b.title),1),e("div",Uu,S(b.description?b.description:"这个知识库还没有介绍～"),1)]),e("div",Pu,[i(g,{class:"box-item",effect:"dark",content:"删除",placement:"bottom"},{default:p(()=>[i(h,{class:"el-col-modal-btn el-col-modal-del",onClick:_e(M=>t.delKBase(b.id),["stop"])},{default:p(()=>[i(v,{style:{"font-size":"14px"}},{default:p(()=>[i(k)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024),e("div",Ru,[Nu,e("div",qu,[i(g,{class:"box-item",effect:"dark",content:"搜索",placement:"bottom"},{default:p(()=>[i(h,{class:"el-col-modal-btn",onClick:_e(M=>t.showSearchModalAction(b.id),["stop"])},{default:p(()=>[Ku]),_:2},1032,["onClick"])]),_:2},1024),i(g,{class:"box-item",effect:"dark",content:"设置",placement:"bottom"},{default:p(()=>[i(h,{class:"el-col-modal-btn",onClick:_e(M=>t.editKbaseAction(b.id),["stop"])},{default:p(()=>[Hu]),_:2},1032,["onClick"])]),_:2},1024)])])])])],8,Vu)):(a(),c("div",Qu,[e("div",Ou,[e("div",Zu,S(t.getFirstLetter(b.title)),1),e("div",$u,[e("div",Yu,S(b.title),1),e("div",ju,S(b.description?b.description:"这个知识库还没有介绍～"),1)]),Ju])]))],64))),128))]),i(x,{background:"",layout:"prev, pager, next",total:t.totalItems,"page-size":t.pageSize,"current-page":t.currentPage,onCurrentChange:t.handlePageChange,"hide-on-single-page":!0},null,8,["total","page-size","current-page","onCurrentChange"])])]),e("div",Gu,[i(y,{class:"breadcrumb",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})]),i(A,{show:t.showDelModal,title:t.modalDelTitle,message:t.modalDelMessage,onClose:t.handleClose,onConfirm:t.handleConfirm},null,8,["show","title","message","onClose","onConfirm"]),t.showModal?(a(),c("div",Wu,[e("div",Xu,[e1,e("form",{onSubmit:o[5]||(o[5]=_e((...b)=>t.createKnowledgeBase&&t.createKnowledgeBase(...b),["prevent"]))},[e("div",t1,[e("div",o1,[s1,me(e("input",{id:"title",class:"field-ipt font14 font-zhongcu","onUpdate:modelValue":o[1]||(o[1]=b=>t.title=b),placeholder:"点击输入名字",required:"",maxlength:"50"},null,512),[[Ae,t.title]])]),e("div",n1,[a1,me(e("textarea",{id:"description",class:"field-ipt field-txt font14 font-zhongcu","onUpdate:modelValue":o[2]||(o[2]=b=>t.description=b),placeholder:"给知识库介绍一下",required:"",maxlength:"100"},null,512),[[Ae,t.description]])]),e("div",l1,[i1,i(L,{modelValue:t.selectedEmbeddingType,"onUpdate:modelValue":o[3]||(o[3]=b=>t.selectedEmbeddingType=b),placeholder:"选择数据模型",size:"large",class:"font14 font-zhongcu field-select",id:"embedding_type"},{default:p(()=>[(a(!0),c(N,null,le(t.embeddingTypeOptions,b=>(a(),X(C,{key:b.value,label:b.label,value:b.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),e("div",c1,[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:o[4]||(o[4]=(...b)=>t.closeModal&&t.closeModal(...b))}," 取消 "),r1])],32)])])):z("",!0),t.showEditModal?(a(),c("div",d1,[e("div",u1,[m1,e("form",{onSubmit:o[9]||(o[9]=_e((...b)=>t.updateKnowledgeBase&&t.updateKnowledgeBase(...b),["prevent"]))},[e("div",p1,[e("div",v1,[h1,me(e("input",{id:"title",class:"field-ipt font14 font-zhongcu","onUpdate:modelValue":o[6]||(o[6]=b=>t.title=b),placeholder:"点击输入名字",required:"",maxlength:"50"},null,512),[[Ae,t.title]])]),e("div",f1,[_1,me(e("textarea",{id:"description",class:"field-ipt field-txt font14 font-zhongcu","onUpdate:modelValue":o[7]||(o[7]=b=>t.description=b),placeholder:"给知识库介绍一下",required:"",maxlength:"100"},null,512),[[Ae,t.description]])]),e("div",g1,[A1,e("div",y1,[e("div",b1,[(a(!0),c(N,null,le(t.siteuserList.slice(0,4),b=>(a(),c("div",{class:"siteuser-div-item font12 font-zhongcu",key:b.id},[e("div",w1,S(t.getFirstLetter(b.name)),1),Q(" "+S(b.name),1)]))),128))]),i(v,{size:"20",class:"siteuser-add",onClick:t.showDepartmentPop},{default:p(()=>[i(r)]),_:1},8,["onClick"])])])]),e("div",C1,[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:o[8]||(o[8]=(...b)=>t.closeModal&&t.closeModal(...b))}," 取消 "),k1])],32)])])):z("",!0),t.showSearchModal?(a(),X(B,{key:2,kid:t.selectedKid,onShowSearchModal:t.handleShowSearchModal},null,8,["kid","onShowSearchModal"])):z("",!0),t.showDepartmentPopModal?(a(),X(P,{key:3,type:"kbase",sids:t.siteuserIds,onValueFromChild:t.handleValueFromChild,closeModal:t.showDepartmentPop},null,8,["sids","onValueFromChild","closeModal"])):z("",!0)],64)}const S1=ue(bu,[["render",x1],["__scopeId","data-v-7db1433d"]]),I1="data:image/png;base64,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",B1={name:"QueryAppListView",components:{BaseNavComponennt:Me,ElIcon:tt,CloseBold:Lt,BreadCrumbComponent:Ee,CirclePlus:vt,Delete:It},setup(){const s=n("queryapp"),o=n([]),l=n(!1),t=n(""),d=n(""),m=Ce(),u=n(!1),r=n(!1),v=n(null),k=n(!1),h=n("永久删除应用"),g=n("该应用数据将被永久删除，不可恢复及撤销。确定要删除吗？"),x=n(null),y=n(1),w=n(18),T=n(0),A=n([{path:"/queryapp-list",name:"应用管理"},{path:"",name:"应用"}]),C=async(J,O)=>{v.value=Re.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});try{const Y=await al(J,O);v.value.close(),o.value=Y.data.data,T.value=Y.data.total_num[0],Y.data.data.length<=0&&(r.value=!0)}finally{v.value.close()}},L=()=>{l.value=!1,t.value="",d.value=""},B=Ze(async()=>{try{const J=await ll(t.value,d.value,M.value);J.data.error=="0"?m.push({name:"QueryappDetail",params:{qid:J.data.id}}):J.data.error=="403"&&E.error("暂无权限"),L()}finally{L()}}),P=J=>{u.value=ze(J,["create_aiserver"])},b=J=>{y.value=J,C(y.value,w.value)};ge(()=>{C(y.value,w.value)});const M=n("text-embedding-v3"),D=[{value:"bge-m3",label:"bge-m3"},{value:"text-embedding-v3",label:"text-embedding-v3"},{value:"text-embedding-3-large",label:"text-embedding-3-large"},{value:"custom-bce-embedding-base_v1",label:"CUSTOM-BCE-EMBEDDING"}],V=J=>{m.push({name:"QueryappDetail",params:{qid:J}})},f=J=>{k.value=!0,x.value=J},I=()=>{k.value=!1,x.value=null},U=Ze(async()=>{k.value=!1;const J=await wn(x.value);J.data.error=="0"?(E.success("删除成功"),x.value=null):J.data.message?E.error(J.data.message):E.error("删除失败"),C(y.value,w.value)});return{selectedBar:s,queryappList:o,showModal:l,title:t,description:d,closeModal:L,fetchcreateQueryapp:B,handleDataPermissions:P,canCreateAction:u,selectedEmbeddingType:M,embeddingTypeOptions:D,showDefault:r,totalItems:T,handlePageChange:b,pageSize:w,currentPage:y,breadcrumbs:A,getFirstLetter:He,delQueryApp:f,showDelModal:k,modalDelTitle:h,modalDelMessage:g,handleClose:I,handleConfirm:U,navigateTo:V}}},Mt=s=>(be("data-v-aeee979e"),s=s(),we(),s),T1={class:"common-layout",style:{height:"100vh"}},M1={class:"el-main-container"},D1={class:"el-main-center"},V1=Mt(()=>e("div",{class:"font18 font-zhongcu"},"我的应用",-1)),E1={key:0,style:{width:"100%",height:"95%",display:"flex","align-items":"center","justify-content":"center","flex-direction":"column"}},L1=Mt(()=>e("img",{src:I1,alt:"",style:{width:"50px",height:"50px","margin-bottom":"10px"}},null,-1)),z1=Mt(()=>e("div",null,"还没有应用，快去创建一个吧",-1)),F1=[L1,z1],U1={class:"el-main-bottom overflow-y"},P1={class:"el-main-bottom-list"},R1=["onClick"],N1={class:"el-col"},q1={class:"el-col-left font18 font-zhongcu"},K1={class:"el-col-right"},H1={class:"font14 font-zhongcu overflow-one el-col-right-title"},Q1={class:"font12 overflow-one el-col-right-desc"},O1={class:"el-col-modal"},Z1={class:"breadcrumb-container"},$1={key:0,class:"new-modal-overlay"},Y1={class:"new-modal-container"},j1=Mt(()=>e("div",{class:"new-modal-top"},[e("div",{class:"font18 font-zhongcu"},"创建应用")],-1)),J1={class:"new-modal-center"},G1={class:"new-modal-center-item"},W1=Mt(()=>e("label",{class:"font16 font-zhongcu"},"名称",-1)),X1={class:"new-modal-center-item"},em=Mt(()=>e("label",{class:"font16 font-zhongcu"},"描述",-1)),tm={class:"new-modal-center-item"},om=Mt(()=>e("label",{class:"font16 font-zhongcu"},"数据模型",-1)),sm={class:"new-modal-bottom"},nm=Mt(()=>e("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu common-confirm-btn"},"保存",-1));function am(s,o,l,t,d,m){const u=_("BaseNavComponennt"),r=_("CirclePlus"),v=_("el-icon"),k=_("Delete"),h=_("el-button"),g=_("el-tooltip"),x=_("el-pagination"),y=_("BreadCrumbComponent"),w=_("el-main"),T=_("el-container"),A=_("newComfirmsModal"),C=_("el-option"),L=_("el-select");return a(),c(N,null,[e("div",T1,[i(T,{style:{height:"100vh"},class:"el-container-base"},{default:p(()=>[i(u,{selectedBar:t.selectedBar,onDataPermissions:t.handleDataPermissions},null,8,["selectedBar","onDataPermissions"]),i(w,{class:"el-main common-layout-bg"},{default:p(()=>[e("div",M1,[e("div",D1,[V1,t.canCreateAction?(a(),c("button",{key:0,class:"el-main-center-add font18",onClick:o[0]||(o[0]=B=>t.showModal=!0)},[i(v,{size:"22",style:{position:"relative",top:"5px"}},{default:p(()=>[i(r)]),_:1}),Q(" 创建 ")])):z("",!0)]),t.showDefault?(a(),c("div",E1,F1)):z("",!0),e("div",U1,[e("div",P1,[(a(!0),c(N,null,le(t.queryappList,B=>(a(),c("div",{key:B.id,class:"el-col-link cursor",onClick:P=>t.navigateTo(B.id)},[e("div",N1,[e("div",q1,S(t.getFirstLetter(B.title)),1),e("div",K1,[e("div",H1,S(B.title),1),e("div",Q1,S(B.description?B.description:"这个应用还没有介绍～"),1)]),e("div",O1,[i(g,{class:"box-item",effect:"dark",content:"删除",placement:"bottom"},{default:p(()=>[i(h,{class:"el-col-modal-btn el-col-modal-del",onClick:_e(P=>t.delQueryApp(B.id),["stop"])},{default:p(()=>[i(v,{style:{"font-size":"14px"}},{default:p(()=>[i(k)]),_:1})]),_:2},1032,["onClick"])]),_:2},1024)])])],8,R1))),128))]),i(x,{background:"",layout:"prev, pager, next",total:t.totalItems,"page-size":t.pageSize,"current-page":t.currentPage,onCurrentChange:t.handlePageChange,"hide-on-single-page":!0},null,8,["total","page-size","current-page","onCurrentChange"])])]),e("div",Z1,[i(y,{class:"breadcrumb",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})]),i(A,{show:t.showDelModal,title:t.modalDelTitle,message:t.modalDelMessage,onClose:t.handleClose,onConfirm:t.handleConfirm},null,8,["show","title","message","onClose","onConfirm"]),t.showModal?(a(),c("div",$1,[e("div",Y1,[j1,e("form",{onSubmit:o[5]||(o[5]=_e((...B)=>t.fetchcreateQueryapp&&t.fetchcreateQueryapp(...B),["prevent"]))},[e("div",J1,[e("div",G1,[W1,me(e("input",{id:"title",class:"field-ipt font14 font-zhongcu","onUpdate:modelValue":o[1]||(o[1]=B=>t.title=B),placeholder:"点击输入名字",required:"",maxlength:"50"},null,512),[[Ae,t.title]])]),e("div",X1,[em,me(e("textarea",{id:"description",class:"field-ipt field-txt font14 font-zhongcu","onUpdate:modelValue":o[2]||(o[2]=B=>t.description=B),placeholder:"给应用介绍一下",required:"",maxlength:"100"},null,512),[[Ae,t.description]])]),e("div",tm,[om,i(L,{modelValue:t.selectedEmbeddingType,"onUpdate:modelValue":o[3]||(o[3]=B=>t.selectedEmbeddingType=B),placeholder:"选择数据模型",size:"large",class:"font14 font-zhongcu field-select",id:"embedding_type"},{default:p(()=>[(a(!0),c(N,null,le(t.embeddingTypeOptions,B=>(a(),X(C,{key:B.value,label:B.label,value:B.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),e("div",sm,[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:o[4]||(o[4]=(...B)=>t.closeModal&&t.closeModal(...B))},"取消"),nm])],32)])])):z("",!0)],64)}const lm=ue(B1,[["render",am],["__scopeId","data-v-aeee979e"]]),im="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAPNJREFUOE+l06tKREEYwPHfweIiJotZLBazeL+FxSwoPsM+gEE0eQk+gMFiMior6nppgsEHEIPRqMWqwTMwwmHZc5jlDAxMmP//u8x8mZorq8nrVzCFUVz+B+5HMI1bNLCBiyBJFcxE+AHDWMQk3lIEs7hBgEMGJ9jDfkoGcxG+L8A7OErpwTyuUYS3cVx8ubISFiJ8hyucYhcH3c/eS7AUoQBvYgKPeEcT31UZLEe4E+EfDOEcq3E/lwlW0EaAtzCONbTypg1iHU9lJYzkl18w1nXhC2c4zEWfvb59sQfhHKIGyQA+8IrfqnlJ+UiV81Zb8AdlzSsRHZYXQgAAAABJRU5ErkJggg==",cm={name:"DocListView",components:{BaseNavComponent:Me,ElPagination:pa,ArrowLeftBold:$e,CirclePlus:vt,BreadCrumbComponent:Ee},setup(){const s=n("kbase"),o=De(),l=Ce(),t=n("知识库"),d=n("doc"),m=n(null),u=n({}),r=n([]),v=n(1),k=n(10),h=n(0),g=n(null),x=n(!0),y=n(!1),w=n(!1),T=n("永久删除文档"),A=n("该文档数据将被永久删除，不可恢复及撤销。确定要删除吗？"),C=n([]),L=n(null),B=n(null),P=async(ee,K,H)=>{try{const ne=await sl(ee,K,H);ne.data.error=="1"?E.error("知识库不存在"):ne.data.error=="2"||ne.data.error=="403"?E.error("无权限查看"):(r.value=ne.data.data,h.value=ne.data.total_num[0])}catch{}},b=ee=>{v.value=ee,P(o.params.kid,v.value,k.value)},M=async()=>{const ee=await jt(m.value);return ee.data.error=="0"?`知识库（${ee.data.title}）`:"知识库"};ge(async()=>{P(o.params.kid,v.value,k.value),m.value=o.params.kid;let ee="";ee=await M(),C.value=[{path:"/kbase-list",name:ee},{path:"",name:"文档"}],L.value&&clearInterval(L.value)});const D=ee=>{ee=="file"?l.push({name:"DocAddFile",params:{kid:o.params.kid}}):l.push({name:"DocAddText",params:{kid:o.params.kid}})},V=ee=>{w.value=!0,g.value=ee},f=()=>{w.value=!1,g.value=null},I=async()=>{w.value=!1;try{const ee=await nl(o.params.kid,g.value);ee.data.error=="0"?E.success("删除成功"):ee.data.error=="1"?E.error("删除失败，知识库不存在"):ee.data.error=="2"?E.error("删除失败，无权限操作"):ee.data.error=="3"?E.error("删除失败，文档不存在"):ee.data.message&&E.error(ee.data.message),P(o.params.kid,v.value,k.value)}catch{}},U=ee=>{ee||(x.value=!1)},J=ee=>({wait:"待学习",split:"分片完成",doing:"学习中",success:"学习成功",fail:"学习失败"})[ee],O=()=>{y.value=!y.value},Y=async(ee,K)=>{try{const H=await po(ee,K);if(H.data.error=="0"){const ne=H.data,re={id:ne.id,title:ne.title,description:null,learn_type:ne.learn_type,status:ne.status,type:ne.type};R(ne.id,re),ne.status=="success"&&L.value&&clearInterval(L.value)}}catch{}};function R(ee,K){const H=r.value.findIndex(ne=>ne.id===ee);H!==-1&&(r.value[H]={...r.value[H],...K})}const j=Ze(async ee=>{B.value=Re.service({lock:!0,text:"正在提交",background:"rgba(0, 0, 0, 0.5)"}),(await Bl(o.params.kid,ee)).data.error=="0"&&(B.value.close(),E.success("学习中，请稍后查看"),L.value&&clearInterval(L.value),L.value=setInterval(()=>Y(o.params.kid,ee),2e3))});return{selectedBar:s,kbaseName:t,selectedKBaseNav:d,items:r,currentPage:v,pageSize:k,totalItems:h,handlePageChange:b,selectedKid:m,selectedKBase:u,nextLink:D,delDocment:V,showModal:w,modalTitle:T,modalMessage:A,handleClose:f,handleConfirm:I,canPerformAction:x,handleIsEdit:U,get_status_display:J,isShowOperate:y,showOperate:O,breadcrumbs:C,learnDocment:j}}},go=s=>(be("data-v-59a51d46"),s=s(),we(),s),rm={class:"common-layout",style:{height:"100vh"}},dm={class:"el-main-container overflow-y"},um={class:"el-main-top"},mm={class:"el-main-top-left font18 font-zhongcu"},pm={style:{position:"relative"}},vm={key:1,class:"el-main-top-right-caozuo font12"},hm={class:"el-main-bottom"},fm={class:"el-main-list"},_m={class:"el-col-head font14"},gm=go(()=>e("div",null,"#",-1)),Am=go(()=>e("div",null,"名称",-1)),ym=go(()=>e("div",null,"学习方式",-1)),bm=go(()=>e("div",null,"状态",-1)),wm={key:0},Cm={class:"el-col-body"},km={class:"font-zhongcu"},xm={class:"font-zhongcu"},Sm={class:"overflow-one el-col-item-title"},Im=go(()=>e("img",{src:im,alt:""},null,-1)),Bm={class:"font-zhongcu"},Tm={class:"font-zhongcu"},Mm={key:0},Dm={class:"breadcrumb-container"};function Vm(s,o,l,t,d,m){const u=_("BaseNavComponent"),r=_("ArrowLeftBold"),v=_("el-icon"),k=_("router-link"),h=_("CirclePlus"),g=_("el-button"),x=_("el-pagination"),y=_("BreadCrumbComponent"),w=_("el-main"),T=_("el-container"),A=_("newComfirmsModal");return a(),c(N,null,[e("div",rm,[i(T,{style:{height:"100vh"},class:"el-container-base"},{default:p(()=>[i(u,{selectedBar:t.selectedBar},null,8,["selectedBar"]),i(w,{class:"el-main common-layout-bg"},{default:p(()=>[e("div",dm,[e("div",um,[e("div",mm,[i(k,{to:{name:"KBaseList"},class:"el-main-top-left-back"},{default:p(()=>[i(v,{color:"#666666",size:"16"},{default:p(()=>[i(r)]),_:1})]),_:1}),Q(" 文档 ")]),e("div",pm,[t.canPerformAction?(a(),c("button",{key:0,class:"el-main-top-right-add font18",onClick:o[0]||(o[0]=(...C)=>t.showOperate&&t.showOperate(...C))},[i(v,{size:"22",style:{position:"relative",top:"5px"}},{default:p(()=>[i(h)]),_:1}),Q(" 创建 ")])):z("",!0),t.isShowOperate?(a(),c("div",vm,[e("button",{onClick:o[1]||(o[1]=C=>t.nextLink("txt"))},"自定义文本"),e("button",{onClick:o[2]||(o[2]=C=>t.nextLink("file"))},"本地文件上传")])):z("",!0)])]),e("div",hm,[e("div",fm,[e("div",_m,[gm,Am,ym,bm,t.canPerformAction?(a(),c("div",wm,"操作")):z("",!0)]),e("div",Cm,[(a(!0),c(N,null,le(t.items,(C,L)=>(a(),c("div",{class:"el-col-item font14",key:L},[e("div",km,S(L+1),1),e("div",xm,[e("div",Sm,S(C.title),1),i(k,{to:{name:"DocFragmentLIst",params:{kid:t.selectedKid,dcid:C.id}},class:"el-col-item-link"},{default:p(()=>[Im]),_:2},1032,["to"])]),e("div",Bm,[C.learn_type=="base"?(a(),c(N,{key:0},[Q("基础版")],64)):C.learn_type=="primary"?(a(),c(N,{key:1},[Q("普通版")],64)):C.learn_type=="advance"?(a(),c(N,{key:2},[Q("高级版")],64)):z("",!0)]),e("div",Tm,S(t.get_status_display(C.status)),1),t.canPerformAction?(a(),c("div",Mm,[i(g,{link:"",type:"primary",size:"default",class:"el-col-item-btn",onClick:B=>t.delDocment(C.id)},{default:p(()=>[Q("删除")]),_:2},1032,["onClick"]),C.status=="wait"?(a(),X(g,{key:0,link:"",type:"primary",size:"default",class:"el-col-item-btn",onClick:B=>t.learnDocment(C.id)},{default:p(()=>[Q("重新学习")]),_:2},1032,["onClick"])):z("",!0)])):z("",!0)]))),128))])]),i(x,{background:"",layout:"prev, pager, next",total:t.totalItems,"page-size":t.pageSize,"current-page":t.currentPage,onCurrentChange:t.handlePageChange,"hide-on-single-page":!0},null,8,["total","page-size","current-page","onCurrentChange"])])]),e("div",Dm,[i(y,{class:"breadcrumb",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})]),i(A,{show:t.showModal,title:t.modalTitle,message:t.modalMessage,onClose:t.handleClose,onConfirm:t.handleConfirm},null,8,["show","title","message","onClose","onConfirm"])],64)}const Em=ue(cm,[["render",Vm],["__scopeId","data-v-59a51d46"]]),Lm={props:{step:{type:Number,required:!0},typestr:{type:String,required:!0}}},Rn=s=>(be("data-v-0f73e42f"),s=s(),we(),s),zm={class:"progress-div font16"},Fm={class:"progress-item"},Um={key:0},Pm={key:1},Rm={class:"progress-item"},Nm=Rn(()=>e("div",null,"数据训练",-1)),qm={class:"progress-item"},Km=Rn(()=>e("div",null,"搜索测试",-1));function Hm(s,o,l,t,d,m){return a(),c("div",zm,[e("div",Fm,[e("div",{class:W(["progredd-item-shuzi",l.step==1||l.step==2||l.step==3?"progredd-item-shuzi-active":""])},"1",2),l.typestr=="text"?(a(),c("div",Um,"添加文本")):(a(),c("div",Pm,"选择文件"))]),e("div",{class:W(["progress-fenge",l.step==2||l.step==3?"progredd-item-shuzi-active":""])},null,2),e("div",Rm,[e("div",{class:W(["progredd-item-shuzi",l.step==2||l.step==3?"progredd-item-shuzi-active":""])},"2",2),Nm]),e("div",{class:W(["progress-fenge",l.step==3?"progredd-item-shuzi-active":""])},null,2),e("div",qm,[e("div",{class:W(["progredd-item-shuzi",l.step==3?"progredd-item-shuzi-active":""])},"3",2),Km])])}const Nn=ue(Lm,[["render",Hm],["__scopeId","data-v-0f73e42f"]]),qn="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAWVJREFUOE+t1D1LHkEUhuHLwiI22hqxSCUYf4I2akBCQNQUAQu7pEwTEBs1TT7AxlI7KxPQVMEIphP8BUkgTRpRW9NokSLugRlYxn31LXaaXWbOuec588yZHi2Pnnt4sT6K4RR3il/43ymvE3AAy1jCYPV/nQAPcIEdfMRlCW4CTmAPf7GBrzhPiQ/xDG/Qj+c4rkNLYMCOKlVbKelfh9J602av8KQOrQOjzN/4hNdderWJFxjJ5deB77GAx+ikrNwnlP7EPlZiMQPje1ZNrGO7C3Wx6Z9k1suUNxTuZ2AE/EBMZgMydzKdUVY9h894iu+VgDAqxIyF2gycwRf0FerepVJ2sYjZBFvDh1rsFeZxeBdwNcHiPr7FCaarskpYcG8By5KncJBMins4jm8IxWFefTSWXJoSHfEotVlODkeb3G80JZJavTYBbP1iB7TV1stn1erjkKGtPl/la9TKA9tFOzeH3AAi1GAVAkigrQAAAABJRU5ErkJggg==",Qm="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACMAAAAjCAMAAAApB0NrAAAAhFBMVEUAAAAAlP8Rm/8Smv8Rmv8Rm/8Amv8Rm/8Sm/8Rm/8Rm/8Rm/8Rm/8Sm/8Qm/8PmP8Rm/8Rm/8Rm/8RnP8Rmv8Qmv8Lmf8Rm/8Sm/8Qm/8Smv8Rmv8Rm/8Rm/8PnP8Rm/8Rm/8Sm/8Rm/8Rm/8Rmv8RnP8Sm/8Pmv8Qm/8PmP8RnP8Sm/820qDAAAAAK3RSTlMABpGdaFUK7vKopZh4ZGAQ/OHKsa5uFvbnTQ3a1IQg68O3oYtaPLhEQDQsaeyrOAAAASBJREFUOMvNktl2gjAQhiFEgiCrbKLWvdv3/u/XlhMlqdBe9dS5mUzOl8k/i/PY5r7KhXzxfyBqEQDQRE9TyExBUxyKBoJ4HJEZeeJ/fTjbwGIMuWQsO332j7AaUbth7w5hSe7fi6ExhaYKaQO9BM+6ObE+W5lWOd8FXID2PMQVBGHV2ZkrT8H2lgXKeqTS7giJfpGzdMbNQ3W6oqCeYPydri5ibt5boxKUvX82++6WhEYrY4reHwzGXQJz946JCE3EggSloVkj2Zr1APktUtd+Fb0lSzxEddsMgUqv4yTsj4lKnE/GkWrWZ4ngtmsLUJF8020TvX+XooWTsRgtkKUmEwC72GpoEu5DK09UzON0ckRiCP6YEWx/Zeoqdf7RPgD/fRpu7JoX7wAAAABJRU5ErkJggg==",Om={name:"DocAddFile",components:{BaseNavComponent:Me,ElIcon:tt,ArrowLeftBold:$e,DocProgressComponent:Nn,BreadCrumbComponent:Ee},setup(){const s=n("kbase"),o=n("知识库"),l=n("doc"),t=n(null),d=n([]),m=n(1),u=n("base"),r=n([]),v=De(),k=Ce(),h=n(10),g=n(["txt","docx","xlsx","pptx","pdf","csv","md"]),x=n(500),y=n(""),w=n([]),T=n(0),A=n(0),C=n(null),L=n([]),B=n(!1),P=n(""),b=n([]),M=n(null),D=H=>{if(g.value){let ne="";if(H.name.lastIndexOf(".")>-1&&(ne=H.name.slice(H.name.lastIndexOf(".")+1).toLowerCase()),!g.value.some(G=>(G=G.toLowerCase(),H.raw.type.toLowerCase().includes(G)||ne.includes(G))))return E(`文件格式不正确, 上传失败！请上传${g.value.join("/")}格式文件!`),!1}return x.value&&!(H.size/1024/1024<x.value)?(E(`上传文件大小不能超过 ${x.value} MB!`),!1):!0},V=(H,ne)=>{D(H)&&(w.value=ne,A.value+=1)},f=()=>{E(`一次最多上传${h.value}个文件`)},I=()=>{E.error("上传失败, 请重试")},U=()=>{T.value===A.value&&(M.value.close(),m.value=2,R(),C.value&&clearInterval(C.value),C.value=setInterval(R,2e3))},J=Ze(async()=>{try{M.value=Re.service({lock:!0,text:"提交中",background:"rgba(0, 0, 0, 0.5)"});for(const H of w.value){const ne=await xn(t.value,u.value,"","","",H.raw);ne.data.error=="0"?(y.value+=ne.data.id+"|",T.value+=1,U()):E.error("上传中断，部分文件上传失败")}}catch{M.value.close(),E.error("上传失败")}}),O=H=>{u.value=H},Y=H=>{w.value=w.value.filter(ne=>ne.uid!==H),A.value-=1},R=async()=>{const H=y.value.split("|").filter(Boolean);for(const G of H)await j(t.value,G);if(!r.value.every(G=>["fail","success"].includes(G.status))){console.error("存在未知状态的文档，无法继续处理");return}r.value.some(G=>G.status==="success")?(clearInterval(C.value),m.value=3,console.log("至少有一个文档已完成处理（成功）")):r.value.every(ve=>ve.status==="fail")&&(clearInterval(C.value),E.error("学习失败"),setTimeout(()=>{k.push({name:"DocList",params:{kid:t.value}})},1e3))},j=async(H,ne)=>{try{const re=await po(H,ne);if(re.data.error==="0"){const G=r.value.findIndex(ve=>ve.id===re.data.id);G!==-1?r.value[G].status!==re.data.status&&(r.value[G].status=re.data.status,r.value[G].status_display=re.data.status_display):r.value.push({title:`${re.data.title}.${re.data.type}`,status:re.data.status,status_display:re.data.status_display,id:re.data.id})}}catch(re){console.error(re)}},ee=async()=>{const H=await jt(t.value);return H.data.error=="0"?`知识库（${H.data.title}）`:"知识库"},K=async()=>{if(!B.value){if(B.value=!0,P.value==""||!P.value){E.error("请输入要测试的文本"),B.value=!1;return}M.value=Re.service({lock:!0,text:"搜索中",background:"rgba(0, 0, 0, 0.5)"}),b.value=[];try{const H=await Bs(t.value,P.value);H.data.message?E.error(H.data.message):b.value=H.data}catch{M.value.close(),E.error("搜索失败")}finally{B.value=!1,M.value.close()}}};return ge(async()=>{t.value=v.params.kid;let H="";H=await ee(),L.value=[{path:"/kbase-list",name:H},{path:`/doc-list/${t.value}`,name:"文档"},{path:"",name:"本地文件上传"}]}),ks(()=>{C.value&&clearInterval(C.value)}),{selectedBar:s,kbaseName:o,selectedKBaseNav:l,selectedKid:t,selectedFiles:d,fileList:w,fileNum:h,successfulUploads:T,totalUploads:A,step:m,selectedType:u,chooseType:O,getFileIcon:Ko,getFileBaseName:Mi,getFileExtension:En,items:r,breadcrumbs:L,content:P,searchTest:K,results:b,handlChange:V,handleExceed:f,handleUploadError:I,addDocument:J,delDocment:Y}}},ot=s=>(be("data-v-ddd9e0e2"),s=s(),we(),s),Zm={class:"common-layout",style:{height:"100vh"}},$m={class:"el-main-container"},Ym={class:"el-main-top"},jm={class:"el-main-top-left font18 font-zhongcu"},Jm={class:"el-main-bottom"},Gm={key:0,class:"overflow-y",style:{"max-height":"90%"}},Wm={class:"upload-step2-item"},Xm=ot(()=>e("div",{class:"upload-step2-item-title font16 font-zhongcu"},[e("img",{src:qn,alt:""}),Q(" 数据训练模式 ")],-1)),ep={class:"upload-step2-item-bottom"},tp=["checked"],op=ot(()=>e("div",{class:"upload-step2-item-type-desc"},[e("div",{class:"font16 font-zhongcu"},"基础版"),e("div",{class:"font12"},"数据学习新手版，带你走进数据世界的大门。")],-1)),sp=["checked"],np=ot(()=>e("div",{class:"upload-step2-item-type-desc"},[e("div",{class:"font16 font-zhongcu"},"普通版"),e("div",{class:"font12"},"数据技能加强版，让你数据操作更溜更顺畅。")],-1)),ap=["checked"],lp=ot(()=>e("div",{class:"upload-step2-item-type-desc"},[e("div",{class:"font16 font-zhongcu"},"高级版"),e("div",{class:"font12"},"数据洞察大师版，带你深度挖掘数据背后的故事。")],-1)),ip=ot(()=>e("img",{src:Qm,alt:""},null,-1)),cp={class:"el-upload__text"},rp=ot(()=>e("div",{class:"font14 font-zhongcu",style:{color:"#000"}},"点击此处选择文件上传",-1)),dp=ot(()=>e("div",{class:"font12",style:{color:"#666","line-height":"17px","margin-top":"5px"}},"支持 .txt, .docx, .csv, .xlsx, .md, .pptx, .pdf类型文件",-1)),up={class:"font12",style:{color:"#666","line-height":"17px"}},mp={key:0,class:"el-main-list",style:{"margin-top":"15px"}},pp=ot(()=>e("div",{class:"el-col-head font14"},[e("div",{style:{width:"5%"}},"#"),e("div",null,"文件名称"),e("div",{style:{width:"10%"}},"操作")],-1)),vp={class:"el-col-body"},hp={class:"font-zhongcu",style:{width:"5%"}},fp={class:"font-zhongcu",style:{display:"flex"}},_p=["src"],gp={class:"el-col-item-title overflow-one"},Ap={style:{width:"10%"}},yp={key:2,class:"upload-btn font14 font-zhongcu",style:{"margin-top":"20px",background:"#B2DEFF",cursor:"not-allowed"}},bp={key:1,class:"upload-step3"},wp={class:"upload-step3-file-list overflow-y"},Cp=ot(()=>e("div",{class:"el-col-head font14"},[e("div",null,"#"),e("div",null,"来源名称"),e("div",null,"状态")],-1)),kp={class:"font-zhongcu"},xp={class:"font-zhongcu overflow-one"},Sp=["src"],Ip={class:"font-zhongcu el-col-item-status"},Bp={key:0,src:Jt,alt:""},Tp=ot(()=>e("button",{class:"upload-btn font14 font-zhongcu",style:{background:"#ACD6F9"}},"学习中",-1)),Mp={key:2,class:"upload-step4"},Dp={class:"upload-step4-left"},Vp=ot(()=>e("div",{class:"font16 font-zhongcu"},"搜索测试",-1)),Ep={class:"upload-step4-left-btn-div font14"},Lp={class:"upload-step4-right"},zp=ot(()=>e("div",{class:"font16 font-zhongcu",style:{"margin-bottom":"25px"}},"测试结果",-1)),Fp={key:0,class:"upload-step4-right-result overflow-y"},Up={class:"result-item-top font12"},Pp={class:"result-item-content font14"},Rp={class:"result-item-bottom font12"},Np=["src"],qp={class:"file-name"},Kp={class:"breadcrumb-container"};function Hp(s,o,l,t,d,m){const u=_("BaseNavComponent"),r=_("ArrowLeftBold"),v=_("el-icon"),k=_("router-link"),h=_("DocProgressComponent"),g=_("el-upload"),x=_("el-button"),y=_("BreadCrumbComponent"),w=_("el-main"),T=_("el-container");return a(),c("div",Zm,[i(T,{style:{height:"100vh"},class:"el-container-base"},{default:p(()=>[i(u,{selectedBar:t.selectedBar},null,8,["selectedBar"]),i(w,{class:"el-main common-layout-bg"},{default:p(()=>[e("div",$m,[e("div",Ym,[e("div",jm,[t.selectedKid?(a(),X(k,{key:0,to:{name:"DocList",params:{kid:t.selectedKid}},class:"el-main-top-left-back"},{default:p(()=>[i(v,{color:"#666666",size:"16"},{default:p(()=>[i(r)]),_:1})]),_:1},8,["to"])):z("",!0),Q(" 本地文件上传 ")])]),e("div",Jm,[i(h,{step:t.step,typestr:"file"},null,8,["step"]),t.step==1?(a(),c("div",Gm,[e("div",Wm,[Xm,e("div",ep,[e("label",{onChange:o[0]||(o[0]=A=>t.chooseType("base")),class:W(["upload-step2-item-type",t.selectedType=="base"?"upload-step2-item-type-active":""])},[e("input",{type:"radio",name:"type",value:"base",checked:t.selectedType=="base"},null,8,tp),op],34),e("label",{onClick:o[1]||(o[1]=A=>t.chooseType("primary")),class:W(["upload-step2-item-type",t.selectedType=="primary"?"upload-step2-item-type-active":""])},[e("input",{type:"radio",name:"type",value:"primary",checked:t.selectedType=="primary"},null,8,sp),np],2),e("label",{onClick:o[2]||(o[2]=A=>t.chooseType("advance")),class:W(["upload-step2-item-type",t.selectedType=="advance"?"upload-step2-item-type-active":""])},[e("input",{type:"radio",name:"type",value:"advance",checked:t.selectedType=="advance"},null,8,ap),lp],2)])]),i(g,{class:"upload-demo upload-file-uploader",ref:"upload",drag:"","auto-upload":!1,limit:t.fileNum,multiple:"","file-list":t.fileList,"show-file-list":!1,accept:".txt, .docx, .pdf, .csv, .xlsx, .md, .pptx","on-change":t.handlChange,"on-exceed":t.handleExceed,"on-error":t.handleUploadError},{default:p(()=>[ip,e("div",cp,[rp,dp,e("div",up,"最多支持 "+S(t.fileNum)+" 个文件。单个文件最大 500MB",1)])]),_:1},8,["limit","file-list","on-change","on-exceed","on-error"]),t.fileList.length>0?(a(),c("div",mp,[pp,e("div",vp,[(a(!0),c(N,null,le(t.fileList,(A,C)=>(a(),c("div",{class:"el-col-item font14",key:C},[e("div",hp,S(C+1),1),e("div",fp,[e("img",{src:t.getFileIcon(A.name),alt:"file icon",class:"file-icon"},null,8,_p),e("span",gp,S(A.name),1)]),e("div",Ap,[i(x,{link:"",type:"primary",size:"default",class:"el-col-item-btn",onClick:L=>t.delDocment(A.uid)},{default:p(()=>[Q("删除")]),_:2},1032,["onClick"])])]))),128))])])):z("",!0),t.fileList.length>0?(a(),c("button",{key:1,class:"upload-btn font14 font-zhongcu",onClick:o[3]||(o[3]=(...A)=>t.addDocument&&t.addDocument(...A)),style:{"margin-top":"20px"}},"开始学习")):(a(),c("button",yp,"开始学习"))])):z("",!0),t.step==2?(a(),c("div",bp,[e("div",wp,[Cp,(a(!0),c(N,null,le(t.items,(A,C)=>(a(),c("div",{class:"el-col-item font14",key:C},[e("div",kp,S(C+1),1),e("div",xp,[e("img",{src:t.getFileIcon(A.title),alt:"file icon",class:"file-icon"},null,8,Sp),Q(" "+S(A.title),1)]),e("div",Ip,[Q(S(A.status_display)+" ",1),["wait","split","doing"].includes(A.status)?(a(),c("img",Bp)):z("",!0)])]))),128))]),Tp])):z("",!0),t.step==3?(a(),c("div",Mp,[e("div",Dp,[Vp,me(e("textarea",{"onUpdate:modelValue":o[4]||(o[4]=A=>t.content=A),class:"upload-step4-left-txt overflow-y font14 font-zhongcu",placeholder:"输入要测试的文本"},null,512),[[Ae,t.content]]),e("div",Ep,[t.selectedKid?(a(),X(k,{key:0,to:{name:"DocList",params:{kid:t.selectedKid}},class:"upload-step4-left-btn upload-btn common-cancel-btn"},{default:p(()=>[Q("跳过")]),_:1},8,["to"])):z("",!0),e("button",{type:"primary",color:"#129BFF",onClick:o[5]||(o[5]=(...A)=>t.searchTest&&t.searchTest(...A)),class:"upload-step4-left-btn upload-btn common-confirm-btn"},"测试")])]),e("div",Lp,[zp,t.results.length>0?(a(),c("div",Fp,[(a(!0),c(N,null,le(t.results,(A,C)=>(a(),c("div",{class:"result-item",key:C},[e("div",Up,"# "+S(C+1)+" | 语义检索 "+S(A.docfragment_score.toFixed(4)),1),e("div",Pp,S(A.docfragment),1),e("div",Rp,[e("img",{src:t.getFileIcon(A.document_type),alt:"file icon",class:"file-icon"},null,8,Np),e("span",qp,S(A.document_title)+"."+S(A.document_type),1)])]))),128))])):z("",!0)])])):z("",!0)])]),e("div",Kp,[i(y,{class:"breadcrumb",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})])}const Qp=ue(Om,[["render",Hp],["__scopeId","data-v-ddd9e0e2"]]),Op={name:"QueryappDetailView",components:{BaseNavComponent:Me,ElButton:mt,ElIcon:tt,Delete:It,ChatLineRound:Js,Position:js,Setting:Ys,Plus:hn,CloseBold:Lt,DepartmentPop:Us,CaretTop:vn,CaretBottom:pn,ArrowLeftBold:$e,BreadCrumbComponent:Ee,CirclePlus:vt,Loading:va,ThinkingBox:Ln},setup(){const s=n("queryapp"),o=De(),l=Ce(),t=n("setting"),d=n(null),m=n(""),u=n(""),r=n(""),v=n(""),k=n("给应用介绍一下"),h=n(""),g=n(""),x=n("点击输入历史消息限制条数"),y=n(0),w=n(!1),T=n("输入提示词"),A=n(null),C=n(null),L=n([]),B=n(null),P=n(!1),b=n(""),M=n(null),D=n(!0),V=n(!0),f=n(!1),I=n([]),U=n([]),J=n([]),O=n(!1),Y=n(""),R=n(""),j=n(1),ee=n("object"),K="开始参考资料分析",H=n(""),ne=n(!1),re=n(""),G=n(!1),ve=n(""),Z=n(""),se=n(!1),$=n(!1),ae=n(!1),te=n([]),de=n([]),he=n([]),Oe=n([]),Ge=n(!1),Nt=n(!1),ft=n(""),kt=n(""),_t=n(!0),to=n(""),rt=n(""),Vt=n(2.3),xt=n([]),gt=n([]),qt=n([]),So=n(!1),Io=n(!1),oo=n([]),so=n([]),At=n([]),Bo=n([]),Et=n(!1),To=n(!1),no=n([]),ao=n(!1),{processText:Zo,getThinkingContent:$o,hasThinkingContent:Yo,isThinkingCompleted:dt}=zn(),Kt=F=>{J.value=F,F.forEach(ce=>{U.value.push(ce.id)})},Mo=F=>{xt.value=F,F.forEach(ce=>{gt.value.push(ce.id)})},jo=F=>{so.value=F,F.forEach(ce=>{At.value.push(ce.id)})},lo=async()=>{try{const F=await cl(o.params.qid);F.data.error=="1"?l.push({name:"QueryAppList"}):(m.value=F.data.title,u.value=F.data.title,y.value=parseInt(F.data.status),A.value=F.data.prompt,rt.value=F.data.model_name?F.data.model_name:"默认模型",F.data.prompt_is_default&&F.data.prompt&&(T.value=F.data.prompt),F.data.history_limit_is_default?x.value=F.data.history_limit:(h.value=F.data.history_limit,g.value=F.data.history_limit),F.data.description_is_default?F.data.description&&(k.value=F.data.description):(r.value=F.data.description,v.value=F.data.description),C.value=F.data.opening_statement,he.value=F.data.siteusers,te.value=F.data.departments,ft.value=F.data.retrival_type,kt.value=F.data.retrival_type,to.value=F.data.embedding_model,Kt(F.data.kbases),Mo(F.data.querytools),jo(F.data.queryfiletemplates),F.data.status=="1"?V.value=!1:V.value=!0,Oe.value=he.value.map(ce=>ce.id),de.value=te.value.map(ce=>ce.id))}catch{}},St=()=>{w.value=!1,f.value=!1,So.value=!1,Io.value=!1,O.value=!1,v.value=r.value,u.value=m.value,kt.value=ft.value,g.value=h.value,Nt.value=!1,ae.value=!1},Jo=F=>{kt.value=F,ft.value=F},Go=async()=>{try{let F=0;g.value&&(F=g.value);const ce=await il(u.value,v.value,kt.value,d.value,F);ce.data.error=="0"?(E.success("保存成功"),r.value=v.value,m.value=u.value,h.value=g.value,lo()):ce.data.error=="403"&&E.error("暂无权限"),St()}finally{St()}},Wo=async()=>{try{const F=await wn(d.value);F.data.error=="0"?l.push({name:"QueryAppList"}):F.data.error=="403"&&E.error("暂无权限")}catch{}},Xo=async F=>{const ce=await rl(d.value,F);ce.data.error=="0"?(y.value=parseInt(F),F=="1"?V.value=!1:V.value=!0,Do()):ce.data.error=="403"?E.error("暂无权限"):E.error("保存失败")},es=()=>{l.push({name:"IndexWithQid",query:{qid:d.value}})},Do=async()=>{try{const F=he.value.map(Ue=>Ue.id).join("|"),ce=te.value.map(Ue=>Ue.id).join("|"),Se=rt.value==="默认模型"?"":rt.value,Te=await dl(d.value,A.value,C.value,U.value.join("|"),gt.value.join("|"),At.value.join("|"),F,ce,Se||"");Te.data.error=="0"?E.success("保存成功"):Te.data.error=="2"?E.error("暂无权限"):Te.data.error=="1"&&E.error("应用不存在")}finally{lo(),qe=0,Ve.value="",nt=0,Zt.value="",co()}},Ht=()=>{let F=0;H.value="";const ce=setInterval(()=>{F<K.length?(H.value+=K[F],F++):(clearInterval(ce),G.value=!0)},100)},ts=F=>{try{if(F.includes("error")){const ce=JSON.parse(F);return{topic_id:null,content:[{content:""}],taskmessage_id:"",error:ce.error}}else if(F.includes("topic_id")){const ce=JSON.parse(F),Se=ce.topic_id;M.value=Se;const Te=ce.content||"",Ue=ce.taskmessage_id;return{topic_id:Se,content:[{content:Te}],taskmessage_id:Ue}}else return{topic_id:null,content:F.match(/\{.*?}/g).map(Te=>JSON.parse(Te))}}catch{return{topic_id:null,content:"",taskmessage_id:""}}};Xe.setOptions({highlight:function(F,ce){return Ke.getLanguage(ce)?Ke.highlight(F,{language:ce}).value:Ke.highlightAuto(F).value}});const os=F=>Xe(F);Pe(b,F=>{F!==""?P.value=!0:P.value=!1});const ss=F=>{if(F.key==="Enter"){if(b.value.trim()===""){F.preventDefault();return}F.shiftKey||F.ctrlKey?(b.value+=`
`,F.preventDefault()):Vo()}},Vo=async()=>{const F=b.value,ce={role:"user",content:F,taskmessage_id:""};L.value.push(ce),Z.value="";let Se={role:"assistant",content:"",taskmessage_id:"",thinkingContent:"",thinkingCompleted:!1};L.value.push(Se),ut(),b.value="",P.value=!1,ne.value=!0,Et.value=!0,Ht(),_t.value=!1;try{const Te=await bn(F,j.value,d.value,M.value,ee.value);if(j.value==1)if(Te&&Te.getReader){const Ue=Te.getReader(),vs=new TextDecoder;let Zs=!1;(async()=>{for(;!Zs;){const{value:zo,done:Wn}=await Ue.read();if(Zs=Wn,zo){const Xn=vs.decode(zo,{stream:!0}),{content:ea,taskmessage_id:hs,error:ta}=ts(Xn);if(ta=="1"){L.value.pop(),E.error("应用不存在或无权限使用该应用"),ne.value=!1,G.value=!1,Et.value=!1,_t.value=!0;return}let $s="";ea.map(ro=>{if(ro.type==Ms.StartTool)ne.value=!0,To.value=!0,G.value=!0,H.value=ro.content;else if(ro.content!==""){ne.value&&(ne.value=!1),G.value&&(G.value=!1);const Fo=Se.taskmessage_id,oa=Zo(ro.content||"",Fo);$s+=oa.normalText,Yo(Fo)&&(Se.thinkingContent=$o(Fo),Se.thinkingCompleted=dt(Fo))}}),Z.value+=$s;try{Se.content=Z.value,hs&&(Se.taskmessage_id=hs,ce.taskmessage_id=hs),L.value=[...L.value],ut(),Se.content&&(ne.value=!1,_t.value=!0,G.value=!1)}catch{}}}Et.value=!1})().catch(zo=>{console.error("Stream error:",zo),ne.value=!1,G.value=!1,_t.value=!0,L.value.pop(),L.value.push({role:"assistant",content:"当前请求网络可能有问题，请重新发起对话"}),ut()})}else throw new Error("Invalid response format for stream mode");else if(_t.value=!0,Te.data.error=="1")E.error("应用不存在");else if(Te.data.error=="0"){ne.value=!1,Et.value=!1;const{topic_id:Ue,content:vs}=Te.data;L.value.push({role:"assistant",content:vs}),M.value=Ue,ut()}}catch(Te){console.log(Te),b.value="",P.value=!1,L.value.push({role:"assistant",content:"当前请求网络可能有问题，请重新发起对话"}),ut()}},ut=()=>{const F=B.value;F&&(F.scrollTop=F.scrollHeight)};Pe(L,async()=>{await pt(),document.querySelectorAll("pre code").forEach(F=>{F.dataset.highlighted||(Ke.highlightElement(F),F.dataset.highlighted="yes")})}),ha(()=>{ut(),window.innerWidth>=800&&window.innerWidth<=1600&&(Vt.value=1.5)});const ns=async()=>{const F=await fl();I.value=F.data},as=F=>{const ce=U.value.indexOf(F);ce!==-1?U.value.splice(ce,1):U.value.push(F)},ls=()=>{J.value=I.value.filter(F=>U.value.includes(F.id)),St()};ge(async()=>{d.value=o.params.qid,ut(),await lo();try{const ce=await tn(1,20,"");if(ce.data&&Array.isArray(ce.data.items)){const Se={id:-1,model_name:"默认模型",type:"",parameters:0};no.value=[Se,...ce.data.items]}}catch(ce){console.error("加载模型列表失败:",ce)}let F="应用";m.value&&(F=`应用（${m.value}）`),Bo.value=[{path:"/queryapp-list",name:F},{path:"",name:"配置"}],setTimeout(()=>{co()},1e3)});const is=F=>{se.value=ze(F,["del_aiserver"]),$.value=ze(F,["create_aiserver","manage_aiserver"])};function cs(F){Y.value=F}const rs=F=>{R.value=F},ds=F=>{const ce=he.value.findIndex(Se=>Se.id===F);ce!==-1&&he.value.splice(ce,1)},us=F=>{const ce=te.value.findIndex(Se=>Se.id===F);ce!==-1&&te.value.splice(ce,1)},Qt=()=>{ae.value=!ae.value},Ot=async()=>{const F=await Sl();qt.value=F.data},io=F=>{const ce=gt.value.indexOf(F);ce!==-1?gt.value.splice(ce,1):gt.value.push(F)},ms=()=>{xt.value=qt.value.filter(F=>gt.value.includes(F.id)),St()},Os=async()=>{const F=await Dl();oo.value=F.data},q=F=>{const ce=At.value.indexOf(F);ce!==-1?At.value.splice(ce,1):At.value.push(F)},pe=()=>{so.value=oo.value.filter(F=>At.value.includes(F.id)),St()},fe=()=>{Ge.value=!Ge.value},xe=(F,ce)=>{console.log(F,"8888888888888");const Se=F;he.value=[],Oe.value=[],Se.forEach(Ue=>{console.log(Ue,"新数据"),he.value.push(Ue),Oe.value.push(Ue.id)});const Te=ce;te.value=[],de.value=[],Te.forEach(Ue=>{te.value.push(Ue),de.value.push(Ue.id)})},Ve=n("");let qe=0,yt=null;const Zt=n("");let nt=0,ps=null;const co=()=>{let F=`Hello，${R.value}`;qe<F.length?(Ve.value+=F[qe],qe++,setTimeout(co,100)):(Eo(),clearInterval(yt))},Eo=()=>{let F="";C.value?F=C.value:F="很高兴见到你，开始探索吧！",nt<F.length?(Zt.value+=F[nt],nt++,setTimeout(Eo,100)):clearInterval(ps)};return{selectedBar:s,selectedKBaseNav:t,selectedQid:d,queryappTitle:m,queryappTitle1:u,queryappHistoryLimit1:g,queryappHistoryLimit:h,queryappHistoryLimitPlaceholder:x,ElButton:mt,Delete:It,ElIcon:tt,delQueryapp:Wo,queryappDesc:r,queryappDesc1:v,queryappDescPlaceholder:k,ChatLineRound:Js,Position:js,Setting:Ys,showModal:w,setStatus:Xo,toIndex:es,updateQueryapp:Do,promptPlaceholder:T,queryappPormpt:A,queryappStatement:C,scrollContainer:B,renderMarkdown:os,sendTaskmessageHandler:Vo,taskmesssageList:L,canSendMessage:P,userContent:b,successAlert:D,queryappStatus:y,fetchcreateQueryapp:Go,isUp:V,showModalKbase:f,fetchgetAllKBaseList:ns,kbaseList:I,selectedKid:U,chooseKbase:as,closeModal:St,saveKbase:ls,selectedKbaseList:J,showModalDel:O,nicknameOne:Y,handleNickname:rs,nickname:R,displayedText:re,showEllipsis:G,ellipsis:ve,showLoading:ne,handleDataPermissions:is,handleNicknameOne:cs,canDelAction:se,showAllSiteuser:Qt,pullDown:ae,siteuserList:he,departmentList:te,siteuserIds:Oe,departmentIds:de,showModalSiteuser:Nt,getFirstLetter:He,handleValueFromChild:xe,removeSiteuser:ds,removeDepartment:us,changeType:Jo,selectedType:ft,canWriteIpt:_t,queryappEmbeddingModel:to,minRowsAction:Vt,selectedToolList:xt,selectedTids:gt,getAppToolList:Ot,toolList:qt,showModalTool:So,chooseTool:io,saveTool:ms,canPerformAction:$,handleKeyDown:ss,showModalFileTemplate:Io,fileTemplateList:oo,selectedfileTemplateList:so,selectedFTids:At,getFileTemplateList:Os,chooseFileTemplate:q,saveFileTemplate:pe,breadcrumbs:Bo,showDepartmentPopModal:Ge,showDepartmentPop:fe,displayedTextLoading:H,isRendering:Et,showTool:To,displayedText1:Ve,displayedText2:Zt,modelName:rt,modelOptions:no,modelLoading:ao,handleModelSearch:async F=>{ao.value=!0;try{const ce=await tn(1,20,F||"");if(ce.data.data&&Array.isArray(ce.data.data)){const Se={id:-1,model_name:"默认模型",type:"",parameters:0};no.value=[Se,...ce.data.data]}}catch(ce){console.error("搜索模型失败:",ce)}finally{ao.value=!1}}}}},Fe=s=>(be("data-v-8dbb5ba8"),s=s(),we(),s),Zp={class:"common-layout",style:{height:"100vh"}},$p={class:"el-main-container"},Yp={class:"el-main-top"},jp={class:"el-main-top-left font18 font-zhongcu"},Jp={class:"el-main-bottom"},Gp={class:"el-aside2-top"},Wp=Fe(()=>e("div",{class:"font18 font-zhongcu"},"配置",-1)),Xp={class:"el-aside2-center font12 overflow-y",style:{height:"80%"}},ev=["placeholder"],tv={class:"el-aside2-center-item"},ov=Fe(()=>e("div",{class:"el-aside2-center-item-title"}," 关联知识库 ",-1)),sv={class:"el-aside2-center-item"},nv=Fe(()=>e("div",{class:"el-aside2-center-item-title"}," 关联工具 ",-1)),av={class:"el-aside2-center-item"},lv=Fe(()=>e("div",{class:"el-aside2-center-item-title"}," 关联文件模板 ",-1)),iv=Fe(()=>e("div",{class:"font14 font-zhongcu",style:{"margin-bottom":"15px"}}," AI 模型 ",-1)),cv={class:"field-model"},rv={class:"model-option-label"},dv=Fe(()=>e("div",{class:"font14 font-zhongcu",style:{margin:"15px 0"}}," 使用权限 ",-1)),uv={class:"field-siteuser"},mv={class:"siteuser-div"},pv={class:"siteuser-headimg"},vv={class:"siteuser-headimg"},hv={class:"el-aside2-bottom"},fv={class:"el-main2-top"},_v={style:{display:"flex","align-items":"center"}},gv=Fe(()=>e("div",{class:"font18 font-zhongcu"}," 预览 ",-1)),Av={class:"el-main2-top-status-div"},yv={key:0,class:"font12"},bv={key:1,class:"font12"},wv={class:"font14"},Cv={class:"taskmessage-right-bottom overflow-y",ref:"scrollContainer"},kv={key:0,class:"opening-statement-div"},xv={class:"font36"},Sv={class:"font24"},Iv={key:1,style:{"margin-top":"35px"}},Bv={class:"chat-item chat-item-opening"},Tv=Fe(()=>e("img",{src:Tt,alt:"",class:"chat-item-ai-headimg"},null,-1)),Mv={class:"chat-item-content opening-statement"},Dv={class:"opening-statement-title font18"},Vv=["innerHTML"],Ev={key:0,class:W(["chat-item","taskmessage-user"])},Lv={class:"siteuser-headimg font16"},zv={class:"chat-item-content taskmessage-user-content font14"},Fv=["innerHTML"],Uv={key:1,class:W(["chat-item","taskmessage-ai"])},Pv={class:"chat-item-ai-headimg-div"},Rv={key:0,src:Es,alt:"",style:{width:"25px",height:"25px"}},Nv={key:1,src:Tt,alt:"",class:"chat-item-ai-headimg"},qv={class:"chat-item-content taskmessage-ai-content"},Kv=["innerHTML"],Hv={key:1,class:"font14",style:{}},Qv={key:0,src:Jt,alt:"",style:{width:"18px",height:"18px"}},Ov={class:"el-main2-bottom"},Zv={class:"taskmessage-add"},$v=["disabled"],Yv=Fe(()=>e("img",{src:ho,alt:""},null,-1)),jv=[Yv],Jv=Fe(()=>e("div",{class:"font12",style:{color:"#666","text-align":"center","margin-top":"10px"}}," 内容由AI生成，无法确保真实准确，仅供参考，请阅读并遵守《AiALIGN用户协议》 ",-1)),Gv={class:"breadcrumb-container"},Wv={key:1,class:"new-modal-overlay"},Xv={class:"new-modal-container"},eh=Fe(()=>e("div",{class:"new-modal-top"},[e("div",{class:"font18 font-zhongcu"},"应用信息配置")],-1)),th={class:"new-modal-center"},oh={class:"new-modal-center-item"},sh=Fe(()=>e("label",{class:"font16 font-zhongcu"},"名称",-1)),nh={class:"new-modal-center-item"},ah=Fe(()=>e("label",{class:"font16 font-zhongcu"},"描述",-1)),lh=["placeholder"],ih={class:"new-modal-center-item"},ch=Fe(()=>e("label",{class:"font16 font-zhongcu"},"历史消息条数",-1)),rh=["placeholder"],dh={class:"new-modal-center-item"},uh=Fe(()=>e("label",{class:"font16 font-zhongcu"},"检索类型",-1)),mh={class:"modal-content-bottom-radio font14"},ph=["checked"],vh=["checked"],hh={class:"new-modal-bottom"},fh=Fe(()=>e("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu common-confirm-btn"}," 保存 ",-1)),_h={key:2,class:"new-modal-overlay"},gh={class:"new-modal-container",style:{width:"45%","max-width":"800px",border:"none"}},Ah=Fe(()=>e("div",{class:"new-modal-top"},[e("div",{class:"font18 font-zhongcu"},"关联知识库")],-1)),yh={class:"new-modal-center"},bh={class:"modal-kbase-list overflow-y"},wh=["onClick"],Ch={class:"el-col-left font18 font-zhongcu"},kh={class:"el-col-right"},xh={class:"font14 font-zhongcu overflow-one el-col-right-title"},Sh={class:"font12 overflow-one el-col-right-desc"},Ih={class:"el-col-left font18 font-zhongcu"},Bh={class:"el-col-right"},Th={class:"font14 font-zhongcu overflow-one el-col-right-title"},Mh={class:"font12 overflow-one el-col-right-desc"},Dh={class:"new-modal-bottom"},Vh={key:3,class:"new-modal-overlay"},Eh={class:"new-modal-container",style:{width:"45%","max-width":"800px",border:"none"}},Lh=Fe(()=>e("div",{class:"new-modal-top"},[e("div",{class:"font18 font-zhongcu"},"关联工具")],-1)),zh={class:"new-modal-center"},Fh={class:"modal-kbase-list overflow-y"},Uh=["onClick"],Ph={class:"el-col-left el-col-left-tool font18 font-zhongcu"},Rh={class:"el-col-right"},Nh={class:"font14 font-zhongcu overflow-one el-col-right-title"},qh={class:"font12 overflow-one el-col-right-desc"},Kh={class:"new-modal-bottom"},Hh={key:4,class:"new-modal-overlay"},Qh={class:"new-modal-container",style:{width:"45%","max-width":"800px",border:"none"}},Oh=Fe(()=>e("div",{class:"new-modal-top"},[e("div",{class:"font18 font-zhongcu"},"关联文件模板")],-1)),Zh={class:"new-modal-center"},$h={class:"modal-kbase-list overflow-y"},Yh=["onClick"],jh={class:"el-col-left el-col-left-file font18 font-zhongcu"},Jh={class:"el-col-right"},Gh={class:"font14 font-zhongcu overflow-one el-col-right-title"},Wh={class:"font12 overflow-one el-col-right-desc"},Xh={class:"new-modal-bottom"};function ef(s,o,l,t,d,m){const u=_("BaseNavComponent"),r=_("ArrowLeftBold"),v=_("el-icon"),k=_("router-link"),h=_("ChatLineRound"),g=_("el-button"),x=_("el-tooltip"),y=_("Setting"),w=_("Plus"),T=_("el-option"),A=_("el-select"),C=_("CirclePlus"),L=_("el-aside"),B=_("ThinkingBox"),P=_("el-input"),b=_("el-main"),M=_("el-container"),D=_("BreadCrumbComponent"),V=_("DepartmentPop");return a(),c(N,null,[e("div",Zp,[i(M,{style:{height:"100vh"},class:"el-container-base"},{default:p(()=>[i(u,{selectedBar:t.selectedBar,onDataPermissions:t.handleDataPermissions,onNicknameOne:t.handleNicknameOne,onNickname:t.handleNickname},null,8,["selectedBar","onDataPermissions","onNicknameOne","onNickname"]),i(b,{class:"el-main common-layout-bg"},{default:p(()=>[e("div",$p,[e("div",Yp,[e("div",jp,[i(k,{to:{name:"QueryAppList"},class:"el-main-top-left-back"},{default:p(()=>[i(v,{color:"#666666",size:"16"},{default:p(()=>[i(r)]),_:1})]),_:1}),Q(" "+S(t.queryappTitle?t.queryappTitle:"应用配置"),1)])]),e("div",Jp,[i(M,{class:"el-container2"},{default:p(()=>[i(L,{class:"el-aside2"},{default:p(()=>[e("div",Gp,[Wp,e("div",null,[i(x,{class:"box-item",effect:"dark",content:"对话",placement:"bottom"},{default:p(()=>[i(g,{class:"btn-default queryapp-btn",onClick:t.toIndex,disabled:t.queryappStatus==0||t.queryappStatus==2},{default:p(()=>[i(v,{size:"16"},{default:p(()=>[i(h)]),_:1})]),_:1},8,["onClick","disabled"])]),_:1}),i(x,{class:"box-item",effect:"dark",content:"设置",placement:"bottom"},{default:p(()=>[i(g,{class:"btn-default queryapp-btn",onClick:o[0]||(o[0]=f=>t.showModal=!0)},{default:p(()=>[i(v,{size:"16"},{default:p(()=>[i(y)]),_:1})]),_:1})]),_:1})])]),e("div",Xp,[me(e("textarea",{name:"prompt",id:"prompt",class:"el-aside2-center-txt",placeholder:t.promptPlaceholder,"onUpdate:modelValue":o[1]||(o[1]=f=>t.queryappPormpt=f),maxlength:"10000"},null,8,ev),[[Ae,t.queryappPormpt]]),e("div",tv,[ov,(a(!0),c(N,null,le(t.selectedKbaseList,f=>(a(),c("div",{class:"selected-kbase-item font10",key:f.id},S(f.title?f.title:"这个知识库还没有名称"),1))),128)),i(g,{class:"queryapp-btn",onClick:o[2]||(o[2]=f=>{t.fetchgetAllKBaseList(),t.showModalKbase=!0})},{default:p(()=>[i(v,null,{default:p(()=>[i(w)]),_:1})]),_:1})]),e("div",sv,[nv,(a(!0),c(N,null,le(t.selectedToolList,f=>(a(),c("div",{class:"selected-kbase-item font10",key:f.id},S(f.name?f.name:"这个工具还没有名称"),1))),128)),i(g,{class:"queryapp-btn",onClick:o[3]||(o[3]=f=>{t.getAppToolList(),t.showModalTool=!0})},{default:p(()=>[i(v,null,{default:p(()=>[i(w)]),_:1})]),_:1})]),e("div",av,[lv,(a(!0),c(N,null,le(t.selectedfileTemplateList,f=>(a(),c("div",{class:"selected-kbase-item font10",key:f.id},S(f.name?f.name:"这个文件模板还没有名称"),1))),128)),i(g,{class:"queryapp-btn",onClick:o[4]||(o[4]=f=>{t.getFileTemplateList(),t.showModalFileTemplate=!0})},{default:p(()=>[i(v,null,{default:p(()=>[i(w)]),_:1})]),_:1})]),me(e("textarea",{name:"statement",id:"statement",class:"el-aside2-center-txt",placeholder:"每次对话开始前，发送一个初始内容。支持标准Markdown语法，可使用的额外标记: [快捷按键]:用户点击后可以直接发送该问题","onUpdate:modelValue":o[5]||(o[5]=f=>t.queryappStatement=f),maxlength:"10000"},null,512),[[Ae,t.queryappStatement]]),iv,e("div",cv,[i(A,{modelValue:t.modelName,"onUpdate:modelValue":o[6]||(o[6]=f=>t.modelName=f),filterable:"",remote:"",clearable:"",placeholder:"请选择 AI 模型","remote-method":t.handleModelSearch,loading:t.modelLoading,class:"w-full","popper-class":"model-select-dropdown el-select-dropdown-100",onFocus:o[7]||(o[7]=f=>t.handleModelSearch(""))},{default:p(()=>[(a(!0),c(N,null,le(t.modelOptions,f=>(a(),X(T,{key:f.id,label:f.model_name,value:f.model_name},{default:p(()=>[e("span",rv,S(f.model_name),1)]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","remote-method","loading"])]),dv,e("div",uv,[e("div",mv,[(a(!0),c(N,null,le(t.siteuserList.slice(0,2),f=>(a(),c("div",{class:"siteuser-div-item font12 font-zhongcu",key:f.id},[e("div",pv,S(t.getFirstLetter(f.name)),1),Q(" "+S(f.name),1)]))),128)),(a(!0),c(N,null,le(t.departmentList.slice(0,2),f=>(a(),c("div",{class:"siteuser-div-item font12 font-zhongcu",key:f.id},[e("div",vv,S(t.getFirstLetter(f.name)),1),Q(" "+S(f.name),1)]))),128))]),i(v,{size:"20",color:"#575B66",class:"cursor",onClick:t.showDepartmentPop},{default:p(()=>[i(C)]),_:1},8,["onClick"])])]),e("div",hv,[t.canPerformAction?(a(),X(g,{key:0,type:"primary",color:"#129BFF",onClick:t.updateQueryapp,class:"save-btn font14 common-confirm-btn"},{default:p(()=>[Q("保存")]),_:1},8,["onClick"])):z("",!0)])]),_:1}),i(b,{class:"el-main2"},{default:p(()=>[e("div",fv,[e("div",_v,[gv,e("div",Av,[e("div",{class:W(t.queryappStatus=="1"?"el-main2-top-status-div-success":"")},null,2),t.queryappStatus=="1"?(a(),c("span",yv,"已发布")):(a(),c("span",bv,"未发布"))])]),e("div",wv,[i(g,{class:W(["el-main2-top-btn",t.queryappStatus!="1"?"el-main2-top-btn-disabled":"common-confirm-btn"]),disabled:t.queryappStatus==0||t.queryappStatus==2,onClick:t.toIndex},{default:p(()=>[Q("对话")]),_:1},8,["class","disabled","onClick"]),t.queryappStatus==0||t.queryappStatus==2?(a(),X(g,{key:0,class:"el-main2-top-btn common-confirm-btn",onClick:o[8]||(o[8]=f=>t.setStatus("1"))},{default:p(()=>[Q("上架")]),_:1})):z("",!0),t.queryappStatus==1?(a(),X(g,{key:1,class:"el-main2-top-btn el-main2-top-btn-xiajia common-cancel-btn",onClick:o[9]||(o[9]=f=>t.setStatus("2"))},{default:p(()=>[Q("下架")]),_:1})):z("",!0)])]),e("div",Cv,[t.taskmesssageList.length<=0?(a(),c("div",kv,[e("div",xv,S(t.displayedText1),1),e("div",Sv,S(t.displayedText2),1)])):(a(),c("div",Iv,[e("div",Bv,[Tv,e("div",Mv,[e("div",Dv," 你好，我是"+S(t.queryappTitle),1),t.queryappStatement?(a(),c("div",{key:0,class:"opening-statement-desc font14",innerHTML:t.renderMarkdown(t.queryappStatement)},null,8,Vv)):z("",!0)])]),(a(!0),c(N,null,le(t.taskmesssageList,(f,I)=>(a(),c(N,{key:I},[f.role==="user"?(a(),c("div",Ev,[e("div",Lv,S(t.nicknameOne),1),e("div",zv,[e("div",{class:"font14",innerHTML:t.renderMarkdown(f.content)},null,8,Fv)])])):f.role==="assistant"?(a(),c("div",Uv,[e("div",Pv,[t.isRendering&&I==t.taskmesssageList.length-1?(a(),c("img",Rv)):(a(),c("img",Nv))]),e("div",qv,[f.thinkingContent?(a(),X(B,{key:0,content:f.thinkingContent,completed:f.thinkingCompleted,style:{"margin-top":"0","margin-bottom":"10px"}},null,8,["content","completed"])):z("",!0),e("div",{class:"font14",innerHTML:t.renderMarkdown(f.content)},null,8,Kv),t.showLoading&&I==t.taskmesssageList.length-1?(a(),c("div",Hv,[Q(S(t.displayedTextLoading)+" ",1),t.showEllipsis?(a(),c("img",Qv)):z("",!0)])):z("",!0)])])):z("",!0)],64))),128))]))],512),e("div",Ov,[e("div",Zv,[e("div",{class:W(t.canSendMessage?"":"taskmessage-ipt-no")},[i(P,{modelValue:t.userContent,"onUpdate:modelValue":o[10]||(o[10]=f=>t.userContent=f),autosize:{minRows:1,maxRows:6},type:"textarea",placeholder:"输入问题",resize:"none",disabled:!t.canWriteIpt,maxlength:"20000",onKeydown:t.handleKeyDown},null,8,["modelValue","disabled","onKeydown"]),e("button",{class:W(t.canSendMessage?"":"send-btn-no"),disabled:!t.canSendMessage,onClick:o[11]||(o[11]=(...f)=>t.sendTaskmessageHandler&&t.sendTaskmessageHandler(...f))},jv,10,$v)],2)]),Jv])]),_:1})]),_:1})])]),e("div",Gv,[i(D,{class:"breadcrumb",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})]),t.showDepartmentPopModal?(a(),X(V,{key:0,type:"queryapp",sids:t.siteuserIds,dids:t.departmentIds,onValueFromChild:t.handleValueFromChild,closeModal:t.showDepartmentPop},null,8,["sids","dids","onValueFromChild","closeModal"])):z("",!0),t.showModal?(a(),c("div",Wv,[e("div",Xv,[eh,e("form",{onSubmit:o[18]||(o[18]=_e((...f)=>t.fetchcreateQueryapp&&t.fetchcreateQueryapp(...f),["prevent"]))},[e("div",th,[e("div",oh,[sh,me(e("input",{id:"title",class:"field-ipt font14 font-zhongcu","onUpdate:modelValue":o[12]||(o[12]=f=>t.queryappTitle1=f),placeholder:"点击输入名字",required:"",maxlength:"50"},null,512),[[Ae,t.queryappTitle1]])]),e("div",nh,[ah,me(e("textarea",{id:"description",class:"field-ipt field-txt font14 font-zhongcu","onUpdate:modelValue":o[13]||(o[13]=f=>t.queryappDesc1=f),placeholder:t.queryappDescPlaceholder,required:"",maxlength:"100"},null,8,lh),[[Ae,t.queryappDesc1]])]),e("div",ih,[ch,me(e("input",{type:"number",class:"field-ipt font14 font-zhongcu","onUpdate:modelValue":o[14]||(o[14]=f=>t.queryappHistoryLimit1=f),placeholder:t.queryappHistoryLimitPlaceholder},null,8,rh),[[Ae,t.queryappHistoryLimit1]])]),e("div",dh,[uh,e("div",mh,[e("label",{onChange:o[15]||(o[15]=f=>t.changeType("base")),class:W(t.selectedType=="base"?"modal-content-bottom-radio-selected":"")},[e("input",{type:"radio",name:"retrival_type",value:"base",checked:t.selectedType=="base",style:{width:"15px",height:"15px"}},null,8,ph),Q(" 基础索引 ")],34),e("label",{onChange:o[16]||(o[16]=f=>t.changeType("advance")),class:W(t.selectedType=="advance"?"modal-content-bottom-radio-selected":"")},[e("input",{type:"radio",name:"retrival_type",value:"advance",checked:t.selectedType=="advance",style:{width:"15px",height:"15px"}},null,8,vh),Q(" 高级索引 ")],34)])])]),e("div",hh,[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:o[17]||(o[17]=(...f)=>t.closeModal&&t.closeModal(...f))}," 取消 "),fh])],32)])])):z("",!0),t.showModalKbase?(a(),c("div",_h,[e("div",gh,[Ah,e("div",yh,[e("div",bh,[(a(!0),c(N,null,le(t.kbaseList,f=>(a(),c(N,{key:f.id},[t.queryappEmbeddingModel==f.embedding_type?(a(),c("div",{key:0,onClick:I=>t.chooseKbase(f.id),class:W(["modal-kabse-item",t.selectedKid.includes(f.id)?"modal-kabse-item-selected":""])},[e("div",Ch,S(t.getFirstLetter(f.title)),1),e("div",kh,[e("div",xh,S(f.title),1),e("div",Sh,S(f.description?f.description:"这个知识库还没有介绍～"),1)])],10,wh)):(a(),c("div",{key:1,class:W(["modal-kabse-item",t.selectedKid.includes(f.id)?"modal-kabse-item-selected":""]),style:{cursor:"not-allowed"}},[e("div",Ih,S(t.getFirstLetter(f.title)),1),e("div",Bh,[e("div",Th,S(f.title),1),e("div",Mh,S(f.description?f.description:"这个知识库还没有介绍～"),1)])],2))],64))),128))])]),e("div",Dh,[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:o[19]||(o[19]=(...f)=>t.closeModal&&t.closeModal(...f))}," 取消 "),e("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu common-confirm-btn",onClick:o[20]||(o[20]=(...f)=>t.saveKbase&&t.saveKbase(...f))}," 保存 ")])])])):z("",!0),t.showModalTool?(a(),c("div",Vh,[e("div",Eh,[Lh,e("div",zh,[e("div",Fh,[(a(!0),c(N,null,le(t.toolList,f=>(a(),c("div",{key:f.id,onClick:I=>t.chooseTool(f.id),class:W(["modal-kabse-item",t.selectedTids.includes(f.id)?"modal-kabse-item-selected":""])},[e("div",Ph,S(t.getFirstLetter(f.name)),1),e("div",Rh,[e("div",Nh,S(f.name),1),e("div",qh,S(f.description?f.description:"这个工具还没有介绍～"),1)])],10,Uh))),128))])]),e("div",Kh,[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:o[21]||(o[21]=(...f)=>t.closeModal&&t.closeModal(...f))}," 取消 "),e("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu common-confirm-btn",onClick:o[22]||(o[22]=(...f)=>t.saveTool&&t.saveTool(...f))}," 保存 ")])])])):z("",!0),t.showModalFileTemplate?(a(),c("div",Hh,[e("div",Qh,[Oh,e("div",Zh,[e("div",$h,[(a(!0),c(N,null,le(t.fileTemplateList,f=>(a(),c("div",{key:f.id,onClick:I=>t.chooseFileTemplate(f.id),class:W(["modal-kabse-item",t.selectedFTids.includes(f.id)?"modal-kabse-item-selected":""])},[e("div",jh,S(t.getFirstLetter(f.name)),1),e("div",Jh,[e("div",Gh,S(f.name),1),e("div",Wh,S(f.description?f.description:"这个模板还没有介绍～"),1)])],10,Yh))),128))])]),e("div",Xh,[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:o[23]||(o[23]=(...f)=>t.closeModal&&t.closeModal(...f))}," 取消 "),e("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu common-confirm-btn",onClick:o[24]||(o[24]=(...f)=>t.saveFileTemplate&&t.saveFileTemplate(...f))}," 保存 ")])])])):z("",!0)],64)}const tf=ue(Op,[["render",ef],["__scopeId","data-v-8dbb5ba8"]]),of={props:{selectedBar:{type:String,required:!0}},setup(){return{}}},Rs=s=>(be("data-v-2f5da738"),s=s(),we(),s),sf={class:"el-main-top"},nf=Rs(()=>e("div",{class:"el-main-top-link-fenge"},null,-1)),af=Rs(()=>e("div",{class:"el-main-top-link-fenge"},null,-1)),lf=Rs(()=>e("div",{class:"el-main-top-link-fenge"},null,-1));function cf(s,o,l,t,d,m){const u=_("router-link");return a(),c("div",sf,[i(u,{to:{name:"Organization"},class:W(["el-main-top-link",l.selectedBar=="zuzhi"?"el-main-top-link-selected":""])},{default:p(()=>[Q("组织结构"),nf]),_:1},8,["class"]),i(u,{to:{name:"SiteuserList"},class:W(["el-main-top-link",l.selectedBar=="renyuan"?"el-main-top-link-selected":""])},{default:p(()=>[Q("人员"),af]),_:1},8,["class"]),i(u,{to:{name:"DepartmentList"},class:W(["el-main-top-link",l.selectedBar=="bumen"?"el-main-top-link-selected":""])},{default:p(()=>[Q("部门"),lf]),_:1},8,["class"])])}const Ut=ue(of,[["render",cf],["__scopeId","data-v-2f5da738"]]),rf={name:"SiteuserListView",components:{BaseNavComponennt:Me,OrganizationBaseNav:Ut,BreadCrumbComponent:Ee},setup(){const s=Ce(),o=n("siteuser"),l=n(!1),t=n([]),d=n(1),m=n(10),u=n(0),r=n(!1),v=n("永久删除员工"),k=n("该员工将被永久删除，不可恢复及撤销。确定要删除吗？"),h=n(null),g=n([{path:"/siteuser-list/",name:"应用管理"},{path:"",name:"人员"}]),x=async(B,P)=>{try{const b=await gi(B,P);b.data.error=="403"?s.push({name:"Index"}):(t.value=b.data.data,u.value=b.data.total_num[0])}catch{}},y=B=>{d.value=B,x(d.value,m.value)},w={backgroundColor:"#F9F9F9",color:"#000",border:"none",fontWeight:"normal",fontSize:"18px",padding:"20px 0"};ge(()=>{x(d.value,m.value)});const T=B=>{l.value=ze(B,["manage_siteuser"])},A=B=>{r.value=!0,h.value=B},C=()=>{r.value=!1,h.value=null},L=Ze(async()=>{r.value=!1;const B=await Ai(h.value);B.data.error=="0"?(E.success("删除成功"),x(d.value,m.value)):B.data.message?E.error("删除失败，"+B.data.message):E.error("删除失败，人员不存在")});return{selectedBar:o,canPerformAction:l,handleDataPermissions:T,headerCellStyle:w,handlePageChange:y,pageSize:m,totalItems:u,items:t,currentPage:d,showDelModal:r,modalDelTitle:v,modalDelMessage:k,delSiteuser:A,handleClose:C,handleConfirm:L,breadcrumbs:g}}},Ao=s=>(be("data-v-d880a758"),s=s(),we(),s),df={class:"common-layout",style:{height:"100vh"}},uf={class:"el-main-container"},mf={class:"el-main-top"},pf=Ao(()=>e("div",{class:"el-main-top-left font18 font-zhongcu"}," 人员 ",-1)),vf={key:0},hf={class:"el-main-bottom overflow-y"},ff={class:"el-main-list"},_f={class:"el-col-head font14"},gf=Ao(()=>e("div",{style:{width:"20%"}},"姓名",-1)),Af=Ao(()=>e("div",{style:{width:"20%"}},"联系电话",-1)),yf=Ao(()=>e("div",{style:{width:"20%"}},"是否有效",-1)),bf=Ao(()=>e("div",{style:{width:"40%"}},"部门",-1)),wf={key:0,style:{width:"20%"},class:"caozuo"},Cf={class:"el-col-body"},kf={style:{width:"20%"}},xf={style:{width:"20%"}},Sf={style:{width:"20%"}},If={style:{width:"40%"},class:"overflow-one"},Bf={key:0,style:{width:"20%"},class:"caozuo"},Tf={class:"breadcrumb-container"};function Mf(s,o,l,t,d,m){const u=_("BaseNavComponennt"),r=_("router-link"),v=_("el-button"),k=_("el-pagination"),h=_("BreadCrumbComponent"),g=_("el-main"),x=_("el-container"),y=_("newComfirmsModal");return a(),c(N,null,[e("div",df,[i(x,{style:{height:"100vh"},class:"el-container-base"},{default:p(()=>[i(u,{selectedBar:t.selectedBar,onDataPermissions:t.handleDataPermissions},null,8,["selectedBar","onDataPermissions"]),i(g,{class:"el-main common-layout-bg"},{default:p(()=>[e("div",uf,[e("div",mf,[pf,t.canPerformAction?(a(),c("div",vf,[i(r,{to:{name:"SiteuserAdd"},class:"el-main-add-btn font12 common-confirm-btn"},{default:p(()=>[Q("添加成员")]),_:1})])):z("",!0)]),e("div",hf,[e("div",ff,[e("div",_f,[gf,Af,yf,bf,t.canPerformAction?(a(),c("div",wf,"操作")):z("",!0)]),e("div",Cf,[(a(!0),c(N,null,le(t.items,(w,T)=>(a(),c("div",{class:"el-col-item font14",key:T},[e("div",kf,S(w.nickname),1),e("div",xf,S(w.tel),1),e("div",Sf,[w.is_valid?(a(),c(N,{key:0},[Q("是")],64)):(a(),c(N,{key:1},[Q("否")],64))]),e("div",If,[(a(!0),c(N,null,le(w.departments,(A,C)=>(a(),c("span",{key:A.id},[Q(S(A.name)+" ",1),C<w.departments.length-1?(a(),c(N,{key:0},[Q("、")],64)):z("",!0)]))),128))]),t.canPerformAction?(a(),c("div",Bf,[i(r,{to:{name:"SiteuserAdd",params:{sid:w.id}},class:"el-col-item-btn"},{default:p(()=>[Q("修改")]),_:2},1032,["to"]),i(v,{link:"",type:"primary",size:"default",class:"el-col-item-btn",onClick:A=>t.delSiteuser(w.id)},{default:p(()=>[Q("删除")]),_:2},1032,["onClick"]),i(r,{to:{name:"SiteuserPositionList",params:{sid:w.id}},class:"el-col-item-btn"},{default:p(()=>[Q("岗位")]),_:2},1032,["to"])])):z("",!0)]))),128))])]),i(k,{background:"",layout:"prev, pager, next",total:t.totalItems,"page-size":t.pageSize,"current-page":t.currentPage,onCurrentChange:t.handlePageChange,"hide-on-single-page":!0},null,8,["total","page-size","current-page","onCurrentChange"])])]),e("div",Tf,[i(h,{class:"breadcrumb",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})]),i(y,{show:t.showDelModal,title:t.modalDelTitle,message:t.modalDelMessage,onClose:t.handleClose,onConfirm:t.handleConfirm},null,8,["show","title","message","onClose","onConfirm"])],64)}const Df=ue(rf,[["render",Mf],["__scopeId","data-v-d880a758"]]),Vf={name:"DepartmentListView",components:{BaseNavComponennt:Me,OrganizationBaseNav:Ut,BreadCrumbComponent:Ee},setup(){const s=Ce(),o=n("department"),l=n(!1),t=n([]),d=n(1),m=n(10),u=n(0),r=n(null),v=n([{path:"/department-list/",name:"应用管理"},{path:"",name:"部门"}]),k=async(B,P)=>{try{const b=await ui(B,P);b.data.error=="403"?s.push({name:"Index"}):(t.value=b.data.data,u.value=b.data.total_num[0])}catch{}},h=B=>{d.value=B,k(d.value,m.value)},g={backgroundColor:"#F9F9F9",color:"#000",border:"none",fontWeight:"normal",fontSize:"18px",padding:"20px 0"};ge(()=>{k(d.value,m.value)});const x=B=>{l.value=ze(B,["manage_siteuser"])},y=B=>{w.value=!0,r.value=B},w=n(!1),T=n("永久删除部门"),A=n("该部门将被永久删除，不可恢复及撤销。确定要删除吗？"),C=()=>{w.value=!1,r.value=null},L=Ze(async()=>{w.value=!1;const B=await mi(r.value);B.data.error=="0"?(E.success("删除成功"),k(d.value,m.value)):B.data.message?E.error("删除失败，"+B.data.message):E.error("删除失败，部门不存在")});return{selectedBar:o,canPerformAction:l,handleDataPermissions:x,headerCellStyle:g,handlePageChange:h,pageSize:m,totalItems:u,items:t,paginatedData:t,currentPage:d,getFirstLetter:He,handleClose:C,handleConfirm:L,delDepartment:y,showDelModal:w,modalDelTitle:T,modalDelMessage:A,breadcrumbs:v}}},yo=s=>(be("data-v-c3d32585"),s=s(),we(),s),Ef={class:"common-layout",style:{height:"100vh"}},Lf={class:"el-main-container"},zf={class:"el-main-top"},Ff=yo(()=>e("div",{class:"el-main-top-left font18 font-zhongcu"}," 部门 ",-1)),Uf={key:0},Pf={class:"el-main-bottom overflow-y"},Rf={class:"el-main-list"},Nf={class:"el-col-head font14"},qf=yo(()=>e("div",{style:{width:"15%"}},"部门名称",-1)),Kf=yo(()=>e("div",{style:{width:"15%"}},"上级部门",-1)),Hf=yo(()=>e("div",{style:{width:"10%"}},"部门人数",-1)),Qf=yo(()=>e("div",{style:{width:"30%"}},"部门管理员",-1)),Of={key:0,style:{width:"20%"},class:"caozuo"},Zf={class:"el-col-body"},$f={style:{width:"15%"}},Yf={style:{width:"15%"}},jf={style:{width:"10%"}},Jf={style:{width:"30%",display:"flex"},class:"overflow-one"},Gf={class:"siteuser-headimg"},Wf={class:"siteuser-nickname"},Xf={key:0,style:{width:"20%"},class:"caozuo"},e2={class:"breadcrumb-container"};function t2(s,o,l,t,d,m){const u=_("BaseNavComponennt"),r=_("router-link"),v=_("el-button"),k=_("el-pagination"),h=_("BreadCrumbComponent"),g=_("el-main"),x=_("el-container"),y=_("newComfirmsModal");return a(),c(N,null,[e("div",Ef,[i(x,{style:{height:"100vh"},class:"el-container-base"},{default:p(()=>[i(u,{selectedBar:t.selectedBar,onDataPermissions:t.handleDataPermissions},null,8,["selectedBar","onDataPermissions"]),i(g,{class:"el-main common-layout-bg"},{default:p(()=>[e("div",Lf,[e("div",zf,[Ff,t.canPerformAction?(a(),c("div",Uf,[i(r,{to:{name:"DepartmentAdd"},class:"el-main-add-btn font12 common-confirm-btn"},{default:p(()=>[Q("新建部门")]),_:1})])):z("",!0)]),e("div",Pf,[e("div",Rf,[e("div",Nf,[qf,Kf,Hf,Qf,t.canPerformAction?(a(),c("div",Of,"操作")):z("",!0)]),e("div",Zf,[(a(!0),c(N,null,le(t.items,(w,T)=>(a(),c("div",{class:"el-col-item font14",key:T},[e("div",$f,S(w.name),1),e("div",Yf,[w.father?We(s.$slots,"default",{key:0},()=>[Q(S(w.father.name),1)],!0):We(s.$slots,"default",{key:1},()=>[Q("-")],!0)]),e("div",jf,S(w.siteuser_count),1),e("div",Jf,[(a(!0),c(N,null,le(w.leaders,A=>(a(),c("div",{class:"sidebar-siteuser",key:A.id},[e("div",Gf,S(t.getFirstLetter(A.name)),1),e("div",Wf,S(A.name),1)]))),128))]),t.canPerformAction?(a(),c("div",Xf,[i(r,{to:{name:"DepartmentAdd",params:{did:w.id}},class:"el-col-item-btn"},{default:p(()=>[Q("修改")]),_:2},1032,["to"]),i(v,{link:"",type:"primary",size:"default",class:"el-col-item-btn",onClick:A=>t.delDepartment(w.id)},{default:p(()=>[Q("删除")]),_:2},1032,["onClick"]),i(r,{to:{name:"DepartmentPositionList",params:{did:w.id}},class:"el-col-item-btn"},{default:p(()=>[Q("岗位")]),_:2},1032,["to"])])):z("",!0)]))),128))])]),i(k,{background:"",layout:"prev, pager, next",total:t.totalItems,"page-size":t.pageSize,"current-page":t.currentPage,onCurrentChange:t.handlePageChange,"hide-on-single-page":!0},null,8,["total","page-size","current-page","onCurrentChange"])])]),e("div",e2,[i(h,{class:"breadcrumb",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:3})]),_:3})]),i(y,{show:t.showDelModal,title:t.modalDelTitle,message:t.modalDelMessage,onClose:t.handleClose,onConfirm:t.handleConfirm},null,8,["show","title","message","onClose","onConfirm"])],64)}const o2=ue(Vf,[["render",t2],["__scopeId","data-v-c3d32585"]]),s2=ye({components:{},props:{selectedDepartmentId:{type:Number,default:null},pageStr:{type:String,default:null}},emits:["valueSelectedDepartment"],setup(s,{emit:o}){const l=n(null),t=n(null),d=n([]),m=n(null),u=n([]),r=Ce(),v={children:"children",label:"label"},k=async()=>{try{const A=await di();if(A.data.error=="0"?d.value=A.data.departments:r.push({name:"Index"}),s.selectedDepartmentId){await pt();const C=h(d.value,s.selectedDepartmentId);C&&(T(C),m.value.setCurrentKey(C.id),l.value=C)}else if(s.pageStr!="renyuan"&&d.value.length>0){const C=d.value[0];m.value.setCurrentKey(C.id),g(C)}}catch{}},h=(A,C)=>{for(const L of A){if(L.id===C)return L;if(L.children){const B=h(L.children,C);if(B)return B}}return null};Pe(()=>s.selectedDepartmentId,async A=>{if(A){const C=h(d.value,A);C&&(await pt(),T(C),m.value.setCurrentKey(C.id),l.value=C)}});const g=A=>{l.value=A,t.value=A.id,o("valueSelectedDepartment",A.id,A.label)},x=A=>{u.value.includes(A.id)||u.value.push(A.id)},y=A=>{const C=u.value.indexOf(A.id);C!==-1&&u.value.splice(C,1)},w=(A,{node:C,data:L})=>A("span",{style:{color:l.value&&l.value.id===L.id?"#129BFF":"inherit"}},[A("span",C.label)]),T=async A=>{const C=[];let L=A;for(;L;)C.unshift(L.id),L=L.parent;u.value=C};return ge(()=>{k()}),{selectedDid:t,treeDepartment:d,defaultProps:v,handleNodeClick:g,handleNodeExpand:x,handleNodeCollapse:y,renderContent:w,treeRef:m,fetchGetAllDepartments:k,expandToNode:T,expandedKeys:u}}});function n2(s,o,l,t,d,m){const u=_("el-tree"),r=_("el-aside");return a(),X(r,{width:"300px",class:"el-aside-tree"},{default:p(()=>[i(u,{style:{"max-width":"100%",color:"#666",background:"none"},class:"font14",ref:"treeRef",data:s.treeDepartment,props:s.defaultProps,"node-key":"id","default-expanded-keys":s.expandedKeys,"render-content":s.renderContent,onNodeClick:s.handleNodeClick,onNodeExpand:s.handleNodeExpand,onNodeCollapse:s.handleNodeCollapse},null,8,["data","props","default-expanded-keys","render-content","onNodeClick","onNodeExpand","onNodeCollapse"])]),_:1})}const Ns=ue(s2,[["render",n2],["__scopeId","data-v-e26842da"]]),a2={name:"OrganizationView",components:{BaseNavComponennt:Me,DepartmentTreeComponent:Ns,OrganizationBaseNav:Ut,BreadCrumbComponent:Ee},setup(){const s=Ce(),o=n("zuzhi"),l=n(!1),t=n(null),d=n(null),m=n(0),u=n([]),r=n(1),v=n(10),k=n(0),h=n([{path:"/organization",name:"应用管理"},{path:"",name:"组织"}]),g=async(A,C,L)=>{try{const B=await hi(A,C,L);B.data.error=="403"?s.push({name:"Index"}):(u.value=B.data.data,k.value=B.data.total_num[0],m.value=B.data.total_num[0])}catch{}};return{selectedBar:o,handleDataPermissions:A=>{l.value=ze(A,["manage_siteuser"])},handleValueSelectedDepartment:(A,C)=>{t.value=A,d.value=C,g(t.value,r.value,v.value)},canPerformAction:l,selectedDid:t,selectedDepartmentName:d,totalSiteuserName:m,headerCellStyle:{backgroundColor:"#F9F9F9",color:"#000",border:"none",fontWeight:"normal",fontSize:"18px",padding:"20px 0"},handlePageChange:A=>{r.value=A,g(t.value,r.value,v.value)},pageSize:v,totalItems:k,items:u,currentPage:r,breadcrumbs:h}}},bo=s=>(be("data-v-013610ed"),s=s(),we(),s),l2={class:"common-layout",style:{height:"100vh"}},i2={class:"el-main-container"},c2=bo(()=>e("div",{class:"el-main-top"},[e("div",{class:"el-main-top-left font18 font-zhongcu"}," 组织 ")],-1)),r2={class:"el-main-bottom"},d2={class:"el-main2-title font14"},u2=bo(()=>e("span",{style:{color:"#666666"}},"总人数",-1)),m2={class:"el-main2-bottom overflow-y",style:{"max-height":"90%"}},p2={class:"el-main-list"},v2={class:"el-col-head font14"},h2=bo(()=>e("div",{style:{width:"20%"}},"姓名",-1)),f2=bo(()=>e("div",{style:{width:"30%"}},"联系电话",-1)),_2=bo(()=>e("div",{style:{width:"30%"}},"是否有效",-1)),g2={key:0,style:{width:"20%"},class:"caozuo"},A2={class:"el-col-body"},y2={style:{width:"20%"}},b2={style:{width:"30%"}},w2={style:{width:"30%"}},C2={key:0,style:{width:"20%"},class:"caozuo"},k2={class:"breadcrumb-container"};function x2(s,o,l,t,d,m){const u=_("BaseNavComponennt"),r=_("DepartmentTreeComponent"),v=_("router-link"),k=_("el-pagination"),h=_("el-main"),g=_("el-container"),x=_("BreadCrumbComponent");return a(),c("div",l2,[i(g,{style:{height:"100vh"},class:"el-container-base"},{default:p(()=>[i(u,{selectedBar:t.selectedBar,onDataPermissions:t.handleDataPermissions},null,8,["selectedBar","onDataPermissions"]),i(h,{class:"el-main common-layout-bg"},{default:p(()=>[e("div",i2,[c2,e("div",r2,[i(g,{style:{height:"100%","margin-top":"15px"}},{default:p(()=>[i(r,{onValueSelectedDepartment:t.handleValueSelectedDepartment,ref:"treeRef"},null,8,["onValueSelectedDepartment"]),i(h,{class:"el-main2"},{default:p(()=>[e("div",d2,[e("div",null,S(t.selectedDepartmentName),1),e("div",null,[u2,Q(" "+S(t.totalSiteuserName),1)])]),e("div",m2,[e("div",p2,[e("div",v2,[h2,f2,_2,t.canPerformAction?(a(),c("div",g2,"操作")):z("",!0)]),e("div",A2,[(a(!0),c(N,null,le(t.items,(y,w)=>(a(),c("div",{class:"el-col-item font14",key:w},[e("div",y2,S(y.nickname),1),e("div",b2,S(y.tel),1),e("div",w2,[y.is_valid?(a(),c(N,{key:0},[Q("是")],64)):(a(),c(N,{key:1},[Q("否")],64))]),t.canPerformAction?(a(),c("div",C2,[i(v,{to:{name:"SiteuserAdd",params:{sid:y.id}},class:"el-col-item-btn"},{default:p(()=>[Q("修改")]),_:2},1032,["to"])])):z("",!0)]))),128))])]),i(k,{background:"",layout:"prev, pager, next",total:t.totalItems,"page-size":t.pageSize,"current-page":t.currentPage,onCurrentChange:t.handlePageChange,"hide-on-single-page":!0},null,8,["total","page-size","current-page","onCurrentChange"])])]),_:1})]),_:1})])]),e("div",k2,[i(x,{class:"breadcrumb",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})])}const S2=ue(a2,[["render",x2],["__scopeId","data-v-013610ed"]]);var $t=(s=>(s.Tool="tool",s.Template="template",s))($t||{}),ys=(s=>(s.Tool="tool",s.Template="template",s.KB="KBase",s))(ys||{});const Kn=s=>(be("data-v-68dbf9f3"),s=s(),we(),s),I2={key:0,class:"modal-del"},B2={class:"modal-del-container"},T2=Kn(()=>e("div",{class:"modal-del-container-top font-zhongcu font18"},"保存提醒",-1)),M2=Kn(()=>e("div",{class:"modal-del-container-center font-zhongcu font14"},"即将离开此页面，是否保存更改？",-1)),D2={class:"modal-del-container-bottom font14"},V2=ye({__name:"BackTip",props:fa({type:{},confirmFun:{type:Function}},{show:{type:Boolean},showModifiers:{}}),emits:["update:show"],setup(s){const o=s,l=As(s,"show"),t=()=>{l.value=!1},d=()=>{o.confirmFun(),l.value=!1};return o.type==ys.Tool||o.type==ys.Template,(m,u)=>l.value?(a(),c("div",I2,[e("div",B2,[T2,M2,e("div",D2,[e("button",{type:"button",onClick:_e(t,["stop"]),class:"common-cancel-btn"},"取消"),e("button",{type:"button",onClick:d,class:"common-confirm-btn"},"确认")])])])):z("",!0)}}),E2=ue(V2,[["__scopeId","data-v-68dbf9f3"]]),L2={class:"flex"},z2={class:"cc-page-title-text flex items-center font-zhongcu"},F2=ye({__name:"HeaderLeft",props:{to:{},text:{},type:{},submit:{type:Function}},setup(s){const o=s,l=Ce(),t=n(!1),d=()=>{t.value=!0};return Pe(t,(m,u)=>{m===!1&&u&&l.push(o.to)}),(m,u)=>{const r=_("el-icon");return a(),c(N,null,[e("div",L2,[e("div",{onClick:_e(d,["stop"]),class:"el-main-top-left-back cursor-pointer"},[i(r,{color:"#666666",size:"16"},{default:p(()=>[i(oe($e))]),_:1})]),e("div",z2,[e("div",null,S(o.text),1)])]),i(E2,{show:t.value,"onUpdate:show":u[0]||(u[0]=v=>t.value=v),"confirm-fun":m.submit,type:"tool"},null,8,["show","confirm-fun"])],64)}}}),Gt=ue(F2,[["__scopeId","data-v-2c1f1834"]]),U2={name:"SiteuserAddView",components:{BaseNavComponennt:Me,OrganizationBaseNav:Ut,ArrowLeftBold:$e,BreadCrumbComponent:Ee,HeaderLeft:Gt},setup(){const s=n("siteuser"),o=n(null),l=n(null),t=n(null),d=n(null),m=n(!0),u=n("user"),r=n([{key:"create_aiserver",label:"创建/编辑应用"},{key:"del_aiserver",label:"删除应用"},{key:"manage_aiserver",label:"管理所有应用"},{key:"create_docbase",label:"创建/编辑知识库"},{key:"manage_docbase",label:"管理所有知识库"},{key:"manage_siteuser",label:"管理部门和员工"},{key:"create_tool",label:"创建/编辑api工具"},{key:"manage_tool",label:"管理所有工具"},{key:"create_account",label:"创建/编辑公司"},{key:"del_account",label:"删除公司"},{key:"create_opportunity",label:"创建/编辑商机"},{key:"del_opportunity",label:"删除商机"},{key:"view_totaldata",label:"查看crm总数据"},{key:"create_project",label:"创建/编辑项目"},{key:"del_project",label:"删除项目"},{key:"create_project_fund",label:"创建/编辑回款"},{key:"del_project_fund",label:"删除回款"}]),v=n([]),k=n(!1),h=De(),g=Ce(),x=n(null),y=n([]),w=A=>{u.value=A};return ge(async()=>{if(h.params.sid){x.value=h.params.sid;const A=await Dn(x.value);if(A.data.error=="0"){const C=A.data;o.value=C.username,t.value=C.nickname,d.value=C.tel,m.value=C.is_valid,u.value=C.role,v.value=C.permissions.split("|")}}y.value=[{path:"/siteuser-list/",name:"应用管理"},{path:"/siteuser-list/",name:"人员"},{path:"",name:x.value?"修改人员":"添加人员"}]}),{selectedBar:s,siteuserUsername:o,siteuserPassword:l,siteuserNickname:t,siteuserTel:d,siteuserIsValid:m,changeRole:w,siteuserRole:u,pubilcPermission:r,selectedPermissions:v,saveSiteuser:async()=>{if(k.value)return;k.value=!0;const A=m.value?1:0;try{if(x.value){const C=await wi(x.value,o.value,l.value,t.value,d.value,A,u.value,v.value.join("|"));C.data.error=="0"?g.push({name:"SiteuserList"}):E.error("修改失败，"+C.data.message)}else{const C=await bi(o.value,l.value,t.value,d.value,A,u.value,v.value.join("|"));C.data.error=="0"?g.push({name:"SiteuserList"}):E.error("新建失败，"+C.data.message)}}finally{k.value=!1}},sid:x,breadcrumbs:y}}},it=s=>(be("data-v-dbbd6fea"),s=s(),we(),s),P2={class:"common-layout",style:{height:"100vh"}},R2={class:"el-main-container overflow-y",style:{"padding-bottom":"20px"}},N2={class:"el-main-top"},q2={class:"el-main-bottom",style:{"margin-top":"30px"}},K2=it(()=>e("div",{class:"el-main-title"},"基本信息",-1)),H2={style:{display:"flex","flex-wrap":"wrap"}},Q2={class:"el-main-item"},O2=it(()=>e("div",{class:"el-main-item-title font14 font-zhongcu"},"账号",-1)),Z2={class:"el-main-item",style:{"margin-bottom":"10px"}},$2=it(()=>e("div",{class:"el-main-item-title font14 font-zhongcu"},"密码",-1)),Y2=["placeholder"],j2={key:0,style:{"font-size":"12px",color:"#666","margin-top":"5px"}},J2=["placeholder"],G2={style:{display:"flex","flex-wrap":"wrap"}},W2={class:"el-main-item"},X2=it(()=>e("div",{class:"el-main-item-title font14 font-zhongcu"},"姓名",-1)),e0={class:"el-main-item"},t0=it(()=>e("div",{class:"el-main-item-title font14 font-zhongcu"},"手机号",-1)),o0={class:"file-ipt-div"},s0=it(()=>e("div",{class:"file-ipt-div-left"},"+86",-1)),n0={class:"el-main-item"},a0=it(()=>e("div",{class:"el-main-item-title font14 font-zhongcu"},"是否有效",-1)),l0=it(()=>e("div",{class:"el-main-title"},"权限信息",-1)),i0={class:"el-main-item"},c0=it(()=>e("div",{class:"el-main-item-title font14 font-zhongcu"},"角色",-1)),r0={class:"el-main-item-radio-div font12"},d0=["checked"],u0=["checked"],m0={class:"el-main-item"},p0=it(()=>e("div",{class:"el-main-item-title font14 font-zhongcu"},"权限",-1)),v0=it(()=>e("div",{class:"el-main-footer"},[e("button",{type:"submit",class:"el-main-footer-btn font14 font-zhongcu common-confirm-btn"},"保存")],-1)),h0={class:"breadcrumb-container"};function f0(s,o,l,t,d,m){const u=_("BaseNavComponennt"),r=_("HeaderLeft"),v=_("el-switch"),k=_("el-transfer"),h=_("BreadCrumbComponent"),g=_("el-main"),x=_("el-container");return a(),c("div",P2,[i(x,{style:{height:"100vh"},class:"el-container-base"},{default:p(()=>[i(u,{selectedBar:t.selectedBar},null,8,["selectedBar"]),i(g,{class:"el-main common-layout-bg"},{default:p(()=>[e("div",R2,[e("div",N2,[i(r,{to:{name:"SiteuserList"},text:t.sid?"修改人员":"添加人员",submit:t.saveSiteuser},null,8,["text","submit"])]),e("div",q2,[e("form",{onSubmit:o[9]||(o[9]=_e((...y)=>t.saveSiteuser&&t.saveSiteuser(...y),["prevent"]))},[K2,e("div",H2,[e("div",Q2,[O2,me(e("input",{type:"text",placeholder:"请创建账号",class:"file-ipt",maxlength:"20","onUpdate:modelValue":o[0]||(o[0]=y=>t.siteuserUsername=y),required:""},null,512),[[Ae,t.siteuserUsername]])]),e("div",Z2,[$2,t.sid?We(s.$slots,"default",{key:0},()=>[me(e("input",{type:"password",placeholder:t.sid?"重置密码请输入":"请创建密码",class:"file-ipt",maxlength:"20","onUpdate:modelValue":o[1]||(o[1]=y=>t.siteuserPassword=y)},null,8,Y2),[[Ae,t.siteuserPassword]]),t.sid?(a(),c("div",j2,"*重置密码请重新输入，无需重置密码请留空")):z("",!0)],!0):We(s.$slots,"default",{key:1},()=>[me(e("input",{type:"password",placeholder:t.sid?"重置密码请输入":"请创建密码",class:"file-ipt",maxlength:"20","onUpdate:modelValue":o[2]||(o[2]=y=>t.siteuserPassword=y),required:""},null,8,J2),[[Ae,t.siteuserPassword]])],!0)])]),e("div",G2,[e("div",W2,[X2,me(e("input",{type:"text",placeholder:"请创建姓名",class:"file-ipt",maxlength:"20","onUpdate:modelValue":o[3]||(o[3]=y=>t.siteuserNickname=y),required:""},null,512),[[Ae,t.siteuserNickname]])]),e("div",e0,[t0,e("div",o0,[s0,me(e("input",{type:"text",placeholder:"请输入手机号",class:"file-ipt",maxlength:"20","onUpdate:modelValue":o[4]||(o[4]=y=>t.siteuserTel=y)},null,512),[[Ae,t.siteuserTel]])])])]),e("div",n0,[a0,i(v,{modelValue:t.siteuserIsValid,"onUpdate:modelValue":o[5]||(o[5]=y=>t.siteuserIsValid=y),size:"large"},null,8,["modelValue"])]),l0,e("div",i0,[c0,e("div",r0,[e("label",{onChange:o[6]||(o[6]=y=>t.changeRole("user")),class:W(t.siteuserRole=="user"?"label-selected":"")},[e("input",{type:"radio",name:"role",value:"user",checked:t.siteuserRole=="user",style:{width:"15px",height:"15px"}},null,8,d0),Q(" 普通用户 ")],34),e("label",{onChange:o[7]||(o[7]=y=>t.changeRole("super")),class:W(t.siteuserRole=="super"?"label-selected":"")},[e("input",{type:"radio",name:"role",value:"super",checked:t.siteuserRole=="super",style:{width:"15px",height:"15px"}},null,8,u0),Q(" 超级管理员 ")],34)])]),e("div",m0,[p0,i(k,{modelValue:t.selectedPermissions,"onUpdate:modelValue":o[8]||(o[8]=y=>t.selectedPermissions=y),data:t.pubilcPermission,titles:["全选","全选"]},null,8,["modelValue","data"])]),v0],32)])]),e("div",h0,[i(h,{class:"breadcrumb",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:3})]),_:3})])}const _0=ue(U2,[["render",f0],["__scopeId","data-v-dbbd6fea"]]),g0={name:"DepartmentAddView",components:{BaseNavComponennt:Me,OrganizationBaseNav:Ut,ArrowLeftBold:$e,DepartmentTreeComponent:Ns,BreadCrumbComponent:Ee,HeaderLeft:Gt},setup(){const s=De(),o=Ce(),l=n("department"),t=n(null),d=n(null),m=n(null),u=n(null),r=n(!1),v=n(!1),k=n([]);return ge(async()=>{if(s.params.did){t.value=s.params.did;const y=await Sn(t.value);y.data.error=="0"&&(u.value=y.data.name,y.data.father&&(d.value=y.data.father.id,m.value=y.data.father.name))}k.value=[{path:"/department-list/",name:"应用管理"},{path:"/department-list/",name:"部门"},{path:"",name:t.value?"修改部门":"添加部门"}]}),{selectedBar:l,did:t,fatherId:d,fatherName:m,name:u,saveDepartment:async()=>{if(!r.value){if(r.value=!0,u.value==""||!u.value){E.error("请填写部门名称"),r.value=!1;return}try{if(t.value){const y=await vi(t.value,u.value);y.data.error=="0"?o.push({name:"DepartmentList"}):E.error("修改失败，"+y.data.message)}else{const y=await pi(d.value,u.value);y.data.error=="0"?o.push({name:"DepartmentList"}):E.error("新建失败，"+y.data.message)}}catch{}finally{r.value=!1}}},handleValueSelectedDepartment:(y,w)=>{d.value=y,m.value=w,v.value=!1},showDepartment:v,toggleDepartmentTree:()=>{v.value=!v.value},breadcrumbs:k}}},Ho=s=>(be("data-v-6dc8c2b8"),s=s(),we(),s),A0={class:"common-layout",style:{height:"100vh"}},y0={class:"el-main-container",style:{position:"relative"}},b0={class:"el-main-top"},w0={class:"el-main-bottom",style:{"margin-top":"30px"}},C0=Ho(()=>e("div",{class:"el-main-title"},"部门信息",-1)),k0={class:"el-main-item"},x0=Ho(()=>e("div",{class:"el-main-item-title font14 font-zhongcu"},"部门名称",-1)),S0={key:0,class:"el-main-item"},I0=Ho(()=>e("div",{class:"el-main-item-title font14 font-zhongcu"},"上级部门",-1)),B0={key:1,class:"el-main-item"},T0=Ho(()=>e("div",{class:"el-main-item-title font14 font-zhongcu"},"上级部门",-1)),M0={key:0,class:"el-main-department-div"},D0={class:"el-main-footer"},V0={class:"breadcrumb-container"};function E0(s,o,l,t,d,m){const u=_("BaseNavComponennt"),r=_("HeaderLeft"),v=_("DepartmentTreeComponent"),k=_("BreadCrumbComponent"),h=_("el-main"),g=_("el-container");return a(),c("div",A0,[i(g,{style:{height:"100vh"},class:"el-container-base"},{default:p(()=>[i(u,{selectedBar:t.selectedBar},null,8,["selectedBar"]),i(h,{class:"el-main common-layout-bg"},{default:p(()=>[e("div",y0,[e("div",b0,[i(r,{to:{name:"DepartmentList"},text:t.did?"修改部门":"添加部门",submit:t.saveDepartment},null,8,["text","submit"])]),e("div",w0,[C0,e("div",null,[e("div",k0,[x0,me(e("input",{type:"text",placeholder:"请输入部门名称",class:"file-ipt","onUpdate:modelValue":o[0]||(o[0]=x=>t.name=x),maxlength:"10"},null,512),[[Ae,t.name]])]),t.did?(a(),c("div",S0,[I0,me(e("input",{type:"text",disabled:"",class:"file-ipt","onUpdate:modelValue":o[1]||(o[1]=x=>t.fatherName=x)},null,512),[[Ae,t.fatherName]])])):(a(),c("div",B0,[T0,e("div",null,[e("div",{class:"file-ipt",onClick:o[2]||(o[2]=_e((...x)=>t.toggleDepartmentTree&&t.toggleDepartmentTree(...x),["stop"]))},S(t.fatherName),1),t.showDepartment?(a(),c("div",M0,[i(v,{selectedDepartmentId:t.fatherId,pageStr:"renyuan",onValueSelectedDepartment:t.handleValueSelectedDepartment},null,8,["selectedDepartmentId","onValueSelectedDepartment"])])):z("",!0)])]))]),e("div",D0,[e("button",{type:"button",onClick:o[3]||(o[3]=(...x)=>t.saveDepartment&&t.saveDepartment(...x)),class:"el-main-footer-btn font14 font-zhongcu common-confirm-btn"},"保存")])])]),e("div",V0,[i(k,{class:"breadcrumb",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})])}const L0=ue(g0,[["render",E0],["__scopeId","data-v-6dc8c2b8"]]),z0={name:"DepartmentPositionListView",components:{BaseNavComponennt:Me,OrganizationBaseNav:Ut,ArrowLeftBold:$e,CloseBold:Lt,ElSelect:ga,ElOption:_a,BreadCrumbComponent:Ee},setup(){const s=De(),o=Ce(),l=n("department"),t=n(null),d=n(null),m=n(!1),u=n([]),r=n(1),v=n(10),k=n(0),h=n(!1),g=n(!1),x=n("永久删除员工岗位"),y=n("该员工岗位将被永久删除，不可恢复及撤销。确定要删除吗？"),w=n("添加岗位"),T=n(!0),A=n(!0),C=n(null),L=n(null),B=n(!1),P=n(null),b=n([]),M=n(null),D=n(!1),V=n("输入员工名字"),f=n([{path:"/department-list/",name:"应用管理"},{path:"/department-list/",name:"部门"},{path:"",name:"部门岗位"}]),I={backgroundColor:"#F9F9F9",color:"#000",border:"none",fontWeight:"normal",fontSize:"18px",padding:"20px 0"},U=G=>{m.value=ze(G,["manage_siteuser"])},J=async(G,ve,Z)=>{try{const se=await fi(G,ve,Z);se.data.error=="403"?o.push({name:"Index"}):(u.value=se.data.data,k.value=se.data.total_num[0])}catch{}},O=G=>{r.value=G,J(t.value,r.value,v.value)};ge(async()=>{if(s.params.did){t.value=s.params.did;const G=await Sn(t.value);G.data.error=="0"&&(d.value=G.data.name,J(t.value,r.value,v.value))}});const Y=G=>{h.value=!0,C.value=G},R=()=>{h.value=!1,C.value=null},j=Ze(async()=>{h.value=!1;const G=await In(C.value);G.data.error=="0"?(E.success("删除成功"),J(t.value,r.value,v.value)):G.data.message?E.error("删除失败，"+G.data.message):E.error("删除失败")}),ee=async G=>{C.value=G,w.value="修改岗位";try{const ve=await Bn(C.value);if(ve.data.error=="0"){B.value=!0;const Z=ve.data;T.value=Z.is_able,A.value=Z.is_leader,L.value=Z.name,P.value=Z.siteuser}else ve.data.message&&E.error(ve.data.message)}catch{}},K=()=>{B.value=!1,P.value=null,C.value=null,T.value=!0,A.value=!0,L.value=null,V.value="输入员工名字",M.value=null},H=()=>{B.value=!0,w.value="添加岗位"},ne=Ze(async()=>{if(g.value)return;g.value=!0,B.value=!1;const G=T.value?1:0,ve=A.value?1:0;if(C.value)try{if(L.value){const Z=await Tn(C.value,L.value,G,ve);Z.data.error=="0"?(E.success("修改成功"),J(t.value,r.value,v.value)):Z.data.message&&E.error(Z.data.message)}else E.error("请将信息填写完整")}finally{g.value=!1,K()}else try{if(M.value&&L.value){const Z=await Mn(t.value,M.value,L.value,G,ve);Z.data.error=="0"?(E.success("添加成功"),J(t.value,r.value,v.value)):Z.data.message&&E.error(Z.data.message)}else E.error("请将信息填写完整")}finally{g.value=!1,K()}});return{headerCellStyle:I,selectedBar:l,departmentId:t,departmentName:d,handleDataPermissions:U,canPerformAction:m,handlePageChange:O,pageSize:v,totalItems:k,items:u,currentPage:r,showDelModal:h,handleClose:R,handleConfirm:j,modalDelTitle:x,modalDelMessage:y,delPosition:Y,modalTitle:w,is_able:T,is_leader:A,positionName:L,editPosition:ee,showModal:B,siteuser:P,closeModal:K,submitPosition:ne,addPosition:H,options:b,sid:M,loading:D,remoteMethod:async G=>{if(G){D.value=!0;try{const ve=await _i(G);b.value=ve.data}finally{D.value=!1}}else b.value=[]},placeholder:V,breadcrumbs:f}}},wt=s=>(be("data-v-60a19478"),s=s(),we(),s),F0={class:"common-layout",style:{height:"100vh"}},U0={class:"el-main-container"},P0={class:"el-main-top"},R0=wt(()=>e("div",{class:"el-main-top-left font18 font-zhongcu"}," 部门岗位 ",-1)),N0={key:0},q0={class:"el-main-bottom overflow-y"},K0={class:"el-main-list"},H0={class:"el-col-head font14"},Q0=wt(()=>e("div",{style:{width:"25%"}},"人员",-1)),O0=wt(()=>e("div",{style:{width:"25%"}},"岗位",-1)),Z0=wt(()=>e("div",{style:{width:"25%"}},"是否可用",-1)),$0={key:0,style:{width:"15%"},class:"caozuo"},Y0={class:"el-col-body"},j0={style:{width:"25%"}},J0={style:{width:"25%"}},G0={style:{width:"25%"}},W0={key:0,style:{width:"15%"},class:"caozuo"},X0={class:"breadcrumb-container"},e5={key:0,class:"new-modal-overlay"},t5={class:"new-modal-container"},o5={class:"new-modal-top"},s5={class:"font18 font-zhongcu"},n5={class:"new-modal-center"},a5={class:"new-modal-center-item"},l5={class:"font16 font-zhongcu"},i5={class:"new-modal-center-item"},c5=wt(()=>e("label",{class:"font16 font-zhongcu"},"人员",-1)),r5={key:0,style:{"margin-top":"10px"},class:"font14 font-zhongcu"},d5={class:"new-modal-center-item"},u5=wt(()=>e("label",{class:"font16 font-zhongcu"},"岗位",-1)),m5={class:"new-modal-center-item"},p5=wt(()=>e("label",{class:"font16 font-zhongcu"},"是否有效",-1)),v5={class:"new-modal-center-item"},h5=wt(()=>e("label",{class:"font16 font-zhongcu"},"是否为管理员",-1)),f5={class:"new-modal-bottom"},_5=wt(()=>e("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu common-confirm-btn"},"保存",-1));function g5(s,o,l,t,d,m){const u=_("BaseNavComponennt"),r=_("el-button"),v=_("el-pagination"),k=_("BreadCrumbComponent"),h=_("el-main"),g=_("el-container"),x=_("newComfirmsModal"),y=_("el-option"),w=_("el-select"),T=_("el-switch");return a(),c(N,null,[e("div",F0,[i(g,{style:{height:"100vh"},class:"el-container-base"},{default:p(()=>[i(u,{selectedBar:t.selectedBar,onDataPermissions:t.handleDataPermissions},null,8,["selectedBar","onDataPermissions"]),i(h,{class:"el-main common-layout-bg"},{default:p(()=>[e("div",U0,[e("div",P0,[R0,t.canPerformAction?(a(),c("div",N0,[e("button",{class:"el-main-add-btn font12 common-confirm-btn",onClick:o[0]||(o[0]=(...A)=>t.addPosition&&t.addPosition(...A))},"添加岗位")])):z("",!0)]),e("div",q0,[e("div",K0,[e("div",H0,[Q0,O0,Z0,t.canPerformAction?(a(),c("div",$0,"操作")):z("",!0)]),e("div",Y0,[(a(!0),c(N,null,le(t.items,(A,C)=>(a(),c("div",{class:"el-col-item font14",key:C},[e("div",j0,S(A.siteuser.name),1),e("div",J0,S(A.name),1),e("div",G0,[A.is_able?(a(),c(N,{key:0},[Q("是")],64)):(a(),c(N,{key:1},[Q("否")],64))]),t.canPerformAction?(a(),c("div",W0,[i(r,{link:"",type:"primary",size:"default",class:"el-col-item-btn",onClick:L=>t.editPosition(A.id)},{default:p(()=>[Q("修改")]),_:2},1032,["onClick"]),i(r,{link:"",type:"primary",size:"default",class:"el-col-item-btn",onClick:L=>t.delPosition(A.id)},{default:p(()=>[Q("删除")]),_:2},1032,["onClick"])])):z("",!0)]))),128))])]),i(v,{background:"",layout:"prev, pager, next",total:t.totalItems,"page-size":t.pageSize,"current-page":t.currentPage,onCurrentChange:t.handlePageChange,"hide-on-single-page":!0},null,8,["total","page-size","current-page","onCurrentChange"])])]),e("div",X0,[i(k,{class:"breadcrumb",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})]),i(x,{show:t.showDelModal,title:t.modalDelTitle,message:t.modalDelMessage,onClose:t.handleClose,onConfirm:t.handleConfirm},null,8,["show","title","message","onClose","onConfirm"]),t.showModal?(a(),c("div",e5,[e("div",t5,[e("div",o5,[e("div",s5,S(t.modalTitle),1)]),e("form",{onSubmit:o[6]||(o[6]=_e((...A)=>t.submitPosition&&t.submitPosition(...A),["prevent"]))},[e("div",n5,[e("div",a5,[e("label",l5,S(t.departmentName),1)]),e("div",i5,[c5,t.siteuser?(a(),c("div",r5,S(t.siteuser.name),1)):(a(),X(w,{key:1,modelValue:t.sid,"onUpdate:modelValue":o[1]||(o[1]=A=>t.sid=A),filterable:"",remote:"","reserve-keyword":"",placeholder:t.placeholder,"remote-method":t.remoteMethod,loading:t.loading,size:"large",class:"field-select font14 font-zhongcu"},{default:p(()=>[(a(!0),c(N,null,le(t.options,A=>(a(),X(y,{key:A.id,label:A.name,value:A.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","remote-method","loading"]))]),e("div",d5,[u5,me(e("input",{class:"field-ipt font14 font-zhongcu","onUpdate:modelValue":o[2]||(o[2]=A=>t.positionName=A),placeholder:"点击输入岗位",required:"",maxlength:"10"},null,512),[[Ae,t.positionName]])]),e("div",m5,[p5,i(T,{modelValue:t.is_able,"onUpdate:modelValue":o[3]||(o[3]=A=>t.is_able=A),size:"large"},null,8,["modelValue"])]),e("div",v5,[h5,i(T,{modelValue:t.is_leader,"onUpdate:modelValue":o[4]||(o[4]=A=>t.is_leader=A),size:"large"},null,8,["modelValue"])])]),e("div",f5,[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:o[5]||(o[5]=(...A)=>t.closeModal&&t.closeModal(...A))},"取消"),_5])],32)])])):z("",!0)],64)}const A5=ue(z0,[["render",g5],["__scopeId","data-v-60a19478"]]),y5={name:"SiteuserPositionListView",components:{BaseNavComponennt:Me,OrganizationBaseNav:Ut,ArrowLeftBold:$e,CloseBold:Lt,DepartmentTreeComponent:Ns,BreadCrumbComponent:Ee},setup(){const s=De(),o=Ce(),l=n("siteuser"),t=n(null),d=n(null),m=n(!1),u=n([]),r=n(1),v=n(10),k=n(0),h=n(!1),g=n(!1),x=n("永久删除员工岗位"),y=n("该员工岗位将被永久删除，不可恢复及撤销。确定要删除吗？"),w=n("添加岗位"),T=n(!0),A=n(!0),C=n(null),L=n(null),B=n(!1),P=n(null),b=n(null),M=n(null),D=n(!1),V=n([{path:"/siteuser-list/",name:"应用管理"},{path:"/siteuser-list/",name:"人员"},{path:"",name:"人员岗位"}]),f=re=>{m.value=ze(re,["manage_siteuser"])},I=async(re,G,ve)=>{try{const Z=await yi(re,G,ve);Z.data.error=="403"?o.push({name:"Index"}):(u.value=Z.data.data,k.value=Z.data.total_num[0])}catch{}},U=re=>{r.value=re,I(t.value,r.value,v.value)};ge(async()=>{if(s.params.sid){t.value=s.params.sid;const re=await Dn(t.value);re.data.error=="0"&&(d.value=re.data.nickname,I(t.value,r.value,v.value))}});const J=re=>{h.value=!0,C.value=re},O=()=>{h.value=!1,C.value=null},Y=Ze(async()=>{if(!g.value){g.value=!0,h.value=!1;try{const re=await In(C.value);re.data.error=="0"?(E.success("删除成功"),I(t.value,r.value,v.value)):re.data.message?E.error("删除失败，"+re.data.message):E.error("删除失败")}finally{g.value=!1}}}),R=async re=>{C.value=re,w.value="修改岗位";try{const G=await Bn(C.value);if(G.data.error=="0"){B.value=!0;const ve=G.data;T.value=ve.is_able,A.value=ve.is_leader,L.value=ve.name,M.value=ve.department}else G.data.message&&E.error(G.data.message)}catch{}},j=()=>{B.value=!1,C.value=null,T.value=!0,A.value=!0,L.value=null,b.value=null,P.value=null},ee=()=>{B.value=!0,w.value="添加岗位"},K=Ze(async()=>{if(g.value)return;g.value=!0,B.value=!1;const re=T.value?1:0,G=A.value?1:0;if(C.value)try{if(L.value){const ve=await Tn(C.value,L.value,re,G);ve.data.error=="0"?(E.success("修改成功"),I(t.value,r.value,v.value)):ve.data.message&&E.error(ve.data.message)}else E.error("请将信息填写完整")}finally{g.value=!1,j()}else try{if(P.value&&L.value){const ve=await Mn(P.value,t.value,L.value,re,G);ve.data.error=="0"?(E.success("添加成功"),I(t.value,r.value,v.value)):ve.data.message&&E.error(ve.data.message)}else E.error("请将信息填写完整")}finally{g.value=!1,j()}});return{selectedBar:l,siteuserId:t,siteuserName:d,handleDataPermissions:f,canPerformAction:m,handlePageChange:U,pageSize:v,totalItems:k,items:u,paginatedData:u,currentPage:r,showDelModal:h,handleClose:O,handleConfirm:Y,modalDelTitle:x,modalDelMessage:y,delPosition:J,modalTitle:w,is_able:T,is_leader:A,positionName:L,editPosition:R,showModal:B,closeModal:j,submitPosition:K,addPosition:ee,departmentId:P,departmentName:b,department:M,showDepartment:D,handleValueSelectedDepartment:(re,G)=>{P.value=re,b.value=G,D.value=!1},toggleDepartmentTree:()=>{D.value=!D.value},breadcrumbs:V}}},ht=s=>(be("data-v-17205764"),s=s(),we(),s),b5={class:"common-layout common-layout-bg",style:{height:"100vh"}},w5={class:"el-main-container"},C5={class:"el-main-top"},k5=ht(()=>e("div",{class:"el-main-top-left font18 font-zhongcu"}," 人员岗位 ",-1)),x5={key:0},S5={class:"el-main-bottom overflow-y"},I5={class:"el-main-list"},B5={class:"el-col-head font14"},T5=ht(()=>e("div",{style:{width:"25%"}},"部门",-1)),M5=ht(()=>e("div",{style:{width:"25%"}},"岗位",-1)),D5=ht(()=>e("div",{style:{width:"25%"}},"是否可用",-1)),V5={key:0,style:{width:"15%"},class:"caozuo"},E5={class:"el-col-body"},L5={style:{width:"25%"}},z5={style:{width:"25%"}},F5={style:{width:"25%"}},U5={key:0,style:{width:"15%"},class:"caozuo"},P5={class:"breadcrumb-container"},R5={key:0,class:"new-modal-overlay"},N5={class:"new-modal-container"},q5={class:"new-modal-top"},K5={class:"font18 font-zhongcu"},H5={class:"new-modal-center"},Q5={class:"new-modal-center-item"},O5={class:"font16 font-zhongcu"},Z5={class:"new-modal-center-item"},$5=ht(()=>e("label",{class:"font16 font-zhongcu"},"部门",-1)),Y5={key:0,style:{"margin-top":"10px"},class:"font14 font-zhongcu"},j5={key:1,style:{position:"relative"}},J5=ht(()=>e("span",{style:{color:"#d9d9d9"}},"请选择部门",-1)),G5={key:0,class:"el-main-department-div"},W5={class:"new-modal-center-item"},X5=ht(()=>e("label",{class:"font16 font-zhongcu"},"岗位",-1)),e_={class:"new-modal-center-item"},t_=ht(()=>e("label",{class:"font16 font-zhongcu"},"是否有效",-1)),o_={class:"new-modal-center-item"},s_=ht(()=>e("label",{class:"font16 font-zhongcu"},"是否为管理员",-1)),n_={class:"new-modal-bottom"},a_=ht(()=>e("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu common-confirm-btn"},"保存",-1));function l_(s,o,l,t,d,m){const u=_("BaseNavComponennt"),r=_("el-button"),v=_("el-pagination"),k=_("BreadCrumbComponent"),h=_("el-main"),g=_("el-container"),x=_("newComfirmsModal"),y=_("DepartmentTreeComponent"),w=_("el-switch");return a(),c(N,null,[e("div",b5,[i(g,{style:{height:"100vh"},class:"el-container-base"},{default:p(()=>[i(u,{selectedBar:t.selectedBar,onDataPermissions:t.handleDataPermissions},null,8,["selectedBar","onDataPermissions"]),i(h,{class:"el-main"},{default:p(()=>[e("div",w5,[e("div",C5,[k5,t.canPerformAction?(a(),c("div",x5,[e("button",{class:"el-main-add-btn font12 common-confirm-btn",onClick:o[0]||(o[0]=(...T)=>t.addPosition&&t.addPosition(...T))},"添加岗位")])):z("",!0)]),e("div",S5,[e("div",I5,[e("div",B5,[T5,M5,D5,t.canPerformAction?(a(),c("div",V5,"操作")):z("",!0)]),e("div",E5,[(a(!0),c(N,null,le(t.items,(T,A)=>(a(),c("div",{class:"el-col-item font14",key:A},[e("div",L5,S(T.department.name),1),e("div",z5,S(T.name),1),e("div",F5,[T.is_able?(a(),c(N,{key:0},[Q("是")],64)):(a(),c(N,{key:1},[Q("否")],64))]),t.canPerformAction?(a(),c("div",U5,[i(r,{link:"",type:"primary",size:"default",class:"el-col-item-btn",onClick:C=>t.editPosition(T.id)},{default:p(()=>[Q("修改")]),_:2},1032,["onClick"]),i(r,{link:"",type:"primary",size:"default",class:"el-col-item-btn",onClick:C=>t.delPosition(T.id)},{default:p(()=>[Q("删除")]),_:2},1032,["onClick"])])):z("",!0)]))),128))])]),i(v,{background:"",layout:"prev, pager, next",total:t.totalItems,"page-size":t.pageSize,"current-page":t.currentPage,onCurrentChange:t.handlePageChange,"hide-on-single-page":!0},null,8,["total","page-size","current-page","onCurrentChange"])])]),e("div",P5,[i(k,{class:"breadcrumb",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})]),i(x,{show:t.showDelModal,title:t.modalDelTitle,message:t.modalDelMessage,onClose:t.handleClose,onConfirm:t.handleConfirm},null,8,["show","title","message","onClose","onConfirm"]),t.showModal?(a(),c("div",R5,[e("div",N5,[e("div",q5,[e("div",K5,S(t.modalTitle),1)]),e("form",{onSubmit:o[6]||(o[6]=_e((...T)=>t.submitPosition&&t.submitPosition(...T),["prevent"]))},[e("div",H5,[e("div",Q5,[e("label",O5,S(t.siteuserName),1)]),e("div",Z5,[$5,t.department?(a(),c("div",Y5,S(t.department.name),1)):(a(),c("div",j5,[e("div",{class:"field-ipt font14 font-zhongcu",onClick:o[1]||(o[1]=_e((...T)=>t.toggleDepartmentTree&&t.toggleDepartmentTree(...T),["stop"]))},[t.departmentName?We(s.$slots,"default",{key:0},()=>[Q(S(t.departmentName),1)],!0):We(s.$slots,"default",{key:1},()=>[J5],!0)]),t.showDepartment?(a(),c("div",G5,[i(y,{selectedDepartmentId:t.departmentId,pageStr:"renyuan",onValueSelectedDepartment:t.handleValueSelectedDepartment},null,8,["selectedDepartmentId","onValueSelectedDepartment"])])):z("",!0)]))]),e("div",W5,[X5,me(e("input",{class:"field-ipt font14 font-zhongcu","onUpdate:modelValue":o[2]||(o[2]=T=>t.positionName=T),placeholder:"点击输入岗位",required:"",maxlength:"10"},null,512),[[Ae,t.positionName]])]),e("div",e_,[t_,i(w,{modelValue:t.is_able,"onUpdate:modelValue":o[3]||(o[3]=T=>t.is_able=T),size:"large"},null,8,["modelValue"])]),e("div",o_,[s_,i(w,{modelValue:t.is_leader,"onUpdate:modelValue":o[4]||(o[4]=T=>t.is_leader=T),size:"large"},null,8,["modelValue"])])]),e("div",n_,[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:o[5]||(o[5]=(...T)=>t.closeModal&&t.closeModal(...T))},"取消"),a_])],32)])])):z("",!0)],64)}const i_=ue(y5,[["render",l_],["__scopeId","data-v-17205764"]]),c_="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAAXNSR0IArs4c6QAAAcZJREFUKFNtkkFoE0EUht+bTTSSII4VRE+CHkQQFBEFD60ieNAqCOPBQAhMZhLFIBREkFYoxUtB6M0yM7nsSVjEkxUFUSjoxbvgRbyIQmUvISHozHNTumWJndu8+b+Z9/5/EHZYjUZjKoqig977X3Ec/56UYLEgpZxljM0joieiH4h4GAAYACwZY17l2m1IKfUYEc9nt9/p9Xrfc0Gn0znivV9FxHVjzJNxfRPSWl8FgHtpml5LksRrrU+GEG465xbH50KIiHO+RkQr1trXOfQxhFB3zn0bi1qt1hnG2C1jzMP8Ra31USKKrbUXsNls7iuXy2+yG87lAinlacbYbWvtg+LMSqnPg8HgEkopj0VR9NQYcyMXKKVOAcAyIr7YMgKGw2FcqVSee+/vY71e31utVt9l7pzNoW63u3s0Gl0f70MIlDn6N03TNc75p1KpNJ3PtE5E0lr7dafctsw6TkSr1tqZTUgpdRkRHwHAFWPMn0lQCLGLc/42c3vRGPO+mNMcAMwS0V3n3JccbLfbJ4joWQjhpXNuZTungq0XAWABAPYj4s8QwqEsvw0iWnLOffjvRxRbEkLsqdVqB/r9/kaSJMPJdv8BDea6v+qcs2kAAAAASUVORK5CYII=",r_="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAAXNSR0IArs4c6QAAAW1JREFUKFOFkjFLA0EQhWeW5BAOBUGJ5g+IhaSwFcRGC0krgUNI4HYhURArG9HrbIyNVWYvJGKjpvQPpNBCsRGsQgoVIUiUQ+ySu1u54IUjJGTLx/vYN/MGYcjL5/PT3W53zvO8r0ql0h60YFTgnG8g4uG/9gEACQCYVEoVpZRXobcPcc4PEHG10+kUqtXqa2gQQswAwBki/pZKpZ1A70FCiHUA2G80Gul6ve4Oi2yapmSMPRCR3YM453e+7xvlcvltGBBohmFM6bp+T0QpDIZ2XfdWSrkyCohE3QWASzRNc5ExdkxEmXFQfxG5XG42FovdSCnXxkGc84KmadfhTI9KqbRt25+jwGw2O6Fp2pPjOKkQyiDiJhFtj4KEECdKqW8p5Wm0p3NE1IPVE9FPCAc/xOPxI0RccBxnq1areYMXwRFxDwBeAOBdKZVAxGUAuEgmk0XLsvx+udFIlmWxVqu1BADznue1m83m82Dhf7INjafoc0HpAAAAAElFTkSuQmCC",d_="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAAXNSR0IArs4c6QAAAWxJREFUKFONkj8sQ2EUxX/3PQytRA0a+soiRnmtdCG1STQRJAYDq0RC7IRV7BISiZXBICSIVYipaV+ZLfRV1IBESWjfJ++lrcafpHe8Oec759zzCX9MIE2gGdoKJfJPMV5+QqR2Ec6QUIplQBByKEKAEmE1a3JawVZJRpolhAGlM5fr5a4CCF3TKSU2UVzaUdbcvUcqK8zbJuMIzi/HCs3IcCjChqvokQyLc6UzVatQVUoxpGm0OzpnUmLXjjAobmi/cGRHiIeS+HIx3t0cLqkjQ5+m2C4KiQeTvPt4QTEq4TQ9SmPVNpk0LGYF+rIRZoNJupsa2f/4ZCIf48ZzlGFPHJalNUmLr4FjV8nLZ7GuFDpCvyPM3JukKlYNi4u3IiPfmYpM52LcopDwFVs4HGSjnFSzJemSBna8TJ53i2ENFmyTsbqv59lKsaiEuCox5ymW59+eKoCy4krdP6K2UPc4fp3gKzw+R3n+WfYXmOmP+c6G/cgAAAAASUVORK5CYII=",u_="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAANCAYAAABy6+R8AAAAAXNSR0IArs4c6QAAAU5JREFUKFOdUr1KA0EYnN3bGEn2CCbxLl6RzsI8Q6wsLKxEEYNvINgL/hT+gL3gG4giipWFhZV5htinOHNLEgm3Cer9rNzJhagB0S2Hb76Z+XYIxrwupnJ+mhnszRd5vPS+j5BRwMkai4TQXUARgNiAsgCilAqPzL64T2aHJJE1twFS9QO6ab3azWTAnrTKTAvPAFU3+s5JhMekWAF0q9lvLVu6nrNctz0k6XrRdt1eOVu6VQhPI8WYJHjp0ffpBlLugKnMgwavVpDdRofnKwFSFz4ZLMDTM4yF54ZszZMotMcn7kzpVKMFbV6YC8EugfAQoHsU/npRdp4+l5t1Jt+XiEhPzyKlHRuytZZYcrixQkCvFcJVU4qbBBe8dAUv2PmhlFhSUAcEZD+x+kVpXKYAXm1GdhvPPF/RxmX69/Vitb/+0/AAf23EaJ1+694H50rIVR/8gBsAAAAASUVORK5CYII=",m_={name:"DocFragmentLIstView",components:{BaseNavComponent:Me,Delete:It,ArrowLeftBold:$e,CirclePlus:vt,BreadCrumbComponent:Ee},setup(){const s=n("kbase"),o=n("doc"),l=De(),t=Ce(),d=n(null),m=n(null),u=n(!0),r=n(""),v=n(null),k=n(!1),h=n("永久删除文档切片"),g=n("该文档切片数据将被永久删除，不可恢复及撤销。确定要删除吗？"),x=n([]),y=n(1),w=n(24),T=n(0),A=n(null),C=n(!1),L=n(null),B=n(!1),P=n("添加"),b=n(null),M=n([]),D=async(K,H)=>{try{b.value=Re.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});const ne=await _l(K,H);b.value.close(),x.value=ne.data,T.value=ne.data.length}catch{}},V=K=>{k.value=!0,L.value=K},f=()=>{k.value=!1,L.value=null},I=async()=>{k.value=!1,(await bl(m.value,d.value,L.value)).data.error=="0"?(E.success("删除成功"),D(m.value,d.value),L.value=null):E.error("当前切片内容不存在，删除失败")};Pe(A,K=>{K!==""?C.value=!0:C.value=!1});const U=async()=>{const K=await jt(m.value);return K.data.error=="0"?`知识库（${K.data.title}）`:"知识库"};ge(async()=>{if(l.params.dcid){d.value=l.params.dcid,m.value=l.params.kid,D(m.value,d.value);const K=await po(m.value,d.value);try{K.data.error=="0"?(r.value=K.data.title,v.value=K.data.type):K.data.error=="1"||K.data.error=="2"||K.data.error=="404"?t.push({name:"KBaseList"}):K.data.error=="3"?t.push({name:"DocList",params:{kid:m.value}}):E.error(K.data.message)}catch{}let H="";H=await U(),M.value=[{path:"/kbase-list",name:H},{path:`/doc-list/${m.value}`,name:"文档"},{path:"",name:`切片（${r.value}）`}]}});const J=K=>{K||(u.value=!1)},O=()=>{B.value=!1,L.value=null,C.value=!0,A.value="",P.value="添加"},Y=async K=>{try{const H=await gl(m.value,d.value,K);return H.data.error=="0"?(A.value=H.data.content,!0):!1}catch{return!1}};return{selectedBar:s,selectedKBaseNav:o,handleIsEdit:J,canPerformAction:u,docTitle:r,docType:v,showDelModal:k,modalDelTitle:h,modalDelMessage:g,delFragment:V,handleClose:f,handleConfirm:I,pageSize:w,totalItems:T,items:x,currentPage:y,getFileIcon:Ko,selectedKid:m,highlight:C,fragmentContent:A,closeModal:O,showEditModal:B,modalTitle:P,showEditModalAction:K=>{K?(L.value=K,Y(K).then(H=>{H?(B.value=!0,K&&(P.value="更新")):E.error("切片内容不存在或错误")}).catch(()=>{E.error("切片内容不存在或错误")})):B.value=!0},submitContent:async()=>{B.value=!1;let K;if(A.value){if(L.value)try{K=await Al(m.value,d.value,L.value,A.value)}catch{E.error("保存失败")}else try{K=await yl(m.value,d.value,A.value)}catch{E.error("保存失败")}O(),b.value.close();try{K.data.error=="0"?(E.success("保存成功"),D(m.value,d.value)):K.data.error?E.error("保存失败，"+K.data.message):E.error("保存失败")}catch{E.error("保存失败")}}else E("请填写切片内容")},openFullScreen:()=>{b.value=Re.service({lock:!0,text:"提交中",background:"rgba(0, 0, 0, 0.5)"})},breadcrumbs:M}}},Qo=s=>(be("data-v-00793d31"),s=s(),we(),s),p_={class:"common-layout",style:{height:"100vh"}},v_={class:"el-main-container"},h_={class:"el-main-top"},f_={class:"el-main-top-left font16"},__=["src"],g_={class:"file-name"},A_={style:{position:"relative"}},y_={class:"el-main-bottom"},b_={class:"font14 font-zhongcu",style:{"margin-bottom":"10px"}},w_={class:"fragment-list overflow-y"},C_=["onClick"],k_={class:"fragment-item-top"},x_={class:"fragment-item-top-left"},S_={class:"fragment-item-center"},I_={class:"fragment-item-bottom"},B_=Qo(()=>e("img",{src:c_,alt:""},null,-1)),T_=Qo(()=>e("img",{src:r_,alt:""},null,-1)),M_=Qo(()=>e("img",{src:d_,alt:""},null,-1)),D_=Qo(()=>e("img",{src:u_,alt:""},null,-1)),V_={key:0,class:"fragment-item-modal"},E_={class:"breadcrumb-container"},L_={key:0,class:"new-modal-overlay"},z_={class:"new-modal-container"},F_={class:"new-modal-top"},U_={class:"font18 font-zhongcu"},P_={class:"new-modal-top-right font16 overflow-one"},R_=["src"],N_={class:"file-name"},q_={class:"new-modal-center"},K_={class:"new-modal-bottom"},H_=["disabled"];function Q_(s,o,l,t,d,m){const u=_("BaseNavComponent"),r=_("ArrowLeftBold"),v=_("el-icon"),k=_("router-link"),h=_("CirclePlus"),g=_("Delete"),x=_("el-button"),y=_("BreadCrumbComponent"),w=_("el-main"),T=_("el-container"),A=_("newComfirmsModal");return a(),c(N,null,[e("div",p_,[i(T,{style:{height:"100vh"},class:"el-container-base"},{default:p(()=>[i(u,{selectedBar:t.selectedBar},null,8,["selectedBar"]),i(w,{class:"el-main common-layout-bg"},{default:p(()=>[e("div",v_,[e("div",h_,[e("div",f_,[t.selectedKid?(a(),X(k,{key:0,to:{name:"DocList",params:{kid:t.selectedKid}},class:"el-main-top-left-back"},{default:p(()=>[i(v,{color:"#666666",size:"16"},{default:p(()=>[i(r)]),_:1})]),_:1},8,["to"])):z("",!0),t.docType?(a(),c("img",{key:1,src:t.getFileIcon(t.docType),alt:"file icon",class:"file-icon"},null,8,__)):z("",!0),e("span",g_,S(t.docTitle)+"."+S(t.docType),1)]),e("div",A_,[t.canPerformAction?(a(),c("button",{key:0,class:"el-main-top-right-add font18",onClick:o[0]||(o[0]=C=>t.showEditModalAction())},[i(v,{size:"22",style:{position:"relative",top:"5px"}},{default:p(()=>[i(h)]),_:1}),Q(" 插入 ")])):z("",!0)])]),e("div",y_,[e("div",b_,S(t.totalItems)+"组",1),e("div",w_,[(a(!0),c(N,null,le(t.items,(C,L)=>(a(),c("div",{class:"fragment-item font12",onClick:B=>t.canPerformAction&&t.showEditModalAction(C.id),key:C.id},[e("div",k_,[e("div",x_,"#"+S(L+1),1),e("div",null,"集合ID："+S(C.vectorid),1)]),e("div",S_,S(C.content),1),e("div",I_,[C.status=="wait"?(a(),c(N,{key:0},[B_,Q("待学习 ")],64)):C.status=="doing"?(a(),c(N,{key:1},[T_,Q("学习中 ")],64)):C.status=="success"?(a(),c(N,{key:2},[M_,Q("学习成功 ")],64)):C.status=="fail"?(a(),c(N,{key:3},[D_,Q("学习失败 ")],64)):z("",!0)]),t.canPerformAction?(a(),c("div",V_,[i(x,{title:"删除",onClick:_e(B=>t.delFragment(C.id),["stop"])},{default:p(()=>[i(v,{size:"16"},{default:p(()=>[i(g)]),_:1})]),_:2},1032,["onClick"])])):z("",!0)],8,C_))),128))])])]),e("div",E_,[i(y,{class:"breadcrumb",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})]),i(A,{show:t.showDelModal,title:t.modalDelTitle,message:t.modalDelMessage,onClose:t.handleClose,onConfirm:t.handleConfirm},null,8,["show","title","message","onClose","onConfirm"]),t.showEditModal?(a(),c("div",L_,[e("div",z_,[e("div",F_,[e("div",U_,S(t.modalTitle)+"数据",1),e("div",P_,[t.docType?(a(),c("img",{key:0,src:t.getFileIcon(t.docType),alt:"file icon",class:"file-icon"},null,8,R_)):z("",!0),e("span",N_,S(t.docTitle)+"."+S(t.docType),1)])]),e("div",q_,[me(e("textarea",{class:"modal-edit-content font12 overflow-y","onUpdate:modelValue":o[1]||(o[1]=C=>t.fragmentContent=C)},null,512),[[Ae,t.fragmentContent]])]),e("div",K_,[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:o[2]||(o[2]=(...C)=>t.closeModal&&t.closeModal(...C))},"取消"),e("button",{type:"button",class:W(["new-modal-bottom-confirm font14 font-zhongcu modal-footer-confirm common-confirm-btn",t.highlight?"modal-footer-confirm-active":""]),disabled:!t.highlight,onClick:o[3]||(o[3]=C=>{t.submitContent(),t.openFullScreen()})},"保存",10,H_)])])])):z("",!0)],64)}const O_=ue(m_,[["render",Q_],["__scopeId","data-v-00793d31"]]),Z_={},$_={class:"flex-1 flex justify-center overflow-hidden hidden-scrollbar",style:{background:"linear-gradient( 129deg, #F4FAFF 0%, #F9FAFF 100%)"}},Y_={class:"flex flex-col w-[84%] max-w-[1170px] relative h-screen overflow-hidden"},j_={class:"mt-[35px]"},J_={class:"flex-1 overflow-auto hidden-scrollbar",style:{}};function G_(s,o){return a(),c("div",$_,[e("div",Y_,[e("div",j_,[We(s.$slots,"head",{},void 0,!0)]),e("div",J_,[We(s.$slots,"main",{},void 0,!0)])])])}const Wt=ue(Z_,[["render",G_],["__scopeId","data-v-00411696"]]),W_={};function X_(s,o){const l=_("el-row");return a(),X(l,{align:"middle",justify:"space-between",class:"h-[65px] rounded-[15px] px-[30px] text-[18px]",style:{"background-color":"#EBF4FB"}},{default:p(()=>[We(s.$slots,"left"),We(s.$slots,"right")]),_:3})}const Xt=ue(W_,[["render",X_]]),eg="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAYAAABWdVznAAAAAXNSR0IArs4c6QAAAWRJREFUKFN1kDtLA1EQhc9scjemC9j4M2xFUSsRtFAxhdoEQ25S+CO2iAiKjYWBvcFNZeEDHwhiIygoiL19GhsLsRBMbu6M2WBEdJ1uHt9wziEAKBQKOd/3IxEZArBujLmI50lF8bFS6qS7DKy1D5lM5tA5V6/X62eJgNb6WER2jDE38UE+n8/mcrn4wa4x5vw3RFrru1QqNVur1V6DIPCazeagUuodwH0YhsN/gEqlMuqcq1pr5z3Pc+l0+oqIDDMvGWOmABAA6YNxg3K5PC4iAYB5Zp4koljSKhEVAQwwc7XvqQfEpbUeA7AB4E1EIiI6EpEFa+2l7/sHAPa6Ek+/gZ9aS6XSLRFV4+SYeSWbzT63Wq393qOk6LTWj0qpaedciplbYRi+aa0VgOtEoFgsjnqet9npdOaiKHr5kpwHMJMI9D0R0RYRLTLzBIDldru9+C/wld6IiGyLyJO1dq3RaHx8Ais8oHdfhCnmAAAAAElFTkSuQmCC",tg=s=>(be("data-v-005513b7"),s=s(),we(),s),og={key:0},sg={key:1},ng={class:"h-[52px] w-[46px]"},ag={class:"file"},lg={class:"flex-1 ml-[15px] overflow-hidden flex items-center"},ig={class:"max-w-[260px] flex-1 overflow-hidden"},cg={class:"cc-s-title-text font-zhongcu"},rg={key:0,class:"absolute left-0 bottom-0 w-full h-[73px] bg-white rounded-md",style:{background:"linear-gradient(180deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.8) 50%, #FFFFFF 100%)"}},dg={class:"flex items-end w-full h-full"},ug=tg(()=>e("div",{class:"cc-s-body-text"},[e("img",{src:eg,class:"h-[12px] w-[12px]"}),Q(" 可编辑 ")],-1)),mg={class:"cc-s-body-text"},pg=ye({__name:"ThreeElCard",props:{type:{},item:{},footer:{},expand:{}},setup(s){const o=s,l=n(!1),t=n(!1),d=()=>{t.value=!1},m=r=>{t.value=!0},u=()=>{t.value=!1,o.expand.delModal.confirm(o.item.id)};return(r,v)=>{const k=_("el-avatar"),h=_("el-row"),g=_("el-text"),x=_("el-button"),y=_("el-tooltip"),w=_("newComfirmsModal");return a(),c(N,null,[e("div",{class:"relative basis-0 w-full px-7 flex items-center justify-start h-[92px] bg-white rounded-[15px] overflow-hidden",onMouseover:v[0]||(v[0]=T=>l.value=!0),onMouseleave:v[1]||(v[1]=T=>l.value=!1)},[i(h,{class:"justify-start"},{default:p(()=>[r.type==oe($t).Tool?(a(),c("div",og,[i(k,{style:{background:"linear-gradient( 129deg, #129BFF 0%, #3E60E9 100%)",width:"46px",height:"46px"}},{default:p(()=>[Q(S(o.item.name[0]),1)]),_:1})])):r.type==oe($t).Template?(a(),c("div",sg,[e("div",ng,[e("div",ag,[e("div",{class:"file-txt font-zhongcu",style:un(o.item.type=="xlsx"?{background:"#29DA6B"}:{background:"#56AAFF"})},S(o.item.type.toUpperCase()),5)])])])):z("",!0),e("div",lg,[e("div",ig,[i(h,{class:""},{default:p(()=>[e("div",cg,S(o.item.name),1)]),_:1}),i(h,{class:"mt-[5px]"},{default:p(()=>[i(g,{truncated:"",size:"small",class:"cc-s-body-text overflow-hidden"},{default:p(()=>[Q(S(o.item.description),1)]),_:1})]),_:1})])])]),_:1}),l.value&&o.footer?(a(),c("div",rg,[e("div",dg,[i(h,{justify:"space-between",class:"w-full px-[20px] pb-[10px] leading-[17px]"},{default:p(()=>[ug,e("div",mg,S(o.footer.type==="fun"?"本地库":"API创建"),1)]),_:1})])])):z("",!0),l.value?(a(),X(y,{key:1,class:"box-item",effect:"dark",content:"删除",placement:"bottom"},{default:p(()=>[i(x,{class:"el-col-modal-btn el-col-modal-del",onClick:_e(m,["stop"])},{default:p(()=>[i(oe(tt),{style:{"font-size":"14px"}},{default:p(()=>[i(oe(It))]),_:1})]),_:1})]),_:1})):z("",!0)],32),i(w,{show:t.value,title:o.expand.delModal.title,message:o.expand.delModal.description,onClose:d,onConfirm:u},null,8,["show","title","message"])],64)}}}),Hn=ue(pg,[["__scopeId","data-v-005513b7"]]),vg={class:"overflow-x-hidden"},hg=["onClick"],Qn=ye({__name:"ThreeElCardList",props:{type:{},items:{},detailFun:{type:Function},expand:{}},setup(s){return(o,l)=>{const t=_("el-col"),d=_("el-row");return a(),c("div",vg,[i(d,{gutter:15,class:"gap-y-[15px]"},{default:p(()=>[(a(!0),c(N,null,le(o.items,m=>(a(),X(t,{sm:12,lg:8,key:m.id},{default:p(()=>[e("div",{class:"hover:cursor-pointer",onClick:u=>o.detailFun(m)},[i(Hn,{item:m,type:o.type,footer:o.type==oe($t).Tool?{type:m.typestr}:void 0,expand:o.expand},null,8,["item","type","footer","expand"])],8,hg)]),_:2},1024))),128))]),_:1})])}}}),fg={name:"ToolListView",computed:{ListType(){return $t}},components:{ThreeElCardList:Qn,BreadCrumbComponent:Ee,ThreeElCard:Hn,BaseNavComponennt:Me,CloseBold:Lt},setup(){const s=Ce(),o=n("tool"),l=n(!1),t=n(!1),d=n(!1),m=n(!1),u=n(null),r=n([]),v=n(1),k=n(18),h=n(0),g=n(""),x=n(""),y=n(!1),w=async(b,M)=>{try{u.value=Re.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});const D=await wl(b,M);u.value.close(),D.data.error=="403"?s.push({name:"Index"}):(r.value=D.data.data,h.value=D.data.total_num[0],D.data.data.length<=0&&(d.value=!0))}catch{u.value.close()}},T=b=>{v.value=b,w(v.value,k.value)},A=b=>{l.value=ze(b,["create_tool"]),t.value=ze(b,["create_tool","manage_tool"])};ge(()=>{w(v.value,k.value)});const C=()=>{m.value=!1,g.value="",x.value=""};return{selectedBar:o,handleDataPermissions:A,canAddAction:l,canPerformAction:t,pageSize:k,totalItems:h,items:r,currentPage:v,handlePageChange:T,showModal:m,closeModal:C,name:g,description:x,createTool:async()=>{if(!y.value){y.value=!0;try{const b=await Cn(g.value,x.value);b.data.message?E.error(b.data.message):b.data.error=="0"&&s.push({name:"ToolEdit",params:{tid:b.data.id}})}finally{C(),y.value=!1}}},showDefault:d,handleDelConfirm:async b=>{const M=await kn(b);M.data.error=="0"?(E.success("删除成功"),await w(v.value,k.value)):M.data.message?E.error(`删除失败，${M.data.message}`):E.error("删除失败")},pushDetail:b=>{s.push({name:b.typestr==="fun"?"ToolSetting":"ToolEdit",params:{tid:b.id}})}}}},_g={key:0,style:{width:"100%",height:"95%",display:"flex","align-items":"center","justify-content":"center","flex-direction":"column"}},gg=e("img",{src:Ps,alt:"",style:{width:"50px",height:"50px","margin-bottom":"10px"}},null,-1),Ag=e("div",null,"还没有工具，快去创建一个吧",-1),yg=[gg,Ag],bg={class:"mt-[30px]"};function wg(s,o,l,t,d,m){const u=_("ThreeElCardList"),r=_("el-pagination");return a(),c(N,null,[t.showDefault?(a(),c("div",_g,yg)):z("",!0),e("div",bg,[i(u,{type:m.ListType.Tool,expand:{delModal:{title:"永久删除工具",description:"该工具将永久删除，不可恢复及撤销。确认要删除吗？",confirm:t.handleDelConfirm}},"detail-fun":t.pushDetail,items:t.items},null,8,["type","expand","detail-fun","items"]),i(r,{background:"",layout:"prev, pager, next",total:t.totalItems,"page-size":t.pageSize,"current-page":t.currentPage,onCurrentChange:t.handlePageChange,style:{"justify-content":"flex-end",padding:"20px"},"hide-on-single-page":!0},null,8,["total","page-size","current-page","onCurrentChange"])])],64)}const On=ue(fg,[["render",wg]]),wo=s=>(be("data-v-53b90c07"),s=s(),we(),s),Cg=wo(()=>e("div",{class:"cc-page-title-text font-zhongcu"}," 我的工具 ",-1)),kg={class:"breadcrumb-container"},xg={key:0,class:"new-modal-overlay"},Sg={class:"new-modal-container"},Ig=wo(()=>e("div",{class:"new-modal-top"},[e("div",{class:"font18 font-zhongcu"},"创建工具")],-1)),Bg={class:"new-modal-center"},Tg={class:"new-modal-center-item"},Mg=wo(()=>e("label",{class:"font16 font-zhongcu"},"名称",-1)),Dg={class:"new-modal-center-item"},Vg=wo(()=>e("label",{class:"font16 font-zhongcu"},"添加描述",-1)),Eg=wo(()=>e("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu"},"保存",-1)),Lg=ye({__name:"index",setup(s){const o=n("tool"),l=n(!1),t=n(!1),d=n(!1),m=n(""),u=n(""),r=n(!1),v=Ce(),k=y=>{l.value=ze(y,["create_tool"]),t.value=ze(y,["create_tool","manage_tool"])},h=()=>{d.value=!1,m.value="",u.value=""},g=async()=>{if(!r.value){r.value=!0;try{const y=await Cn(m.value,u.value);y.data.message?E.error(y.data.message):y.data.error=="0"&&v.push({name:"ToolEdit",params:{tid:y.data.id}})}finally{h(),r.value=!1}}},x=n([{path:"/tool-list",name:"应用管理"},{path:"",name:"工具"}]);return(y,w)=>{const T=_("el-button"),A=_("el-row");return a(),c(N,null,[i(A,{class:"h-screen w-full"},{default:p(()=>[i(Me,{selectedBar:o.value,onDataPermissions:k},null,8,["selectedBar"]),i(Wt,null,{head:p(()=>[i(Xt,null,{left:p(()=>[Cg]),right:p(()=>[l.value?(a(),X(T,{key:0,icon:oe(vt),class:"rounded-[10px] cc-header-right-button",color:"#F1F6FF",onClick:w[0]||(w[0]=C=>d.value=!0)},{default:p(()=>[Q(" 创建 ")]),_:1},8,["icon"])):z("",!0)]),_:1})]),main:p(()=>[i(On),e("div",kg,[i(Ee,{class:"breadcrumb",style:{"padding-left":"0"},breadcrumbs:x.value},null,8,["breadcrumbs"])])]),_:1})]),_:1}),d.value?(a(),c("div",xg,[e("div",Sg,[Ig,e("form",{onSubmit:_e(g,["prevent"])},[e("div",Bg,[e("div",Tg,[Mg,me(e("input",{id:"title",class:"field-ipt font14 font-zhongcu","onUpdate:modelValue":w[1]||(w[1]=C=>m.value=C),placeholder:"输入工具名字",required:"",maxlength:"50"},null,512),[[Ae,m.value]])]),e("div",Dg,[Vg,me(e("textarea",{id:"description",class:"field-ipt field-txt font14 font-zhongcu","onUpdate:modelValue":w[2]||(w[2]=C=>u.value=C),placeholder:"请准确说明工具用途，方便模型解读",required:"",maxlength:"100"},null,512),[[Ae,u.value]])])]),e("div",{class:"new-modal-bottom"},[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu",onClick:h},"取消"),Eg])],32)])])):z("",!0)],64)}}}),zg=ue(Lg,[["__scopeId","data-v-53b90c07"]]),Fg={props:{selectedKBaseNav:{type:String,required:!0},toolType:{type:String,required:!1},toolName:{type:String,required:!0},canPerformAction:{type:Boolean,required:!0}},components:{ElButton:mt,ElIcon:tt,Delete:It},setup(){const s=De(),o=Ce(),l=n(null),t=v=>{v=="api"?o.push({name:"ToolEdit",params:{tid:s.params.tid}}):v=="setting"?o.push({name:"ToolSetting",params:{tid:s.params.tid}}):v=="test"&&o.push({name:"ToolTest",params:{tid:s.params.tid}})};ge(()=>{l.value=s.params.tid});const d=n(!1),m=n("确认删除该工具吗？");return{changeBar:t,showDelModal:d,modalMessage:m,handleClose:()=>{d.value=!1},handleConfirm:async()=>{d.value=!1;const v=await kn(l.value);v.data.error=="0"?(E.success("删除成功"),setTimeout(()=>{o.push({name:"ToolList"})},1e3)):v.data.message?E.error(`删除失败，${v.data.message}`):E.error("删除失败")}}}},Ug={class:"mt-[30px] flex gap-[20px]"};function Pg(s,o,l,t,d,m){return a(),c("div",Ug,[l.toolType!="fun"?(a(),c("div",{key:0,class:W(l.selectedKBaseNav=="api"?"tag-select":"tag-default"),onClick:o[0]||(o[0]=u=>t.changeBar("api"))},"接口设置",2)):z("",!0),l.toolType!="fun"?(a(),c("div",{key:1,class:W(l.selectedKBaseNav=="test"?"tag-select":"tag-default"),onClick:o[1]||(o[1]=u=>t.changeBar("test"))},"接口测试",2)):z("",!0),e("div",{class:W(l.selectedKBaseNav=="setting"?"tag-select":"tag-default"),onClick:o[2]||(o[2]=u=>t.changeBar("setting"))},"使用权限",2)])}const qs=ue(Fg,[["render",Pg],["__scopeId","data-v-37a57271"]]),Rg={name:"ToolEditView",computed:{CirclePlus(){return vt}},components:{HeaderLeft:Gt,ToolEditNav:qs,BreadCrumbComponent:Ee,BaseNavComponennt:Me,ToolList:On,Main:Wt,MainHeader:Xt,BaseNavComponent:Me,ArrowLeftBold:$e},setup(){const s=Ce(),o=De(),l=n("tool"),t=n("api"),d=n(!1),m=n(null),u=n(""),r=n("GET"),v=n(""),k=n(""),h=n(!1),g=n([{path:"/tool-list",name:"工具"},{path:"",name:"接口设置"}]),x=[{value:"string",label:"string"},{value:"number",label:"number"},{value:"boolean",label:"boolean"},{value:"integer",label:"integer"}],y=[{value:"array",label:"array"},{value:"object",label:"object"}],w=n([...x]),T=n([...x,...y]),A=n([{name:"",value:""}]),C=n([{key:"",description:"",type:"string",const:"",required:!1,isConstSwitchOn:!1}]),L=n([{id:0,key:"",description:"",type:"string",const:"",required:!1,isConstSwitchOn:!1}]),B=n([{id:0,key:"",description:"",type:"string"}]),P=n(0),b=n(0),M=Z=>{if(Z==="body")return b.value+=1,b.value;if(Z==="response")return P.value+=1,P.value},D=(Z,se)=>{Z=="header"?A.value.splice(se,1):Z=="params"&&C.value.splice(se,1)},V=Z=>{Z=="header"?A.value.push({name:"",value:""}):Z=="params"?C.value.push({key:"",description:"",type:"string",const:"",required:!1,isConstSwitchOn:!1}):Z=="body"?L.value.push({id:M("body"),key:"",description:"",type:"string",const:"",required:!1,isConstSwitchOn:!1}):Z=="response"&&B.value.push({id:M("response"),key:"",description:"",type:"string"})},f=(Z,se)=>{const $=(ae,te)=>{const de=ae.findIndex(he=>he.key===te);if(de!==-1)return ae.splice(de,1),!0;for(const he of ae)if(he.children&&$(he.children,te))return!0;return!1};Z=="body"?$(L.value,se.key):Z=="response"&&$(B.value,se.key)},I=(Z,se)=>{se.type==="object"?(se.children||(se.children=[]),Z=="body"?se.children.push({id:M("body"),key:"",description:"",type:"string",const:"",required:!1,isConstSwitchOn:!1}):Z=="response"&&se.children.push({id:M("response"),key:"",description:"",type:"string"})):se.type==="array"&&(se.children||(se.children=[]),se.children.length===0&&(Z=="body"?se.children.push({id:M("body"),key:"items",description:"",type:"string",const:"",required:!1,isConstSwitchOn:!1}):Z=="response"&&se.children.push({id:M("response"),key:"items",description:"",type:"string"})))},U=Z=>{d.value=ze(Z,["create_tool","manage_tool"])},J=Z=>{if(Object.keys(Z.properties).length===0&&Z.properties.constructor===Object)return{};{let $=[],ae=Object.keys(Z.properties);if(ae.length>0)for(var se=0;se<ae.length;se++){let te=ae[se],de=!1;Z.required&&Array.isArray(Z.required)&&(de=Z.required.includes(`${te}`));let he=!1;Z.properties[te].const&&(he=!0),$.push({key:te,const:Z.properties[te].const?Z.properties[te].const:"",type:Z.properties[`${te}`].type,description:Z.properties[`${te}`].description,required:de,isConstSwitchOn:he})}return $}},O=Z=>Object.keys(Z).length===0&&Z.constructor===Object?{}:Object.keys(Z).map(se=>({name:se,value:Z[se]})),Y=(Z,se,$=[])=>{const ae=[];for(const te in se){const de=se[te];let he={};if(Z=="body"){let Oe=!1;$&&Array.isArray($)&&(Oe=$.includes(`${te}`));let Ge=!1;de.const&&(Ge=!0),he={id:M("body"),key:te,const:de.const?de.const:"",type:de.type,description:de.description,required:Oe,isConstSwitchOn:Ge}}else Z=="response"&&(he={id:M("response"),key:te,description:de.description||"",type:de.type});de.type==="object"&&de.properties?he.children=Y(Z,de.properties,$):de.type==="array"&&de.items&&(he.children=[Y(Z,{items:de.items},$)[0]]),ae.push(he)}return ae},R=Z=>Object.keys(Z).length===0&&Z.constructor===Object,j=async()=>{const Z=await Ts(m.value);if(Z.data.error=="1"||Z.data.error=="404")s.push({name:"ToolList"});else{const se=Z.data;if(u.value=se.name,v.value=se.apiurl,k.value=se.fun_name,se.apimethod&&(r.value=se.apimethod),se.apiheaders)try{let $=O(JSON.parse(se.apiheaders));R($)||(A.value=$)}catch{}if(se.apiqueryparams)try{let $=J(JSON.parse(se.apiqueryparams));R($)||(C.value=$)}catch{}if(se.apibody)try{if(!R(JSON.parse(se.apibody).properties)){let $=Y("body",JSON.parse(se.apibody).properties,JSON.parse(se.apibody).required);R($)||(L.value=$)}}catch{}if(se.response)try{if(!R(JSON.parse(se.response))){let $=Y("response",JSON.parse(se.response).properties);R($)||(B.value=$)}}catch{}}};ge(()=>{m.value=o.params.tid,j()});const ee=async()=>{if(h.value)return;if(h.value=!0,A.value.some(de=>/[_\u4e00-\u9fa5]/.test(de.name))){E("Header键名不能包含下划线或中文字符"),h.value=!1;return}if(k.value){if(!v.value){E("请填写接口地址"),h.value=!1;return}}else{E("请填写函数名称"),h.value=!1;return}let se=A.value.reduce((de,he)=>(he.name&&he.value&&(de[he.name]=he.value),de),{}),$={properties:C.value.reduce((de,he)=>(he.key&&he.type&&he.description!==void 0&&(de[he.key]={type:he.type,description:he.description},he.const&&(de[he.key].const=he.const)),de),{}),required:C.value.filter(de=>de.required).map(de=>de.key)},ae={};R(ne("body",L.value))||(re=[],ae={properties:ne("body",L.value),required:G(L.value)});let te={};R(ne("response",B.value))||(te={properties:ne("response",B.value)});try{const de=await Cl(m.value,v.value,r.value,JSON.stringify(se),JSON.stringify($),JSON.stringify(ae),JSON.stringify(te),k.value);de.data.error=="0"?E.success("提交成功"):de.data.message?E.error(de.data.message):s.push({name:"ToolList"})}catch{E.error("提交失败")}finally{h.value=!1,j()}},K=(Z,se)=>{for(const $ of se){if($.children&&$.children.includes(Z))return $;if($.children){const ae=K(Z,$.children);if(ae)return ae}}return null},H=(Z,se)=>{if(Z.key==="items"){const $=K(Z,se);return $&&$.type==="array"}return!1},ne=(Z,se)=>{const $={};return se.forEach(ae=>{ae.type==="array"?Z=="body"?$[ae.key]={type:ae.type,description:ae.description,items:ae.children&&ae.children.length>0?ne(Z,ae.children)[ae.children[0].key]:{}}:Z=="response"&&($[ae.key]={type:ae.type,description:ae.description,items:ae.children&&ae.children.length>0?ne(Z,ae.children)[ae.children[0].key]:{}}):ae.type==="object"?Z=="body"?$[ae.key]={type:ae.type,description:ae.description,properties:ae.children?ne(Z,ae.children):{}}:Z=="response"&&($[ae.key]={type:ae.type,description:ae.description,properties:ae.children?ne(Z,ae.children):{}}):Z=="body"?$[ae.key]={type:ae.type,description:ae.description}:Z=="response"&&($[ae.key]={type:ae.type,description:ae.description}),Z=="body"&&ae.const&&($[ae.key].const=ae.const)}),$};let re=[];const G=Z=>(Z.forEach(se=>{se.required&&re.push(se.key),se.type==="array"?se.children&&se.children.length>0&&G(se.children)[se.children[0].key]:se.type==="object"&&se.children&&G(se.children)}),re);return{selectedBar:l,selectedKBaseNav:t,toolId:m,toolName:u,selectApiMethod:r,apiUrl:v,funName:k,headersTableData:A,handleDataPermissions:U,paramsType:w,paramsTableData:C,bodyTableData:L,submitData:ee,canPerformAction:d,responseBodyType:T,responseTableData:B,isKeyDisabled:H,deleteHeaderParamsRow:D,onAddTableItem:V,deleteBodyResponseRow:f,onBodyResponseAddChildItem:I,changSwitch:Z=>{Z.isConstSwitchOn||(Z.const="")},breadcrumbs:g}}},Pt=s=>(be("data-v-e2dc8a9b"),s=s(),we(),s),Ng=Pt(()=>e("div",{class:"el-main-title"},"接口参数",-1)),qg={class:"el-main-item"},Kg=Pt(()=>e("div",{class:"el-main-item-title"},"函数名称",-1)),Hg={class:"el-main-item"},Qg=Pt(()=>e("div",{class:"el-main-item-title"},"接口地址",-1)),Og={class:"el-main-item"},Zg=Pt(()=>e("div",{class:"el-main-item-title"},[Q("Headers "),e("span",{style:{color:"red","font-weight":"normal","font-size":"12px","margin-left":"10px"}},'禁止输入"_"或中文')],-1)),$g={class:"el-main-item"},Yg=Pt(()=>e("div",{class:"el-main-item-title"},"请求参数",-1)),jg={class:"el-main-item"},Jg=Pt(()=>e("div",{class:"el-main-item-title"},"请求body",-1)),Gg={class:"el-main-item"},Wg=Pt(()=>e("div",{class:"el-main-item-title"},"返回body",-1)),Xg={key:0,class:"el-main-footer"},e3={class:"breadcrumb-container"};function t3(s,o,l,t,d,m){const u=_("BaseNavComponennt"),r=_("HeaderLeft"),v=_("MainHeader"),k=_("tool-edit-nav"),h=_("el-input"),g=_("el-table-column"),x=_("el-button"),y=_("el-table"),w=_("el-option"),T=_("el-select"),A=_("el-switch"),C=_("el-main"),L=_("BreadCrumbComponent"),B=_("Main"),P=_("el-row");return a(),X(P,{class:"h-screen w-full"},{default:p(()=>[i(u,{selectedBar:t.selectedBar,onDataPermissions:t.handleDataPermissions},null,8,["selectedBar","onDataPermissions"]),i(B,null,{head:p(()=>[i(v,null,{left:p(()=>[i(r,{to:{name:"ToolList"},text:t.toolName,submit:t.submitData},null,8,["text","submit"])]),right:p(()=>[]),_:1})]),main:p(()=>[i(k,{"can-perform-action":t.canPerformAction,"tool-name":t.toolName,"selected-k-base-nav":t.selectedKBaseNav},null,8,["can-perform-action","tool-name","selected-k-base-nav"]),i(C,{class:"el-main-right el-main-box2 overflow-y",style:{"padding-bottom":"12vh"}},{default:p(()=>[Ng,e("div",qg,[Kg,me(e("input",{class:"cc-input","onUpdate:modelValue":o[0]||(o[0]=b=>t.funName=b),placeholder:"输入函数名称"},null,512),[[Ae,t.funName]])]),e("div",Hg,[Qg,me(e("input",{"onUpdate:modelValue":o[1]||(o[1]=b=>t.apiUrl=b),placeholder:"输入URL地址，以http/https开头",class:"cc-input"},null,512),[[Ae,t.apiUrl]])]),e("div",Og,[Zg,i(y,{data:t.headersTableData,style:{width:"480px"},border:"",class:""},{default:p(()=>[i(g,{prop:"",label:"Header Name",width:"200px"},{default:p(b=>[i(h,{modelValue:b.row.name,"onUpdate:modelValue":M=>b.row.name=M,class:"header-name-input"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(g,{prop:"",label:"Header Value"},{default:p(b=>[i(h,{modelValue:b.row.value,"onUpdate:modelValue":M=>b.row.value=M,class:"header-value-input"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(g,{prop:"",label:"操作",fixed:"right",width:"100px"},{default:p(b=>[i(x,{link:"",type:"primary",size:"small",onClick:_e(M=>t.deleteHeaderParamsRow("header",b.$index),["prevent"])},{default:p(()=>[Q(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),i(x,{type:"primary",link:"",onClick:o[2]||(o[2]=b=>t.onAddTableItem("header")),class:"add-item-btn"},{default:p(()=>[Q(" + 添加参数")]),_:1})]),e("div",$g,[Yg,i(y,{data:t.paramsTableData,style:{width:"960px"},border:"",class:"el-main-item-table"},{default:p(()=>[i(g,{prop:"",label:"字段key",width:"200px"},{default:p(b=>[i(h,{modelValue:b.row.key,"onUpdate:modelValue":M=>b.row.key=M},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(g,{prop:"",label:"字段说明"},{default:p(b=>[i(h,{modelValue:b.row.description,"onUpdate:modelValue":M=>b.row.description=M},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(g,{prop:"",label:"字段类型"},{default:p(b=>[i(T,{modelValue:b.row.type,"onUpdate:modelValue":M=>b.row.type=M,placeholder:"选择类型",size:"large"},{default:p(()=>[(a(!0),c(N,null,le(t.paramsType,M=>(a(),X(w,{key:M.value,label:M.label,value:M.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),i(g,{prop:"",label:"固定值",width:"200px"},{default:p(b=>[i(A,{modelValue:b.row.isConstSwitchOn,"onUpdate:modelValue":M=>b.row.isConstSwitchOn=M,style:{"--el-switch-on-color":"#129BFF"},onChange:M=>t.changSwitch(b.row)},null,8,["modelValue","onUpdate:modelValue","onChange"]),me(i(h,{modelValue:b.row.const,"onUpdate:modelValue":M=>b.row.const=M,style:{"box-shadow":"0 0 0 1px var(--el-input-border-color,var(--el-border-color)) inset","margin-left":"5px"}},null,8,["modelValue","onUpdate:modelValue"]),[[bt,b.row.isConstSwitchOn||b.row.const]])]),_:1}),i(g,{prop:"",label:"是否必填"},{default:p(b=>[i(A,{modelValue:b.row.required,"onUpdate:modelValue":M=>b.row.required=M,style:{"--el-switch-on-color":"#129BFF"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(g,{prop:"",label:"操作",fixed:"right",width:"100px"},{default:p(b=>[i(x,{link:"",type:"primary",size:"small",onClick:_e(M=>t.deleteHeaderParamsRow("params",b.$index),["prevent"])},{default:p(()=>[Q(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),i(x,{type:"primary",link:"",onClick:o[3]||(o[3]=b=>t.onAddTableItem("params")),class:"add-item-btn"},{default:p(()=>[Q(" + 添加参数")]),_:1})]),e("div",jg,[Jg,i(y,{data:t.bodyTableData,style:{width:"960px"},"row-key":"id",border:"","default-expand-all":"",class:"el-main-item-table"},{default:p(()=>[i(g,{prop:"",label:"字段key",width:"200px"},{default:p(b=>[i(h,{modelValue:b.row.key,"onUpdate:modelValue":M=>b.row.key=M,disabled:t.isKeyDisabled(b.row,t.bodyTableData)},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),i(g,{prop:"",label:"字段说明"},{default:p(b=>[i(h,{modelValue:b.row.description,"onUpdate:modelValue":M=>b.row.description=M},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(g,{prop:"",label:"字段类型"},{default:p(b=>[i(T,{modelValue:b.row.type,"onUpdate:modelValue":M=>b.row.type=M,placeholder:"选择类型",size:"large"},{default:p(()=>[(a(!0),c(N,null,le(t.responseBodyType,M=>(a(),X(w,{key:M.value,label:M.label,value:M.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),i(g,{prop:"",label:"固定值",width:"200px"},{default:p(b=>[i(A,{modelValue:b.row.isConstSwitchOn,"onUpdate:modelValue":M=>b.row.isConstSwitchOn=M,style:{"--el-switch-on-color":"#129BFF"},onChange:M=>t.changSwitch(b.row)},null,8,["modelValue","onUpdate:modelValue","onChange"]),me(i(h,{modelValue:b.row.const,"onUpdate:modelValue":M=>b.row.const=M,style:{"box-shadow":"0 0 0 1px var(--el-input-border-color,var(--el-border-color)) inset","margin-left":"5px"}},null,8,["modelValue","onUpdate:modelValue"]),[[bt,b.row.isConstSwitchOn||b.row.const]])]),_:1}),i(g,{prop:"",label:"是否必填"},{default:p(b=>[i(A,{modelValue:b.row.required,"onUpdate:modelValue":M=>b.row.required=M,style:{"--el-switch-on-color":"#129BFF"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(g,{prop:"",label:"操作",fixed:"right",width:"100px"},{default:p(b=>[i(x,{link:"",type:"primary",size:"small",onClick:M=>t.deleteBodyResponseRow("body",b.row)},{default:p(()=>[Q(" 删除 ")]),_:2},1032,["onClick"]),b.row.type==="array"||b.row.type==="object"?(a(),X(x,{key:0,link:"",type:"primary",size:"small",onClick:M=>t.onBodyResponseAddChildItem("body",b.row)},{default:p(()=>[Q(" 添加 ")]),_:2},1032,["onClick"])):z("",!0)]),_:1})]),_:1},8,["data"]),i(x,{type:"primary",link:"",onClick:o[4]||(o[4]=b=>t.onAddTableItem("body")),class:"add-item-btn"},{default:p(()=>[Q(" + 添加参数")]),_:1})]),e("div",Gg,[Wg,i(y,{data:t.responseTableData,style:{width:"640px"},"row-key":"id",border:"","default-expand-all":""},{default:p(()=>[i(g,{prop:"",label:"字段key",width:"200px"},{default:p(b=>[i(h,{modelValue:b.row.key,"onUpdate:modelValue":M=>b.row.key=M,style:{width:"90%"},disabled:t.isKeyDisabled(b.row,t.responseTableData)},null,8,["modelValue","onUpdate:modelValue","disabled"])]),_:1}),i(g,{prop:"、",label:"字段描述"},{default:p(b=>[i(h,{modelValue:b.row.description,"onUpdate:modelValue":M=>b.row.description=M,style:{width:"90%"}},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(g,{prop:"",label:"字段类型"},{default:p(b=>[i(T,{modelValue:b.row.type,"onUpdate:modelValue":M=>b.row.type=M,placeholder:"选择类型",size:"large",style:{width:"90%"}},{default:p(()=>[(a(!0),c(N,null,le(t.responseBodyType,M=>(a(),X(w,{key:M.value,label:M.label,value:M.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),i(g,{prop:"",label:"操作",fixed:"right",width:"100px"},{default:p(b=>[i(x,{link:"",type:"primary",size:"small",onClick:M=>t.deleteBodyResponseRow("response",b.row)},{default:p(()=>[Q(" 删除 ")]),_:2},1032,["onClick"]),b.row.type==="array"||b.row.type==="object"?(a(),X(x,{key:0,link:"",type:"primary",size:"small",onClick:M=>t.onBodyResponseAddChildItem("response",b.row)},{default:p(()=>[Q(" 添加 ")]),_:2},1032,["onClick"])):z("",!0)]),_:1})]),_:1},8,["data"]),i(x,{type:"primary",link:"",onClick:o[5]||(o[5]=b=>t.onAddTableItem("response")),class:"add-item-btn"},{default:p(()=>[Q(" + 添加参数")]),_:1})]),t.canPerformAction?(a(),c("div",Xg,[e("button",{type:"submit",class:"cc-button font-zhongcu common-confirm-btn",onClick:o[6]||(o[6]=(...b)=>t.submitData&&t.submitData(...b))},"保存")])):z("",!0)]),_:1}),e("div",e3,[i(L,{class:"breadcrumb",style:{"padding-left":"0"},breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})}const o3=ue(Rg,[["render",t3],["__scopeId","data-v-e2dc8a9b"]]),s3={class:"auth-box mt-[10px] flex items-center justify-between px-[12px]"},n3={class:"w-10/12 flex gap-2.5"},a3={class:"head-img"},l3={class:"mr-[11px]"},i3={__name:"permissions",props:{siteuserList:{},siteuserListModifiers:{},siteuserIds:{},siteuserIdsModifiers:{}},emits:["update:siteuserList","update:siteuserIds"],setup(s){const o=As(s,"siteuserList"),l=As(s,"siteuserIds"),t=n(!1),d=u=>{o.value=u,l.value=u.map(r=>r.id)},m=()=>{t.value=!t.value};return(u,r)=>(a(),c(N,null,[e("div",s3,[e("div",n3,[(a(!0),c(N,null,le(o.value.slice(0,4),v=>(a(),c("div",{class:"site-user font-zhongcu",key:v.id},[e("div",a3,S(oe(He)(v.name)),1),e("div",l3,S(v.name),1)]))),128))]),i(oe(tt),{size:"20",class:"",onClick:m},{default:p(()=>[i(oe(vt))]),_:1})]),t.value?(a(),X(Us,{key:0,type:"tool",sids:l.value,onValueFromChild:d,closeModal:m},null,8,["sids"])):z("",!0)],64))}},c3=ue(i3,[["__scopeId","data-v-c348e3f0"]]),r3={name:"ToolSettingView",components:{HeaderLeft:Gt,Permissions:c3,CirclePlus:vt,ToolEditNav:qs,BreadCrumbComponent:Ee,BaseNavComponennt:Me,Main:Wt,MainHeader:Xt,ArrowLeftBold:$e,BaseNavComponent:Me,ElIcon:tt,CaretTop:vn,CaretBottom:pn,CloseBold:Lt,Plus:hn},setup(){const s=De(),o=Ce(),l=n("tool"),t=n("setting"),d=n(!1),m=n(null),u=n(""),r=n(""),v=n(""),k=n(null),h=n(!1),g=n([]),x=n([]),y=n(!1),w=n("kbase"),T=n(!1),A=n([{path:"/tool-list",name:"工具"},{path:"",name:"使用权限"}]),C=V=>{d.value=ze(V,["create_tool"]),d.value||(d.value=ze(V,["manage_tool"]))},L=V=>{const f=g.value.findIndex(U=>U.id===V);f!==-1&&g.value.splice(f,1);const I=x.value.indexOf(V);I!==-1&&x.value.splice(I,1)},B=()=>{h.value=!h.value},P=()=>{y.value=!1,h.value=!1},b=V=>{const f=V,I=new Set(g.value.map(U=>U.id));f.forEach(U=>{I.has(U.id)||(g.value.push(U),x.value.push(U.id))})},M=async()=>{try{const V=await Ts(m.value);if(V.data.error=="1")o.push({name:"ToolList"});else{const f=V.data;u.value=f.name,r.value=f.name,v.value=f.description,k.value=f.typestr,V.data.siteusers.forEach(I=>{g.value.push({id:I.id,name:I.name,nameOne:He(I.name)}),x.value.push(I.id)})}}catch{o.push({name:"ToolList"})}},D=async()=>{if(!T.value){T.value=!0;try{const V=await kl(m.value,u.value,v.value,x.value.join("|"));V.data.error=="0"?(E.success("保存成功"),r.value=u.value):V.data.message?E.error(V.data.message):E.error("保存失败"),P()}catch{o.push({name:"ToolList"})}finally{P(),T.value=!1}}};return ge(()=>{m.value=s.params.tid,M()}),{selectedBar:l,selectedKBaseNav:t,handleDataPermissions:C,toolName:u,toolName1:r,toolDescription:v,toolType:k,saveTool:D,handleValueFromChild:b,showAllSiteuser:B,removeSiteuser:L,type:w,siteuserList:g,pullDown:h,canPerformAction:d,showModal:y,closeModal:P,getFirstLetter:He,siteuserIds:x,breadcrumbs:A}}},Oo=s=>(be("data-v-4581ad8f"),s=s(),we(),s),d3={class:"mb-[80px]"},u3={class:"mt-8"},m3=Oo(()=>e("div",{class:"text-base font-bold"}," 基本信息 ",-1)),p3={class:"mt-5 new-modal-center-item"},v3=Oo(()=>e("div",{class:"cc-label font-zhongcu"},"工具名称",-1)),h3={class:"mt-[15px]"},f3=Oo(()=>e("div",{class:"cc-label font-zhongcu"},"工具介绍",-1)),_3={class:"mt-[15px] new-modal-center-item w-[650px]"},g3=Oo(()=>e("div",{class:"cc-label font-zhongcu"},"使用权限",-1)),A3={key:0,class:"mt-[30px]"},y3={class:"breadcrumb-container"};function b3(s,o,l,t,d,m){const u=_("BaseNavComponennt"),r=_("HeaderLeft"),v=_("MainHeader"),k=_("tool-edit-nav"),h=_("permissions"),g=_("BreadCrumbComponent"),x=_("Main"),y=_("el-row");return a(),X(y,{class:"h-screen w-full"},{default:p(()=>[i(u,{selectedBar:t.selectedBar,onDataPermissions:t.handleDataPermissions},null,8,["selectedBar","onDataPermissions"]),i(x,null,{head:p(()=>[i(v,null,{left:p(()=>[i(r,{text:t.toolName,to:{name:"ToolList"},submit:t.saveTool},null,8,["text","submit"])]),right:p(()=>[]),_:1})]),main:p(()=>[i(k,{"can-perform-action":t.canPerformAction,"tool-name":t.toolName,"selected-k-base-nav":t.selectedKBaseNav,"tool-type":t.toolType},null,8,["can-perform-action","tool-name","selected-k-base-nav","tool-type"]),e("div",d3,[e("form",{onSubmit:o[5]||(o[5]=_e(()=>{},["prevent"]))},[e("div",u3,[m3,e("div",p3,[v3,me(e("input",{type:"text",class:"mt-2.5 name-input","onUpdate:modelValue":o[0]||(o[0]=w=>t.toolName=w),placeholder:"输入工具名称",maxlength:"50",required:""},null,512),[[Ae,t.toolName]])]),e("div",h3,[f3,me(e("textarea",{id:"description",class:"mt-[10px] des-textarea","onUpdate:modelValue":o[1]||(o[1]=w=>t.toolDescription=w),placeholder:"输入工具介绍",maxlength:"100"},null,512),[[Ae,t.toolDescription]])]),e("div",_3,[g3,i(h,{siteuserList:t.siteuserList,"onUpdate:siteuserList":o[2]||(o[2]=w=>t.siteuserList=w),siteuserIds:t.siteuserIds,"onUpdate:siteuserIds":o[3]||(o[3]=w=>t.siteuserIds=w)},null,8,["siteuserList","siteuserIds"])]),t.canPerformAction?(a(),c("div",A3,[e("button",{class:"submit font-zhongcu common-confirm-btn",type:"submit",onClick:o[4]||(o[4]=(...w)=>t.saveTool&&t.saveTool(...w))},"保存")])):z("",!0)])],32)]),e("div",y3,[i(g,{class:"breadcrumb",style:{"padding-left":"0"},breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})}const w3=ue(r3,[["render",b3],["__scopeId","data-v-4581ad8f"]]),bs=s=>{for(let o in s)if(typeof s[o]=="string")try{s[o]=JSON.parse(s[o]),bs(s[o])}catch{}else typeof s[o]=="object"&&s[o]!==null&&bs(s[o]);return s};function gs(s){let o;if(typeof s=="string")try{o=JSON.parse(s)}catch{return`\`\`\`text
 ${s}
 \`\`\``}else if(typeof s=="object"&&s!==null)o=s;else return`\`\`\`text
 ${String(s)}
 \`\`\``;return o=bs(o),`\`\`\`json
 ${JSON.stringify(o,null,2)}
 \`\`\``}const C3={name:"ToolTestView",components:{BreadCrumbComponent:Ee,HeaderLeft:Gt,ToolEditNav:qs,MainHeader:Xt,BaseNavComponent:Me,ArrowLeftBold:$e,Main:Wt},setup(){const s=Ce(),o=De(),l=n("tool"),t=n("test"),d=n(!1),m=n(null),u=n(""),r=n(!1),v=n([]),k=n([]),h=n(""),g=n(""),x=n(""),y=n([{path:"/tool-list",name:"工具"},{path:"",name:"接口测试"}]),w=B=>{d.value=ze(B,["create_tool","manage_tool"])},T=B=>{if(Object.keys(B.properties).length===0&&B.properties.constructor===Object)return[];{const P=[],b=(M,D="")=>{for(const V in M){const f=D?`${D}.${V}`:V;let I=!1;B.required&&(I=B.required.includes(V)),M[V].type==="object"?b(M[V].properties,f):M[V].type==="array"?M[V].items.type==="object"?b(M[V].items.properties,f):M[V].const||P.push({key:f,displayName:V,const:M[V].const,type:M[V].items.type,required:I}):M[V].const||P.push({key:f,displayName:V,const:M[V].const,type:M[V].type,required:I})}};return b(B.properties),P}},A=async()=>{const B=await Ts(m.value);if(B.data.error=="1"||B.data.error=="404")s.push({name:"ToolList"});else{const P=B.data;u.value=P.name,P.apiqueryparams&&(v.value=T(JSON.parse(P.apiqueryparams))),P.apibody&&(k.value=T(JSON.parse(P.apibody)))}};ge(()=>{m.value=o.params.tid,A()});const C=(B,P,b)=>{let M=B;for(let D=0;D<P.length-1;D++)M[P[D]]||(M[P[D]]=isNaN(P[D+1])?{}:[]),M=M[P[D]];M[P[P.length-1]]=b};return{selectedBar:l,selectedKBaseNav:t,toolId:m,toolName:u,handleDataPermissions:w,paramsTableData:v,bodyTableData:k,submitData:async()=>{if(r.value)return;r.value=!0;const B={},P={};if(v.value.forEach(b=>{const M=b.key.split(".");C(B,M,b.const)}),k.value.forEach(b=>{const M=b.key.split(".");C(P,M,b.const)}),Object.keys(B).length===0&&Object.keys(P).length===0){E("请先设置接口数据"),r.value=!1;return}try{const b=await Il(m.value,JSON.stringify(B),JSON.stringify(P));b.data.error=="0"?(h.value=Xe(gs(b.data.请求?b.data.请求:"{}")),g.value=Xe(gs(b.data.响应?b.data.响应:"{}")),x.value=Xe(gs(b.data.返回值?b.data.返回值:"{}"))):b.data.message&&E.error(b.data.message)}finally{r.value=!1}},canPerformAction:d,responseData:g,responseBodyData:x,requestData:h,breadcrumbs:y}}},Co=s=>(be("data-v-dfd4bf90"),s=s(),we(),s),k3={class:"mb-[12vh]"},x3=Co(()=>e("div",{class:"cc-form-title mt-[35px]"}," 基本信息 ",-1)),S3={key:0,class:"mt-[20px]"},I3={class:"cc-label font-zhongcu"},B3=["onUpdate:modelValue","required"],T3={key:0,class:"mt-[20px]"},M3={class:"cc-label font-zhongcu"},D3=["onUpdate:modelValue","required"],V3=Co(()=>e("div",{class:"cc-label font-zhongcu mt-[20px]"},"请求：",-1)),E3=["innerHTML"],L3=Co(()=>e("div",{class:"cc-label font-zhongcu mt-[20px]"},"响应：",-1)),z3=["innerHTML"],F3=Co(()=>e("div",{class:"cc-label font-zhongcu mt-[20px]"},"返回值：",-1)),U3=["innerHTML"],P3=Co(()=>e("div",{class:"mt-[20px]"},[e("button",{type:"submit",class:"submit font-zhongcu common-confirm-btn"},"测试")],-1)),R3={class:"breadcrumb-container"};function N3(s,o,l,t,d,m){const u=_("BaseNavComponent"),r=_("HeaderLeft"),v=_("MainHeader"),k=_("tool-edit-nav"),h=_("el-switch"),g=_("BreadCrumbComponent"),x=_("Main"),y=_("el-row");return a(),X(y,{class:"h-screen w-full"},{default:p(()=>[i(u,{selectedBar:t.selectedBar,onDataPermissions:t.handleDataPermissions},null,8,["selectedBar","onDataPermissions"]),i(x,null,{head:p(()=>[i(v,null,{left:p(()=>[i(r,{text:t.toolName,to:{name:"ToolList"},submit:t.submitData},null,8,["text","submit"])]),right:p(()=>[]),_:1})]),main:p(()=>[i(k,{"can-perform-action":t.canPerformAction,"tool-name":t.toolName,"selected-k-base-nav":t.selectedKBaseNav},null,8,["can-perform-action","tool-name","selected-k-base-nav"]),e("div",k3,[x3,e("form",{onSubmit:o[0]||(o[0]=_e((...w)=>t.submitData&&t.submitData(...w),["prevent"]))},[(a(!0),c(N,null,le(t.paramsTableData,(w,T)=>(a(),c(N,{key:T},[w.key?(a(),c("div",S3,[e("div",I3,S(w.displayName),1),w.type!="boolean"?me((a(),c("input",{key:0,"onUpdate:modelValue":A=>w.const=A,placeholder:"输入内容",class:"cur-input mt-[10px]",required:w.required},null,8,B3)),[[Ae,w.const]]):(a(),X(h,{key:1,modelValue:w.isConstSwitchOn,"onUpdate:modelValue":A=>w.isConstSwitchOn=A,style:{}},null,8,["modelValue","onUpdate:modelValue"]))])):z("",!0)],64))),128)),(a(!0),c(N,null,le(t.bodyTableData,(w,T)=>(a(),c(N,{key:T},[w.key?(a(),c("div",T3,[e("div",M3,S(w.displayName),1),w.type!="boolean"?me((a(),c("input",{key:0,"onUpdate:modelValue":A=>w.const=A,placeholder:"输入内容",class:"cur-input mt-[10px]",required:w.required},null,8,D3)),[[Ae,w.const]]):(a(),X(h,{key:1,modelValue:w.isConstSwitchOn,"onUpdate:modelValue":A=>w.isConstSwitchOn=A,style:{"--el-switch-on-color":"#129BFF"}},null,8,["modelValue","onUpdate:modelValue"]))])):z("",!0)],64))),128)),V3,e("div",{class:"http-box mt-[10px]",innerHTML:t.requestData},null,8,E3),L3,e("div",{class:"http-box mt-[10px]",innerHTML:t.responseData},null,8,z3),F3,e("div",{class:"http-box mt-[10px]",innerHTML:t.responseBodyData},null,8,U3),P3],32)]),e("div",R3,[i(g,{class:"breadcrumb",style:{"padding-left":"0"},breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})}const q3=ue(C3,[["render",N3],["__scopeId","data-v-dfd4bf90"]]),K3={__name:"test",setup(s){const o=new URL("/nq/static/splinecode/scene-Ci9wC1bU.splinecode",import.meta.url).href;n({url:o});const l=n(null),t=n([]),d=n(null),m=()=>{t.value=[["老板","同级","老板1"],["老板","下属","经理"],["老板","同事","人事"],["经理","同事","经理2"],["经理","下属","领导"],["领导","下属","员工1"],["领导","下属","员工2"],["领导","下属","员工3"],["员工3","下属","员工3.1"]],u()},u=()=>{const r=new Set,v=[];t.value.forEach(([g,x,y])=>{r.add(g),r.add(y),v.push({source:g,label:x,target:y})});const h={series:[{type:"graph",data:Array.from(r).map(g=>({name:g,draggable:!0})),links:v,roam:!0,edgeSymbol:["circle"],edgeSymbolSize:[4,10],edgeLabel:{show:!0,formatter:function(g){return g.data?g.data.label:""}},lineStyle:{curveness:0},draggable:!0,layout:"force",force:{repulsion:1e3,edgeLength:100,layoutAnimation:!0},label:{show:!0}}]};d.value&&d.value.dispose(),d.value=No(l.value),d.value.setOption(h),d.value.on("mouseover",function(g){g.componentType==="series"&&(d.value.dispatchAction({type:"scaleNode",seriesIndex:g.seriesIndex,dataIndex:g.dataIndex,scale:1.2}),d.value.dispatchAction({type:"scaleNode",seriesIndex:g.seriesIndex,dataIndex:g.dataIndex,scale:.5}))}),d.value.on("mouseout",function(g){d.value.dispatchAction({type:"scaleNodeToOrigin",seriesIndex:g.seriesIndex})}),d.value.on("dragstart",function(g){const x=g.data;d.value.dispatchAction({type:"force",data:x,fixed:!0})}),d.value.on("dragend",function(g){const x=g.data,y=[g.event.offsetX,g.event.offsetY];d.value.dispatchAction({type:"force",data:x,fixed:!1}),d.value.dispatchAction({type:"update",seriesIndex:0,data:x,dataIndex:g.dataIndex}),d.value.setOption({series:[{data:d.value.getOption().series[0].data.map((w,T)=>T===g.dataIndex?{...w,x:y[0],y:y[1],fixed:!0}:w)}]})})};return ge(()=>{m()}),(r,v)=>(a(),c("div",null,[e("button",{onClick:m},"渲染关系图"),e("div",{ref_key:"chartRef",ref:l,style:{width:"600px",height:"400px"}},null,512)]))}},H3={name:"DocAddTextView",components:{BaseNavComponent:Me,DocProgressComponent:Nn,BreadCrumbComponent:Ee,ElIcon:tt,ArrowLeftBold:$e},setup(){const s=n("kbase"),o=De(),l=n("知识库"),t=n("doc"),d=n(null),m=n(1),u=n("base"),r=n(null),v=n([]),k=n(null),h=n(""),g=n(""),x=n([]),y=n([]),w=n(!1),T=n(""),A=n([]),C=n(null),L=V=>{u.value=V},B=async()=>{try{const V=await po(d.value,r.value);V.data.message?E.error(V.data.message):(h.value=V.data.title,g.value=V.data.content,u.value=V.data.learn_type,r.value=V.data.id)}catch{r.value=null,m.value=1}},P=async()=>{const V=await jt(d.value);return V.data.error=="0"?`知识库（${V.data.title}）`:"知识库"};ge(async()=>{d.value=o.params.kid,r.value=o.params.dcid,r.value&&B();let V="";V=await P(),y.value=[{path:"/kbase-list",name:V},{path:`/doc-list/${d.value}`,name:"文档"},{path:"",name:"自定义文本"}]}),ks(()=>{k.value&&clearInterval(k.value)});const b=Ze(async()=>{if(!h.value||!g.value){E("请将内容填写完成");return}else{C.value=Re.service({lock:!0,text:"提交中",background:"rgba(0, 0, 0, 0.5)"}),x.value.push({name:h.value,status:"待学习"});try{const V=await xn(d.value,u.value,"text",h.value,g.value,"");V.data.error=="0"?(r.value=V.data.id,C.value.close(),m.value=2,M(),k.value&&clearInterval(k.value),k.value=setInterval(M,2e3)):V.data.message&&E.error(V.data.message)}catch{E.error("学习失败")}}}),M=async()=>{try{const V=await po(d.value,r.value);if(V.data.error==="0"){const f=v.value.findIndex(U=>U.id===V.data.id);f!==-1?v.value[f].status!==V.data.status&&(v.value[f].status=V.data.status,v.value[f].status_display=V.data.status_display):v.value.push({title:`${V.data.title}.${V.data.type}`,status:V.data.status,status_display:V.data.status_display,id:V.data.id}),v.value.every(U=>U.status==="success")&&(clearInterval(k.value),m.value=3)}}catch{}};return{selectedBar:s,kbaseName:l,selectedKBaseNav:t,selectedKid:d,step:m,selectedType:u,chooseType:L,docName:h,docContent:g,fileList:x,items:v,AddDocText:b,breadcrumbs:y,content:T,searchTest:async()=>{if(!w.value){if(w.value=!0,T.value==""||!T.value){E.error("请输入要测试的文本"),w.value=!1;return}C.value=Re.service({lock:!0,text:"搜索中",background:"rgba(0, 0, 0, 0.5)"}),A.value=[];try{const V=await Bs(d.value,T.value);V.data.message?E.error(V.data.message):A.value=V.data}catch{C.value.close(),E.error("搜索失败")}finally{w.value=!1,C.value.close()}}},results:A,getFileIcon:Ko}}},st=s=>(be("data-v-6b927365"),s=s(),we(),s),Q3={class:"common-layout",style:{height:"100vh"}},O3={class:"el-main-container"},Z3={class:"el-main-top"},$3={class:"el-main-top-left font18 font-zhongcu"},Y3={class:"el-main-right-bottom overflow-y"},j3={key:0,class:"upload-step1"},J3={class:"upload-step2-item"},G3=st(()=>e("div",{class:"upload-step2-item-title font16 font-zhongcu"},[e("img",{src:qn,alt:""}),Q(" 数据训练模式 ")],-1)),W3={class:"upload-step2-item-bottom"},X3=["checked"],eA=st(()=>e("div",{class:"upload-step2-item-type-desc"},[e("div",{class:"font16 font-zhongcu"},"基础版"),e("div",{class:"font12"},"数据学习新手版，带你走进数据世界的大门。")],-1)),tA=["checked"],oA=st(()=>e("div",{class:"upload-step2-item-type-desc"},[e("div",{class:"font16 font-zhongcu"},"普通版"),e("div",{class:"font12"},"数据技能加强版，让你数据操作更溜更顺畅。")],-1)),sA=["checked"],nA=st(()=>e("div",{class:"upload-step2-item-type-desc"},[e("div",{class:"font16 font-zhongcu"},"高级版"),e("div",{class:"font12"},"数据洞察大师版，带你深度挖掘数据背后的故事。")],-1)),aA={class:"upload-step1-left"},lA={class:"upload-step1-left-item"},iA=st(()=>e("div",{class:"font16 font-zhongcu"},"数据集名称",-1)),cA={class:"upload-step1-left-item upload-step1-left-item-txt"},rA=st(()=>e("div",{class:"font16 font-zhongcu"},"数据集内容",-1)),dA={key:1,class:"upload-step3"},uA={class:"upload-step3-file-list overflow-y"},mA=st(()=>e("div",{class:"el-col-head font14"},[e("div",null,"#"),e("div",null,"来源名称"),e("div",null,"状态")],-1)),pA={class:"font-zhongcu"},vA={class:"font-zhongcu overflow-one"},hA=st(()=>e("img",{src:Po,alt:"file icon",class:"file-icon"},null,-1)),fA=st(()=>e("div",{class:"font-zhongcu el-col-item-status"},[Q(" 学习中 "),e("img",{src:Jt,alt:""})],-1)),_A=st(()=>e("button",{class:"upload-btn font14 font-zhongcu",style:{background:"#ACD6F9"}},"学习中",-1)),gA={key:2,class:"upload-step4"},AA={class:"upload-step4-left"},yA=st(()=>e("div",{class:"font16 font-zhongcu"},"搜索测试",-1)),bA={class:"upload-step4-left-btn-div font14"},wA={class:"upload-step4-right"},CA=st(()=>e("div",{class:"font16 font-zhongcu",style:{"margin-bottom":"25px"}},"测试结果",-1)),kA={key:0,class:"upload-step4-right-result overflow-y"},xA={class:"result-item-top font12"},SA={class:"result-item-content font14"},IA={class:"result-item-bottom font12"},BA=["src"],TA={class:"file-name"},MA={class:"breadcrumb-container"};function DA(s,o,l,t,d,m){const u=_("BaseNavComponent"),r=_("ArrowLeftBold"),v=_("el-icon"),k=_("router-link"),h=_("DocProgressComponent"),g=_("BreadCrumbComponent"),x=_("el-main"),y=_("el-container");return a(),c("div",Q3,[i(y,{style:{height:"100vh"},class:"el-container-base"},{default:p(()=>[i(u,{selectedBar:t.selectedBar},null,8,["selectedBar"]),i(x,{class:"el-main common-layout-bg"},{default:p(()=>[e("div",O3,[e("div",Z3,[e("div",$3,[t.selectedKid?(a(),X(k,{key:0,to:{name:"DocList",params:{kid:t.selectedKid}},class:"el-main-top-left-back"},{default:p(()=>[i(v,{color:"#666666",size:"16"},{default:p(()=>[i(r)]),_:1})]),_:1},8,["to"])):z("",!0),Q(" 自定义文本 ")])]),e("div",Y3,[i(h,{step:t.step,typestr:"text"},null,8,["step"]),t.step==1?(a(),c("div",j3,[e("div",J3,[G3,e("div",W3,[e("label",{onChange:o[0]||(o[0]=w=>t.chooseType("base")),class:W(["upload-step2-item-type",t.selectedType=="base"?"upload-step2-item-type-active":""])},[e("input",{type:"radio",name:"type",value:"base",checked:t.selectedType=="base"},null,8,X3),eA],34),e("label",{onClick:o[1]||(o[1]=w=>t.chooseType("primary")),class:W(["upload-step2-item-type",t.selectedType=="primary"?"upload-step2-item-type-active":""])},[e("input",{type:"radio",name:"type",value:"primary",checked:t.selectedType=="primary"},null,8,tA),oA],2),e("label",{onClick:o[2]||(o[2]=w=>t.chooseType("advance")),class:W(["upload-step2-item-type",t.selectedType=="advance"?"upload-step2-item-type-active":""])},[e("input",{type:"radio",name:"type",value:"advance",checked:t.selectedType=="advance"},null,8,sA),nA],2)])]),e("div",aA,[e("div",lA,[iA,me(e("input",{type:"text",class:"el-main-item-ipt font14 font-zhongcu","onUpdate:modelValue":o[3]||(o[3]=w=>t.docName=w),placeholder:"输入名称",maxlength:"50"},null,512),[[Ae,t.docName]])]),e("div",cA,[rA,me(e("textarea",{"onUpdate:modelValue":o[4]||(o[4]=w=>t.docContent=w),maxlength:"20000",placeholder:"输入内容",class:"font14 font-zhongcu"},null,512),[[Ae,t.docContent]])]),e("button",{class:"upload-btn font14 font-zhongcu common-confirm-btn",onClick:o[5]||(o[5]=(...w)=>t.AddDocText&&t.AddDocText(...w))},"开始学习")])])):z("",!0),t.step==2?(a(),c("div",dA,[e("div",uA,[mA,(a(!0),c(N,null,le(t.fileList,(w,T)=>(a(),c("div",{class:"el-col-item font14",key:T},[e("div",pA,S(T+1),1),e("div",vA,[hA,Q(" "+S(w.name),1)]),fA]))),128))]),_A])):z("",!0),t.step==3?(a(),c("div",gA,[e("div",AA,[yA,me(e("textarea",{"onUpdate:modelValue":o[6]||(o[6]=w=>t.content=w),class:"upload-step4-left-txt overflow-y font14 font-zhongcu",placeholder:"输入要测试的文本"},null,512),[[Ae,t.content]]),e("div",bA,[t.selectedKid?(a(),X(k,{key:0,to:{name:"DocList",params:{kid:t.selectedKid}},class:"upload-step4-left-btn upload-btn common-cancel-btn"},{default:p(()=>[Q("跳过")]),_:1},8,["to"])):z("",!0),e("button",{type:"primary",color:"#129BFF",onClick:o[7]||(o[7]=(...w)=>t.searchTest&&t.searchTest(...w)),class:"upload-step4-left-btn upload-btn common-confirm-btn"},"测试")])]),e("div",wA,[CA,t.results.length>0?(a(),c("div",kA,[(a(!0),c(N,null,le(t.results,(w,T)=>(a(),c("div",{class:"result-item",key:T},[e("div",xA,"# "+S(T+1)+" | 语义检索 "+S(w.docfragment_score.toFixed(4)),1),e("div",SA,S(w.docfragment),1),e("div",IA,[e("img",{src:t.getFileIcon(w.document_type),alt:"file icon",class:"file-icon"},null,8,BA),e("span",TA,S(w.document_title)+"."+S(w.document_type),1)])]))),128))])):z("",!0)])])):z("",!0)])]),e("div",MA,[i(g,{class:"breadcrumb",breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})])}const VA=ue(H3,[["render",DA],["__scopeId","data-v-6b927365"]]),Ct=s=>(be("data-v-e55698b8"),s=s(),we(),s),EA={class:"container-center"},LA=Ct(()=>e("div",{class:"container-top-mengban"},null,-1)),zA={class:"conatiner-message-div flex w-full h-full flex-col justify-between"},FA={ref:"innerRef"},UA={key:0,class:"opening-statement-div"},PA=Ct(()=>e("div",{class:"overflow-one"},"Hello，我是FEMA",-1)),RA=Ct(()=>e("div",{class:"overflow-one"},"让我们一起度过美好的一天！",-1)),NA={class:"question-example-div"},qA=Ct(()=>e("div",{class:"font-zhongcu font16",style:{color:"#666"}}," 你可以尝试下面的示例… ",-1)),KA={class:"question-list"},HA=["onClick"],QA={class:"question-item-left font18 font-zhongcu"},OA={class:"question-item-right"},ZA={class:"font14 overflow-five question-item-right-title"},$A={key:1},YA={key:0,class:W(["chat-item","taskmessage-user"])},jA=Ct(()=>e("div",{class:"chat-item-ai-headimg-div"},[e("img",{src:Tt,alt:"",class:"chat-item-ai-headimg"})],-1)),JA={class:"chat-item-content taskmessage-user-content"},GA=["innerHTML"],WA={key:1,class:W(["chat-item","taskmessage-ai"])},XA={class:"chat-item-ai-headimg-div"},e6={key:0,src:Es,alt:"",style:{width:"25px",height:"25px"}},t6={key:1,src:Tt,alt:"",class:"chat-item-ai-headimg"},o6={class:"chat-item-content taskmessage-ai-content"},s6=["innerHTML"],n6={key:0,class:"font14",style:{}},a6={key:0,src:Jt,alt:"",style:{width:"18px",height:"18px"}},l6={class:"taskmessage-add"},i6=["disabled"],c6=Ct(()=>e("img",{src:ho,alt:""},null,-1)),r6=[c6],d6=Ct(()=>e("img",{src:zs,alt:"",class:"gongzuotai-img"},null,-1)),u6=[d6],m6={class:"canvans-box"},p6=Ct(()=>e("div",{class:"font-zhongcu font20 canvans-title"},"工作台",-1)),v6=Ct(()=>e("div",{class:"canvans-bg"},null,-1)),nn="开始参考资料分析",h6={__name:"testDoc",setup(s){const o=n(null),l=n([]),t=n(null),d=n(!1),m=n(!0),u=async()=>{try{const I=await mo.get("http://192.168.0.8:8080/fmea/api/v1/get-fmea-graph");I.data.length<=0?m.value=!1:(d.value=!0,l.value=I.data,await r())}catch(I){console.error(I)}},r=()=>{const I=new Set,U=[];l.value.forEach(Y=>{I.add(Y.m),I.add(Y.n),U.push({source:Y.m,target:Y.n,value:Math.random()*10+1})});const O={series:[{type:"graph",data:Array.from(I).map(Y=>({name:Y,draggable:!0,symbolSize:v(20,50)})),links:U,zoom:.2,roam:!0,edgeSymbol:["circle"],edgeSymbolSize:[4,10],lineStyle:{curveness:0},draggable:!0,layout:"force",force:{repulsion:1e4,edgeLength:100,layoutAnimation:!0},emphasis:{scale:1.5,focus:"adjacency",label:{position:"right",show:!0}}}]};t.value&&t.value.dispose(),t.value=No(o.value),t.value.setOption(O),t.value.on("mouseover",function(Y){Y.componentType==="series"&&(t.value.dispatchAction({type:"scaleNode",seriesIndex:Y.seriesIndex,dataIndex:Y.dataIndex,scale:1.2}),t.value.dispatchAction({type:"scaleNode",seriesIndex:Y.seriesIndex,dataIndex:Y.dataIndex,scale:.5}))}),t.value.on("mouseover",function(Y){Y.componentType==="series"&&(t.value.dispatchAction({type:"scaleNodeToOrigin",seriesIndex:Y.seriesIndex}),t.value.dispatchAction({type:"scaleNode",seriesIndex:Y.seriesIndex,dataIndex:Y.dataIndex,scale:5}),t.value.dispatchAction({type:"scaleNode",seriesIndex:Y.seriesIndex,scale:.5}))}),t.value.on("mouseout",function(Y){t.value.dispatchAction({type:"scaleNodeToOrigin",seriesIndex:Y.seriesIndex})}),t.value.on("dragstart",function(Y){const R=Y.data;t.value.dispatchAction({type:"force",data:R,fixed:!0})}),t.value.on("dragend",function(Y){const R=Y.data,j=[Y.event.offsetX,Y.event.offsetY];t.value.dispatchAction({type:"force",data:R,fixed:!1}),t.value.dispatchAction({type:"update",seriesIndex:0,data:R,dataIndex:Y.dataIndex}),t.value.setOption({series:[{data:t.value.getOption().series[0].data.map((ee,K)=>K===Y.dataIndex?{...ee,x:j[0],y:j[1],fixed:!0}:ee)}]})})},v=(I,U)=>Math.floor(Math.random()*(U-I+1))+I,k=n([]),h=n(!1),g=n(""),x=n(!0),y=n(!1),w=n(""),T=n(null),A=n(!1),C=n(!1),L=n(["RPZ大于30的FailureEffect有几个？","失效效应（FailureEffect）Components misaligned的失效原因(FailureCause）是什么？","Soldering temperature too low最可能造成什么失效效应？"]);Pe(g,I=>{I!==""?h.value=!0:h.value=!1});const B=()=>{pt(()=>{const I=T.value;I&&(I.scrollTop=I.scrollHeight)})};Pe(k,async()=>{for(let I=k.value.length-1;I>=0;I--){const U=k.value[I].attachments_data;if(U&&U.length>0){curPreview.value=U[U.length-1],U[U.length-1].file_url.endsWith(".docx")&&(showPreview.value=!0);break}}await pt(),document.querySelectorAll("pre code").forEach(I=>{I.dataset.highlighted||(Ke.highlightElement(I),I.dataset.highlighted="yes")})}),Xe.setOptions({highlight:function(I,U){return Ke.getLanguage(U)?Ke.highlight(I,{language:U}).value:Ke.highlightAuto(I).value}});const P=I=>Xe(I),b=()=>{let I=0;w.value="";const U=setInterval(()=>{I<nn.length?(w.value+=nn[I],I++):(clearInterval(U),C.value=!0)},100)},M=I=>{if(I.key==="Enter"){if(g.value.trim()===""){I.preventDefault();return}I.shiftKey||I.ctrlKey?(g.value+=`
`,I.preventDefault()):(V(),console.log(g.value,"发哦是那个"))}};Pe(g,I=>{I!==""?h.value=!0:h.value=!1});const D=async I=>{f(I)},V=async()=>{const I=g.value;g.value="",f(I)},f=async I=>{const U={role:"user",content:I,taskmessage_id:""};k.value.push(U);let J={role:"assistant",content:"",taskmessage_id:""};k.value.push(J),B(),y.value=!0,b(),x.value=!1,A.value=!0;try{const O=await mo.post("http://192.168.0.8:8080/fmea/api/v1/question-answer",{question:I},{headers:{"Content-Type":"application/json"}});console.log(O.data),A.value=!1,y.value=!1,C.value=!1,x.value=!0,J.content="你好啊阿斯顿阿三",k.value=[...k.value],B()}catch{y.value=!1,C.value=!1,x.value=!0,k.value.pop(),k.value.push({role:"assistant",content:"当前请求网络可能有问题，请重新发起对话"}),B()}};return ge(()=>{u()}),(I,U)=>{const J=_("el-input"),O=_("el-main"),Y=_("el-aside"),R=_("el-container");return a(),X(R,{style:{background:`linear-gradient(
        180deg,
        #f5fbff 0%,
        rgba(245, 251, 255, 0.93) 45%,
        rgba(244, 250, 255, 0) 100%
      )`,height:"100vh"}},{default:p(()=>[i(O,{class:"container-left"},{default:p(()=>[e("div",EA,[LA,e("div",zA,[e("div",{ref_key:"scrollContainer",ref:T,class:"taskmessage-div overflow-y",onScroll:U[0]||(U[0]=(...j)=>I.handleScroll&&I.handleScroll(...j))},[e("div",FA,[k.value.length<=0?(a(),c("div",UA,[PA,RA,e("div",NA,[qA,e("div",KA,[(a(!0),c(N,null,le(L.value,(j,ee)=>(a(),c("div",{class:"question-item cursor",key:ee,onClick:K=>D(j)},[e("div",QA,S(oe(He)(j)),1),e("div",OA,[e("div",ZA,S(j),1)])],8,HA))),128))])])])):(a(),c("div",$A,[(a(!0),c(N,null,le(k.value,(j,ee)=>(a(),c(N,{key:ee},[j.role==="user"?(a(),c("div",YA,[jA,e("div",JA,[e("div",{class:"font14",innerHTML:P(j.content)},null,8,GA)])])):j.role==="assistant"?(a(),c("div",WA,[e("div",XA,[A.value&&ee===k.value.length-1?(a(),c("img",e6)):(a(),c("img",t6))]),e("div",o6,[e("div",{class:"font14",innerHTML:P(j.content)},null,8,s6),y.value&&ee==k.value.length-1?(a(),c("div",n6,[Q(S(w.value)+" ",1),C.value?(a(),c("img",a6)):z("",!0)])):z("",!0)])])):z("",!0)],64))),128))]))],512)],544),e("div",l6,[e("div",{class:W(h.value?"":"taskmessage-ipt-no")},[i(J,{modelValue:g.value,"onUpdate:modelValue":U[1]||(U[1]=j=>g.value=j),autosize:{minRows:3,maxRows:6},type:"textarea",placeholder:"输入问题",resize:"none",disabled:!x.value,maxlength:"20000",onKeydown:M},null,8,["modelValue","disabled"]),e("button",{class:W(h.value?"":"send-btn-no"),disabled:!h.value,onClick:V},r6,10,i6)],2)])])])]),_:1}),m.value?(a(),X(Y,{key:0,class:W(["container-right",{open:d.value}])},{default:p(()=>[e("div",{class:"gongzuotai-title",onClick:U[2]||(U[2]=j=>d.value=!d.value)},u6),e("div",{class:W(["container-right-bottom",{open:d.value}])},[e("div",m6,[p6,v6,e("div",{ref_key:"chartRef",ref:o,style:{width:"100%",height:"100%"}},null,512)])],2)]),_:1},8,["class"])):z("",!0)]),_:1})}}},f6=ue(h6,[["__scopeId","data-v-e55698b8"]]),{serverConfig:_6}=lt(),Zn=new zt({baseURL:`${_6.VITE_API_BASE_URL}/ai_os`,headers:{"Content-Type":"multipart/form-data"}}),g6=async()=>Zn.get("/ws/desktops/realtime",{timeout:1e4}),A6=async()=>Zn.get("/ws/desktops/delay",{}),y6="data:image/png;base64,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",b6="/nq/static/pdf/manual-Ct3tsx3S.pdf",w6="data:image/png;base64,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",C6="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAVFBMVEUAAAAk6+sh4OAo7+4l7u4j9PQo7+4o7+4p7u4o7+8o7+4o8e8n7+8o7+8p7u4p8O4o7u4o8O4p7+0p7+8o7u4n8PAp7u4n8PAp7u4k8PAq9PQp7+4SCAwvAAAAG3RSTlMAFQbZHQ7139TCj2g96ee5pJqCcFlVSkIsIxhRookwAAAAXElEQVQY022NVw6AIBAFAQWk2Lvv/vdU4hpKnL+ZZN8yQkiWIYzmWbCAEYkveBijHwoB97ms8LJTGEC0NLPNU1Dr1rjSAcj+6jKE2asMdRqav3CmoQeUTAP3ni5u0F0HBhVRUJsAAAAASUVORK5CYII=",k6="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAV1BMVEUAAAAA/0wA/0AA/08A/0sA/00A/08A/1AA/1AA/1AA/1AA/1AA/08A/1AA/1AA/08A/1AA/08A/08A/04A/1AA/08A/1AA/08A/1EA/1EA/1AA/0cA/1DguF7GAAAAHHRSTlMAFgbaHQ7o4NTCj1dA+vb0uaSagnBrZko8LCMSak90bAAAAF9JREFUGNNtz1kOgCAMBFBFBVncd537n9MFCIU4f31JO2lmc5m9zGh6oK0o1ADOFKKd4g9yCiIFHsE2T3iiVu1ggAtnFqrCzo3xO0f3wRJu6nceaYsCJKPApLCtQfyvN9M2BzeHPbVsAAAAAElFTkSuQmCC",x6="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAMAAABF0y+mAAAAyVBMVEUAAAD0+P/2+f/2+P/////0+P/29v/1+P/1+P8AAACWmJ1PUFPd4Ofu8vgGBgbl6e+xtLk+P0EWFxcPDxAKCgvR1Nq2ub5oam1GR0k6Oz0tLi8iIiMfICHy9fzw9Pvs7/bn6/Hg4+nY2+HCxcuztrujpqudoKSUlpp+gINzdHhub3JZWl0BAQHr7vTh5evV2N7O0dfN0NaanKGQkpaMj5OIio6Cg4dvcXRgYWRcXWBRUlVJS003ODkxMTMoKSoSEhPBxMq5vMGrrbJ6yhxlAAAACHRSTlMA8sdsBs8bzwlcVwsAAAEsSURBVCjPfZNpc4IwEIYjoCYabuTyBLlBrfetbf//jyrMMLDF1ufb5pnM7G7eIIQ4lmnhBi2G5VBOp4v/pNvJ77UxBFoOsfhfWMTA8juCFYNgL72MpDzoCgEXZXdZuzn1QS3HhqIvVtQcrV8lfxkNpCeRqaFKTTmTbRoIhJDhx1bY/pbUtsR9RgpUz1ONCZCDUX8akxIhXA/NWSXnyok/k5qjYz1WpRRPB7zpQwI+Tqal3Myx1INI+MsvJd5rvvsgNXcaaX7VUCCEy1vl+mKqfIJRJO2wMEsXO5btglGKhhM+LZTQGwyvs3oJ5ajnyZEQVdefyfhl8dQ2qb5zd1oIFl8hyoJ8VVSvPoGPzV+KvcPHhjEZG9YSxqQRsAks2DfRbHN5qKGFroPefocfsycoRMWbyfYAAAAASUVORK5CYII=",S6="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAUCAMAAACOLiwjAAAAb1BMVEUAAAD///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////8v0wLRAAAAJHRSTlMABvm42Y/r5qiAL+/f0aF1X0M3KxjHw7OLh3tbT0c/MyQjHBBxC9s7AAAAgElEQVQY023LyRbCIBBE0aKBABnN4Dxr/f83GjBysvBu+tWiAUwHI2LsA7OhYCIX4O1IHcJeKCU68oTZVeixY60QaRYo2CKxFJA2tWpYo+IxtScDevuKrckm/i69nfDlSa2Wvq8aZ3LAz4Ys/4+xMgqZWnUvbsyjJbs8bs484/0AhuII/IsVieYAAAAASUVORK5CYII=",I6=window._aa_global_config.appItems,an=fn.cloneDeep(I6),B6={name:"AIOIndexView",components:{Search:xs,VueDraggable:Aa},setup(){const s=n(null);let o=null;const l=n(90),t=n(""),d=n({}),m=n(!1),u=n([]),r=n([]),v=50,k=2e3;d.value={animation:!1,xAxis:{type:"time",splitLine:{show:!1},axisLabel:{show:!1},axisLine:{lineStyle:{type:"solid",color:"#fff",width:"1"}}},yAxis:{allowDecimals:!1,type:"value",splitNumber:5,min:0,max:1e4,interval:1024,axisLabel:{color:"#000",formatter:function(R){return(R/1024).toFixed(0)+"m"}},splitLine:{show:!0,lineStyle:{color:["#fff"]}}},series:[{name:"upload",data:u.value,type:"line",smooth:!0,showSymbol:!1,symbol:"none",visible:!0,itemStyle:{color:"#29EFEE",width:"1"},areaStyle:{color:new Gs(0,0,0,1,[{offset:0,color:"rgba(41, 239, 238, 0.3)"},{offset:1,color:"rgba(41, 239, 238, 0)"}])}},{name:"download",data:r.value,type:"line",smooth:!0,showSymbol:!1,visible:!0,symbol:"none",itemStyle:{color:"#00FF50",width:"2"},areaStyle:{color:new Gs(0,0,0,1,[{offset:0,color:"rgba(0, 255, 80, 0.5)"},{offset:1,color:"rgba(0, 255, 80, 0.2)"}])}}],grid:{top:"15px",left:"40px",right:"5px",bottom:"5px"}};const h=n(null),g=n(null),x=(R,j)=>{const ee=new Date().getTime();h.value||(h.value=R),g.value||(g.value=j);const K=parseFloat(((R-h.value)/2e3).toFixed(2));u.value.shift(),u.value=[...u.value,[ee,K]],h.value=R;const H=parseFloat(((j-g.value)/2e3).toFixed(2));r.value.shift(),r.value=[...r.value,[ee,H]],g.value=j,o&&o.setOption({series:[{name:"upload",data:u.value},{name:"download",data:r.value}]})},y=n(null),w=n(null),T=n({date:"2000年01月01日",time:"00:00"}),A=n({used:0,power:0,temperature:0}),C=n({total:0,used:0}),L=n({total:0,used:0}),B=n({total:0,used:0}),P=n({download:0,upload:0}),b=n([]),M=n(0),D=R=>{let j=R.getHours(),ee=R.getMinutes(),K=j>=12?"下午":"上午";j=j%12,j=j||12,ee=ee<10?"0"+ee:ee;let H=K+" "+j+":"+ee,ne=R.getFullYear()+"年"+(R.getMonth()+1)+"月"+R.getDate()+"日",G=["星期日","星期一","星期二","星期三","星期四","星期五","星期六"][R.getDay()];return{time:H,date:ne+" "+G}},V=()=>{const R=new Date;T.value=D(R)},f=async()=>{const R=await g6();R.data.error=="0"&&(A.value=R.data.cpu,C.value=R.data.memory,L.value=R.data.gpu_memory,P.value=R.data.network,x(P.value.upload,P.value.download))},I=async()=>{const R=await A6();R.data.error=="0"&&(B.value=R.data.storage,b.value=R.data.models,B.value.total!==0&&(M.value=Number((B.value.used/B.value.total*100).toFixed(2))))},U=()=>{const R=[];let j=new Date().getTime();for(let ee=0;ee<v;ee++)j=j-k,R.push([j,0]);u.value=R,r.value=R};ge(()=>{o=No(s.value),o.setOption(d.value),U(),f(),I(),y.value&&clearInterval(y.value),y.value=setInterval(f,2e3),V(),w.value&&clearInterval(w.value),w.value=setInterval(V,1e3)}),Yt(()=>{y.value&&clearInterval(y.value),o&&(o.dispose(),o=null)});const J=(R,j)=>{for(var ee=[],K=Ti(R).split(""),H=0;H<j.length;H++){for(var ne=j[H].name,re=!0,G=0;G<K.length;G++)if(ne.indexOf(K[G])<0){re=!1;break}re&&ee.push(j[H])}return ee},O=n(an);return{chart4:s,storagePercentage:M,dashboardWidth:l,searchKeywords:t,search:()=>{const R=J(t.value,O.value);t.value?O.value=R:O.value=an},appItems:O,datetime:T,cpu:A,memory:C,gpu_memory:L,storage:B,models:b,network:P,showAiosModal:m,data1:u,data2:r,ManualPDF:b6}}},Ye=s=>(be("data-v-d05e8853"),s=s(),we(),s),T6={class:"common-layout"},M6=Ye(()=>e("div",{style:{width:"100vw",height:"5.5vh",background:"rgba(0, 0, 0, 0)"}},null,-1)),D6={class:"bg el-slide-item el-slide-item1"},V6={class:"el-slide-item1-time font-zhongcu"},E6={class:"el-slide-item1-date font-zhongcu"},L6={class:"bg el-slide-item el-slide-item2"},z6=Ye(()=>e("div",{class:"el-slide-item-title font-zhongcu"},"系统状态",-1)),F6={class:"el-slide-item2-bottom"},U6={class:"el-slide-item2-bottom-item"},P6=Ye(()=>e("div",{class:"el-slide-item2-bottom-item-title"}," 中央处理器 ",-1)),R6={class:"el-slide-item2-bottom-item-desc"},N6={class:"el-slide-item2-bottom-item"},q6=Ye(()=>e("div",{class:"el-slide-item2-bottom-item-title"}," 内存 ",-1)),K6={class:"el-slide-item2-bottom-item-desc"},H6={class:"el-slide-item2-bottom-item"},Q6=Ye(()=>e("div",{class:"el-slide-item2-bottom-item-title"}," 显存 ",-1)),O6={class:"el-slide-item2-bottom-item-desc"},Z6={class:"bg el-slide-item el-slide-item3"},$6=Ye(()=>e("div",{class:"el-slide-item-title font-zhongcu"},"存储空间",-1)),Y6={class:"el-slide-item3-bottom"},j6=Ye(()=>e("div",{class:"el-slide-item3-item"},[e("img",{src:w6,alt:""}),e("div",{class:"el-slide-item3-item-status"},"健康")],-1)),J6={class:"el-slide-item3-desc"},G6={class:"bg el-slide-item el-slide-item4"},W6={class:"flex justify-between"},X6=Ye(()=>e("div",{class:"el-slide-item-title font-zhongcu"}," 网络状态 ",-1)),e8={class:"el-slide-item-network flex-1 flex-nowrap"},t8={key:0},o8=Ye(()=>e("img",{src:C6,alt:""},null,-1)),s8={style:{"margin-right":"10px"}},n8={key:1},a8=Ye(()=>e("img",{src:k6,alt:""},null,-1)),l8={class:"el-slide-item4-bottom"},i8={ref:"chart4",style:{width:"100%",height:"200px"}},c8={class:"bg el-main-item el-main-item-search"},r8={key:0,class:"el-main-center"},d8=Ye(()=>e("div",{class:"bg el-main-item el-main-model1"},[e("div",{class:"el-main-model1-title"},[e("img",{src:x6,alt:""}),e("div",{class:"overflow-one font-zhongcu"},"匠心3.0")]),e("div",{class:"el-main-model1-desc font-zhongcu"}," 匠心 3.0是AiALIGN最新推出的最先进型号，有8B、70B和405B的参数尺寸。 "),e("div",{class:"el-main-model1-bottom"}," 模型：参数 8.03B ")],-1)),u8=Ye(()=>e("div",{class:"el-main-model2-nav"},[e("div",{class:"font-zhongcu"},"本地模型"),e("div",{class:"font-zhongcu"},"参数量"),e("div",{class:"font-zhongcu"},"状态")],-1)),m8={class:"font-zhongcu"},p8={class:"font-zhongcu"},v8={key:0,class:"el-main-model2-status-success"},h8={key:1,class:"el-main-model2-status-fail"},f8={class:"font-zhongcu"},_8={class:"font-zhongcu"},g8={key:0,class:"el-main-model2-status-success"},A8={key:1,class:"el-main-model2-status-fail"},y8={class:"el-main-bottom"},b8=Ye(()=>e("div",{class:"font-zhongcu el-main-bottom-title"},"APP",-1)),w8=["href"],C8={class:"bg el-main-app-item"},k8=["src"],x8={class:"font-zhongcu"},S8={class:"aios-modal-container overflow-y"},I8=Ye(()=>e("div",{class:"el-main-model2-nav"},[e("div",{class:"font-zhongcu"},"本地模型"),e("div",{class:"font-zhongcu"},"参数量"),e("div",{class:"font-zhongcu"},"状态")],-1)),B8={class:"font-zhongcu"},T8={class:"font-zhongcu"},M8={key:0,class:"el-main-model2-status-success"},D8={key:1,class:"el-main-model2-status-fail"},V8={class:"help-div"},E8=["href"],L8=Ye(()=>e("div",{class:"yuan"},[e("img",{src:S6,alt:""})],-1));function z8(s,o,l,t,d,m){const u=_("el-progress"),r=_("el-aside"),v=_("Search"),k=_("el-icon"),h=_("el-input"),g=_("el-tooltip"),x=_("VueDraggable"),y=_("el-main"),w=_("el-container");return a(),c(N,null,[e("div",T6,[M6,i(w,null,{default:p(()=>[i(r,{class:"overflow-y"},{default:p(()=>[e("div",D6,[e("div",V6,S(t.datetime.time),1),e("div",E6,S(t.datetime.date),1)]),e("div",L6,[z6,e("div",F6,[e("div",U6,[i(u,{type:"dashboard",percentage:parseFloat(t.cpu.used.toFixed(2)),color:"#6BCCFE",width:t.dashboardWidth,style:{"font-weight":"bold","font-size":"20px",color:"#000"}},null,8,["percentage","width"]),P6,e("div",R6,S(t.cpu.temperature.toFixed(2))+"℃ ",1)]),e("div",N6,[i(u,{type:"dashboard",percentage:parseFloat(t.memory.used.toFixed(2)),color:"#6BCCFE",width:t.dashboardWidth,style:{"font-weight":"bold","font-size":"20px",color:"#000"}},null,8,["percentage","width"]),q6,e("div",K6,S(t.memory.total.toFixed(2))+"G ",1)]),e("div",H6,[i(u,{type:"dashboard",percentage:parseFloat(t.gpu_memory.used.toFixed(2)),color:"#6BCCFE",width:t.dashboardWidth,style:{"font-weight":"bold","font-size":"20px",color:"#000"}},null,8,["percentage","width"]),Q6,e("div",O6,S(t.gpu_memory.total.toFixed(2))+"G ",1)])])]),e("div",Z6,[$6,e("div",Y6,[j6,i(u,{"stroke-width":10,percentage:t.storagePercentage,"show-text":!1,color:"#129BFF",style:{"margin-top":"20px"},class:"custom-progress"},null,8,["percentage"]),e("div",J6,S(t.storage.used.toFixed(2))+"GB/"+S(t.storage.total.toFixed(2))+"GB ",1)])]),e("div",G6,[e("div",W6,[X6,e("div",e8,[t.data1&&t.data1.length>0&&t.data1[t.data1.length-1].length>1?(a(),c("div",t8,[o8,e("span",s8,S(t.data1[t.data1.length-1][1])+"KB/s",1)])):z("",!0),t.data2&&t.data2.length>0&&t.data2[t.data2.length-1].length>1?(a(),c("div",n8,[a8,e("span",null,S(t.data2[t.data2.length-1][1])+"KB/s",1)])):z("",!0)])]),e("div",l8,[e("div",i8,null,512)])])]),_:1}),i(y,{class:"overflow-y"},{default:p(()=>[e("div",c8,[i(k,{color:"#666666",size:"20"},{default:p(()=>[i(v)]),_:1}),i(h,{modelValue:t.searchKeywords,"onUpdate:modelValue":o[0]||(o[0]=T=>t.searchKeywords=T),placeholder:"搜索",onKeyup:ya(t.search,["enter"])},null,8,["modelValue","onKeyup"])]),t.models.length>0?(a(),c("div",r8,[d8,e("div",{class:"bg el-main-item el-main-model2",onClick:o[1]||(o[1]=T=>t.showAiosModal=!0)},[u8,t.models.length<=2?(a(!0),c(N,{key:0},le(t.models,(T,A)=>(a(),c("div",{class:"el-main-model2-item",key:A},[e("div",m8,S(T.name),1),e("div",p8,S(T.parameters),1),e("div",null,[T.status=="on"?(a(),c("div",v8)):(a(),c("div",h8))])]))),128)):(a(!0),c(N,{key:1},le(t.models.slice(0,2),(T,A)=>(a(),c("div",{class:"el-main-model2-item",key:A},[e("div",f8,S(T.name),1),e("div",_8,S(T.parameters),1),e("div",null,[T.status=="on"?(a(),c("div",g8)):(a(),c("div",A8))])]))),128))])])):z("",!0),e("div",y8,[b8,i(x,{ref:"el",modelValue:t.appItems,"onUpdate:modelValue":o[2]||(o[2]=T=>t.appItems=T),animation:150,ghostClass:"ghost",class:"el-main-app-list"},{default:p(()=>[(a(!0),c(N,null,le(t.appItems,(T,A)=>(a(),c("a",{href:T.src,key:A,target:"_blank"},[e("div",C8,[i(g,{content:T.name,placement:"top",effect:"light"},{default:p(()=>[e("img",{src:T.img,alt:""},null,8,k8)]),_:2},1032,["content"]),e("div",x8,S(T.name),1)])],8,w8))),128))]),_:1},8,["modelValue"])])]),_:1})]),_:1})]),t.showAiosModal?(a(),c("div",{key:0,class:"aios-modal",onClick:o[3]||(o[3]=T=>t.showAiosModal=!1)},[e("div",S8,[I8,(a(!0),c(N,null,le(t.models,(T,A)=>(a(),c("div",{class:"el-main-model2-item",key:A},[e("div",B8,S(T.name),1),e("div",T8,S(T.parameters),1),e("div",null,[T.status=="on"?(a(),c("div",M8)):(a(),c("div",D8))])]))),128))])])):z("",!0),e("div",V8,[e("a",{href:t.ManualPDF,target:"blank"},[i(g,{class:"box-item",effect:"dark",content:"帮助",placement:"left"},{default:p(()=>[L8]),_:1})],8,E8)])],64)}const F8=ue(B6,[["render",z8],["__scopeId","data-v-d05e8853"]]),U8={name:"FileTemplateListView",computed:{ListType(){return $t},CirclePlus(){return vt}},components:{BreadCrumbComponent:Ee,ThreeElCardList:Qn,MainHeader:Xt,Main:Wt,BaseNavComponennt:Me},setup(){const s=Ce(),o=n("template"),l=n([]),t=n(!1),d=n(null),m=n(1),u=n(18),r=n(0),v=n([{path:"/filetemplate-list",name:"应用管理"},{path:"",name:"文件模板"}]),k=async(y,w)=>{d.value=Re.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});try{const T=await Ml(y,w);d.value.close(),l.value=T.data.data,r.value=T.data.total_num[0],T.data.data.length<=0&&(t.value=!0)}finally{d.value.close()}},h=y=>{m.value=y,k(m.value,u.value)};return ge(()=>{k(m.value,u.value)}),{selectedBar:o,fileTemplateList:l,showDefault:t,totalItems:r,handlePageChange:h,pageSize:u,currentPage:m,toDetail:y=>{s.push({name:"FileTemplateAdd",params:{ftid:y.id}})},handleDelConfirm:async y=>{const w=await zl(y);w.data.error=="0"?(E.success("删除成功"),await k(m.value,u.value)):w.data.message?E.error(`删除失败，${w.data.message}`):E.error("删除失败")},breadcrumbs:v}}},Ks=s=>(be("data-v-002014ce"),s=s(),we(),s),P8=Ks(()=>e("div",{class:"cc-page-title-text font-zhongcu"}," 我的模板 ",-1)),R8={key:0,style:{width:"100%",height:"95%",display:"flex","align-items":"center","justify-content":"center","flex-direction":"column"}},N8=Ks(()=>e("img",{src:Ps,alt:"",style:{width:"50px",height:"50px","margin-bottom":"10px"}},null,-1)),q8=Ks(()=>e("div",null,"还没有文件模板，快去创建一个吧",-1)),K8=[N8,q8],H8={class:"mt-[30px]"},Q8={class:"breadcrumb-container"};function O8(s,o,l,t,d,m){const u=_("BaseNavComponennt"),r=_("el-button"),v=_("router-link"),k=_("MainHeader"),h=_("ThreeElCardList"),g=_("el-pagination"),x=_("BreadCrumbComponent"),y=_("Main"),w=_("el-row");return a(),X(w,{class:"h-screen w-full"},{default:p(()=>[i(u,{selectedBar:t.selectedBar},null,8,["selectedBar"]),i(y,null,{head:p(()=>[i(k,null,{left:p(()=>[P8]),right:p(()=>[i(v,{to:{name:"FileTemplateAdd"}},{default:p(()=>[i(r,{icon:m.CirclePlus,class:"rounded-[10px] cc-header-right-button",color:"#F1F6FF",onClick:o[0]||(o[0]=()=>{})},{default:p(()=>[Q(" 创建 ")]),_:1},8,["icon"])]),_:1})]),_:1})]),main:p(()=>[t.showDefault?(a(),c("div",R8,K8)):z("",!0),e("div",H8,[i(h,{expand:{delModal:{title:"永久删除模板",description:"该模板将永久删除，不可恢复及撤销。确认要删除吗？",confirm:t.handleDelConfirm}},"detail-fun":t.toDetail,type:m.ListType.Template,items:t.fileTemplateList},null,8,["expand","detail-fun","type","items"])]),i(g,{background:"",layout:"prev, pager, next",total:t.totalItems,"page-size":t.pageSize,"current-page":t.currentPage,onCurrentChange:t.handlePageChange,style:{"justify-content":"flex-end",padding:"20px"},"hide-on-single-page":!0},null,8,["total","page-size","current-page","onCurrentChange"]),e("div",Q8,[i(x,{class:"breadcrumb",style:{"padding-left":"0"},breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})}const Z8=ue(U8,[["render",O8],["__scopeId","data-v-002014ce"]]),$8={name:"FileTemplateAddView",components:{BreadCrumbComponent:Ee,HeaderLeft:Gt,MainHeader:Xt,Main:Wt,BaseNavComponent:Me},setup(){const s=Ce(),o=De(),l=n("template"),t=n(null),d=n(""),m=n(""),u=n(null),r=n(!1),v=n("docx"),k=[{value:"docx",label:"Word"},{value:"csv",label:"CSV"},{value:"pptx",label:"PowerPoint"},{value:"xlsx",label:"Excel"},{value:"txt",label:"txt"},{value:"pdf",label:"Pdf"},{value:"md",label:"Markdown"}],h=n([{value:"string",label:"string"},{value:"number",label:"number"},{value:"float",label:"float"},{value:"integer",label:"integer"}]),g=n([{key:"",description:"",type:"string"}]),x=n([{path:"/filetemplate-list",name:"应用管理"},{path:"",name:"文件模板"}]),y=(D,V)=>{D=="params"&&g.value.splice(V,1)},w=D=>{D=="params"&&g.value.push({key:"",description:"",type:"string"})},T=D=>{if(Object.keys(D.properties).length===0&&D.properties.constructor===Object)return{};{let f=[],I=Object.keys(D.properties);if(I.length>0)for(var V=0;V<I.length;V++){let U=I[V];f.push({key:U,const:D.properties[U].const?D.properties[U].const:"",type:D.properties[`${U}`].type,description:D.properties[`${U}`].description})}return f}},A=D=>Object.keys(D).length===0&&D.constructor===Object,C=async()=>{const D=await Vl(t.value);if(D.data.error=="1"||D.data.error=="404")s.push({name:"FileTemplateList"});else{const V=D.data;if(d.value=V.name,m.value=V.description,v.value=V.type,V.fileurl&&b.value.push({name:V.name+"."+En(V.fileurl),url:V.fileurl}),V.params)try{let f=T(JSON.parse(V.params));A(f)||(g.value=f)}catch{}}};ge(()=>{o.params.ftid&&(t.value=o.params.ftid,C())});const L=D=>{const V=D.target.files[0];V&&(u.value=V)},B=n(null),P=D=>{B.value.clearFiles();const V=D[0];V.uid=ba(),B.value.handleStart(V)},b=n([]);return{selectedBar:l,fileTemplateId:t,fileTemplateName:d,fileTemplateDescription:m,fileTemplateFile:u,paramsType:h,paramsTableData:g,submitData:async()=>{if(r.value)return;if(r.value=!0,d.value)if(m.value){if(b.value.length<=0){E("请填写上传文件模板"),r.value=!1;return}}else{E("请填写模板描述"),r.value=!1;return}else{E("请填写模板名称"),r.value=!1;return}let D={properties:g.value.reduce((V,f)=>(f.key&&f.type&&f.description!==void 0&&(V[f.key]={type:f.type,description:f.description}),V),{})};try{let V;o.params.ftid?V=await Ll(t.value,d.value,m.value,JSON.stringify(D),b.value[0].raw):V=await El(d.value,m.value,v.value,JSON.stringify(D),b.value[0].raw),V.data.error=="0"?(E.success("提交成功"),setTimeout(()=>{s.push({name:"FileTemplateList"})},500)):V.data.message?E.error(V.data.message):s.push({name:"FileTemplateList"})}catch{E.error("提交失败")}finally{r.value=!1}},deleteHeaderParamsRow:y,onAddTableItem:w,selectedFileTemplateType:v,templateTypeOptions:k,handleFileChange:L,upload:B,handleExceed:P,fileList:b,breadcrumbs:x}}},eo=s=>(be("data-v-eecccbb5"),s=s(),we(),s),Y8={class:"mt-[30px] mb-[13vh]"},j8=eo(()=>e("div",{class:"cc-form-title"}," 基本信息 ",-1)),J8=eo(()=>e("div",{class:"cc-label font-zhongcu mt-[20px]"}," 模版名称 ",-1)),G8=eo(()=>e("div",{class:"cc-label font-zhongcu mt-[15px]"}," 模版描述 ",-1)),W8={class:"el-main-item"},X8=eo(()=>e("div",{class:"cc-label mt-[15px]"},"模板类型",-1)),e4={class:"el-main-item"},t4=eo(()=>e("div",{class:"el-main-item-title"},"模板文件",-1)),o4={class:"el-main-item"},s4=eo(()=>e("div",{class:"el-main-item-title"},"变量参数",-1)),n4={class:"breadcrumb-container"};function a4(s,o,l,t,d,m){const u=_("BaseNavComponent"),r=_("HeaderLeft"),v=_("MainHeader"),k=_("el-option"),h=_("el-select"),g=_("el-button"),x=_("el-upload"),y=_("el-input"),w=_("el-table-column"),T=_("el-table"),A=_("BreadCrumbComponent"),C=_("Main"),L=_("el-row");return a(),X(L,{class:"h-screen w-full"},{default:p(()=>[i(u,{"selected-bar":t.selectedBar},null,8,["selected-bar"]),i(C,null,{head:p(()=>[i(v,null,{left:p(()=>[i(r,{to:{name:"FileTemplateList"},text:t.fileTemplateId?"编辑文件模板":"新建文件模板",submit:t.submitData},null,8,["text","submit"])]),_:1})]),main:p(()=>[e("div",Y8,[j8,J8,me(e("input",{"onUpdate:modelValue":o[0]||(o[0]=B=>t.fileTemplateName=B),placeholder:"输入模板名称",class:"cc-input mt-[10px]",maxlength:"50"},null,512),[[Ae,t.fileTemplateName]]),G8,me(e("textarea",{"onUpdate:modelValue":o[1]||(o[1]=B=>t.fileTemplateDescription=B),placeholder:"请输入模版描述",rows:4,class:"cc-textarea mt-[10px]",maxlength:"100"},null,512),[[Ae,t.fileTemplateDescription]]),e("div",W8,[X8,i(h,{modelValue:t.selectedFileTemplateType,"onUpdate:modelValue":o[2]||(o[2]=B=>t.selectedFileTemplateType=B),placeholder:"选择类型",size:"large",style:{width:"650px"},disabled:!!t.fileTemplateId,class:"mt-[10px]"},{default:p(()=>[(a(!0),c(N,null,le(t.templateTypeOptions,B=>(a(),X(k,{key:B.value,label:B.label,value:B.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","disabled"])]),e("div",e4,[t4,i(x,{"file-list":t.fileList,"onUpdate:fileList":o[3]||(o[3]=B=>t.fileList=B),ref:"upload",class:"upload-demo",limit:1,"on-exceed":t.handleExceed,"auto-upload":!1,style:{width:"650px"}},{default:p(()=>[i(g,{type:"primary"},{default:p(()=>[Q("上传文件")]),_:1})]),_:1},8,["file-list","on-exceed"])]),e("div",o4,[s4,i(T,{data:t.paramsTableData,style:{width:"600px"},border:"",class:"el-main-item-table"},{default:p(()=>[i(w,{prop:"",label:"变量名",width:"200px"},{default:p(B=>[i(y,{modelValue:B.row.key,"onUpdate:modelValue":P=>B.row.key=P},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(w,{prop:"",label:"变量描述"},{default:p(B=>[i(y,{modelValue:B.row.description,"onUpdate:modelValue":P=>B.row.description=P},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),i(w,{prop:"",label:"变量类型"},{default:p(B=>[i(h,{modelValue:B.row.type,"onUpdate:modelValue":P=>B.row.type=P,placeholder:"选择类型",size:"large"},{default:p(()=>[(a(!0),c(N,null,le(t.paramsType,P=>(a(),X(k,{key:P.value,label:P.label,value:P.value},null,8,["label","value"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:1}),i(w,{prop:"",label:"操作",fixed:"right",width:"100px"},{default:p(B=>[i(g,{link:"",type:"primary",size:"small",onClick:_e(P=>t.deleteHeaderParamsRow("params",B.$index),["prevent"])},{default:p(()=>[Q(" 删除 ")]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),i(g,{type:"primary",link:"",onClick:o[4]||(o[4]=B=>t.onAddTableItem("params")),class:"add-item-btn"},{default:p(()=>[Q(" + 添加参数")]),_:1})]),e("button",{type:"submit",class:"cc-button font-zhongcu common-confirm-btn",onClick:o[5]||(o[5]=(...B)=>t.submitData&&t.submitData(...B))}," 保存 ")]),e("div",n4,[i(A,{class:"breadcrumb",style:{"padding-left":"0"},breadcrumbs:t.breadcrumbs},null,8,["breadcrumbs"])])]),_:1})]),_:1})}const l4=ue($8,[["render",a4],["__scopeId","data-v-eecccbb5"]]),ct=s=>(be("data-v-46b546fd"),s=s(),we(),s),i4={class:"container-center"},c4=ct(()=>e("div",{class:"container-top-mengban"},null,-1)),r4={class:"conatiner-message-div flex w-full h-full flex-col justify-between"},d4={ref:"innerRef",style:{position:"relative",height:"100%"}},u4={key:0,class:"opening-statement-div"},m4=ct(()=>e("div",{class:"overflow-one"},"Hello，我是FMEA",-1)),p4=ct(()=>e("div",{class:"overflow-one"},"让我们一起度过美好的一天！",-1)),v4={class:"question-example-div"},h4=ct(()=>e("div",{class:"font-zhongcu font16",style:{color:"#666"}}," 你可以尝试下面的示例… ",-1)),f4={class:"question-list"},_4=["onClick"],g4={class:"question-item-left font18 font-zhongcu"},A4={class:"question-item-right"},y4={class:"font14 overflow-five question-item-right-title"},b4={key:1},w4={key:0,class:W(["chat-item","taskmessage-user"])},C4=ct(()=>e("div",{class:"siteuser-headimg"},"用户",-1)),k4={class:"chat-item-content taskmessage-user-content"},x4=["innerHTML"],S4={key:1,class:W(["chat-item","taskmessage-ai"])},I4=ct(()=>e("div",{class:"chat-item-ai-headimg-div"},[e("img",{src:y6,alt:"",class:"chat-item-ai-headimg"})],-1)),B4={class:"chat-item-content taskmessage-ai-content"},T4=["innerHTML"],M4={key:0,class:"font14",style:{}},D4={key:0,src:Jt,alt:"",style:{width:"18px",height:"18px"}},V4={class:"taskmessage-add"},E4=["disabled"],L4=ct(()=>e("img",{src:ho,alt:""},null,-1)),z4=[L4],F4=ct(()=>e("div",{class:"conatiner-tips-div font14"}," 内容由AI生成，无法确保真实准确，仅供参考，请阅读并遵守《AiALIGN用户协议》 ",-1)),U4=ct(()=>e("img",{src:zs,alt:"",class:"gongzuotai-img"},null,-1)),P4=[U4],R4={class:"canvans-box"},N4=ct(()=>e("div",{class:"font-zhongcu font20 canvans-title"},"工作台",-1)),q4=ct(()=>e("div",{class:"canvans-bg"},null,-1)),ln="开始参考资料分析",K4={__name:"index",setup(s){const o=n(null),l=n([]),t=n(null),d=n(!1),m=n(!0),u=async()=>{try{const f=await mo.get("http://arai.aialign.com.cn/fmea/api/v1/get-fmea-graph");f.data.length<=0?m.value=!1:(d.value=!0,l.value=f.data,await r())}catch(f){console.error(f)}},r=()=>{const f=new Set,I=[];l.value.forEach(O=>{f.add(O.m),f.add(O.n),I.push({source:O.m,target:O.n,value:Math.random()*10+1})});const J={series:[{type:"graph",data:Array.from(f).map(O=>({name:O,draggable:!0,symbolSize:v(20,40)})),links:I,zoom:.15,roam:!0,edgeSymbol:["circle"],edgeSymbolSize:[4,10],lineStyle:{curveness:0},draggable:!0,layout:"force",force:{repulsion:1e4,edgeLength:100,layoutAnimation:!0},emphasis:{scale:1.5,focus:"adjacency",label:{position:"right",show:!0}}}]};t.value&&t.value.dispose(),t.value=No(o.value),t.value.setOption(J),t.value.on("mouseover",function(O){O.componentType==="series"&&(t.value.dispatchAction({type:"scaleNode",seriesIndex:O.seriesIndex,dataIndex:O.dataIndex,scale:1.2}),t.value.dispatchAction({type:"scaleNode",seriesIndex:O.seriesIndex,dataIndex:O.dataIndex,scale:.5}))}),t.value.on("mouseover",function(O){O.componentType==="series"&&(t.value.dispatchAction({type:"scaleNodeToOrigin",seriesIndex:O.seriesIndex}),t.value.dispatchAction({type:"scaleNode",seriesIndex:O.seriesIndex,dataIndex:O.dataIndex,scale:5}),t.value.dispatchAction({type:"scaleNode",seriesIndex:O.seriesIndex,scale:.5}))}),t.value.on("mouseout",function(O){t.value.dispatchAction({type:"scaleNodeToOrigin",seriesIndex:O.seriesIndex})}),t.value.on("dragstart",function(O){const Y=O.data;t.value.dispatchAction({type:"force",data:Y,fixed:!0})}),t.value.on("dragend",function(O){const Y=O.data,R=[O.event.offsetX,O.event.offsetY];t.value.dispatchAction({type:"force",data:Y,fixed:!1}),t.value.dispatchAction({type:"update",seriesIndex:0,data:Y,dataIndex:O.dataIndex}),t.value.setOption({series:[{data:t.value.getOption().series[0].data.map((j,ee)=>ee===O.dataIndex?{...j,x:R[0],y:R[1],fixed:!0}:j)}]})})},v=(f,I)=>Math.floor(Math.random()*(I-f+1))+f,k=n([]),h=n(!1),g=n(""),x=n(!0),y=n(!1),w=n(""),T=n(null),A=n(!1),C=n(["RPZ大于30的FailureEffect有几个？","失效效应（FailureEffect）Components misaligned的失效原因(FailureCause）是什么？","Soldering temperature too low最可能造成什么失效效应？"]);Pe(g,f=>{f!==""?h.value=!0:h.value=!1});const L=()=>{pt(()=>{const f=T.value;f&&(f.scrollTop=f.scrollHeight)})};Pe(k,async()=>{for(let f=k.value.length-1;f>=0;f--){const I=k.value[f].attachments_data;if(I&&I.length>0){curPreview.value=I[I.length-1],I[I.length-1].file_url.endsWith(".docx")&&(showPreview.value=!0);break}}await pt(),document.querySelectorAll("pre code").forEach(f=>{f.dataset.highlighted||(Ke.highlightElement(f),f.dataset.highlighted="yes")})}),Xe.setOptions({highlight:function(f,I){return Ke.getLanguage(I)?Ke.highlight(f,{language:I}).value:Ke.highlightAuto(f).value}});const B=f=>Xe(f),P=()=>{let f=0;w.value="";const I=setInterval(()=>{f<ln.length?(w.value+=ln[f],f++):(clearInterval(I),A.value=!0)},100)},b=f=>{if(f.key==="Enter"){if(g.value.trim()===""){f.preventDefault();return}f.shiftKey||f.ctrlKey?(g.value+=`
`,f.preventDefault()):D()}};Pe(g,f=>{f!==""?h.value=!0:h.value=!1});const M=async f=>{V(f)},D=async()=>{const f=g.value;g.value="",V(f)},V=async f=>{const I={role:"user",content:f,taskmessage_id:""};k.value.push(I);let U={role:"assistant",content:"",taskmessage_id:""};k.value.push(U),L(),y.value=!0,P(),x.value=!1;try{const J=await mo.get(`http://arai.aialign.com.cn/fmea/api/v1/question-answer?question=${f}`);y.value=!1,A.value=!1,x.value=!0,U.content=J.data.answer,k.value=[...k.value],L()}catch{y.value=!1,A.value=!1,x.value=!0,k.value.pop(),k.value.push({role:"assistant",content:"当前请求网络可能有问题，请重新发起对话"}),L()}};return ge(()=>{u()}),(f,I)=>{const U=_("el-input"),J=_("el-main"),O=_("el-aside"),Y=_("el-container");return a(),X(Y,{style:{background:`linear-gradient(
        180deg,
        #f5fbff 0%,
        rgba(245, 251, 255, 0.93) 45%,
        rgba(244, 250, 255, 0) 100%
      )`,height:"100vh"}},{default:p(()=>[i(J,{class:"container-left"},{default:p(()=>[e("div",i4,[c4,e("div",r4,[e("div",{ref_key:"scrollContainer",ref:T,class:"taskmessage-div overflow-y",onScroll:I[0]||(I[0]=(...R)=>f.handleScroll&&f.handleScroll(...R))},[e("div",d4,[k.value.length<=0?(a(),c("div",u4,[m4,p4,e("div",v4,[h4,e("div",f4,[(a(!0),c(N,null,le(C.value,(R,j)=>(a(),c("div",{class:"question-item cursor",key:j,onClick:ee=>M(R)},[e("div",g4,S(oe(He)(R)),1),e("div",A4,[e("div",y4,S(R),1)])],8,_4))),128))])])])):(a(),c("div",b4,[(a(!0),c(N,null,le(k.value,(R,j)=>(a(),c(N,{key:j},[R.role==="user"?(a(),c("div",w4,[C4,e("div",k4,[e("div",{class:"font14",innerHTML:B(R.content)},null,8,x4)])])):R.role==="assistant"?(a(),c("div",S4,[I4,e("div",B4,[e("div",{class:"font14",innerHTML:B(R.content)},null,8,T4),y.value&&j==k.value.length-1?(a(),c("div",M4,[Q(S(w.value)+" ",1),A.value?(a(),c("img",D4)):z("",!0)])):z("",!0)])])):z("",!0)],64))),128))]))],512)],544),e("div",V4,[e("div",{class:W(h.value?"":"taskmessage-ipt-no")},[i(U,{modelValue:g.value,"onUpdate:modelValue":I[1]||(I[1]=R=>g.value=R),autosize:{minRows:3,maxRows:6},type:"textarea",placeholder:"输入问题",resize:"none",disabled:!x.value,maxlength:"20000",onKeydown:b},null,8,["modelValue","disabled"]),e("button",{class:W(h.value?"":"send-btn-no"),disabled:!h.value,onClick:D},z4,10,E4)],2)])]),F4])]),_:1}),m.value?(a(),X(O,{key:0,class:W(["container-right",{open:d.value}])},{default:p(()=>[e("div",{class:"gongzuotai-title",onClick:I[2]||(I[2]=R=>d.value=!d.value)},P4),e("div",{class:W(["container-right-bottom",{open:d.value}])},[e("div",R4,[N4,q4,e("div",{ref_key:"chartRef",ref:o,style:{width:"100%",height:"100%"}},null,512)])],2)]),_:1},8,["class"])):z("",!0)]),_:1})}}},H4=ue(K4,[["__scopeId","data-v-46b546fd"]]),Q4="/nq/static/png/ip-logo100X100-lkCq2bJ0.png",O4="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAATLSURBVHgB7Vm9cttGEN49UL+kFESyZEpOgbwB06UL1bmT/QSknyBWlXQSq7iz1KUT06Wz1KUT1aWU3gCZsSyaNhXYQ8sUf269R4rkgQJAgKTo8Qy/GQ6Bw91iv7u93b0FwBRTTPFNA+EeYCYtKwYNi4AskOSgEA4a5Ly7uDiDMWMsBJTCQtYyiJQmwhQAmX59EaFAAMezSEfFYtGGETESgQfJH1jh5i4RpGEIKDKIRu598XUBhsRQBJLJpFUnPBxW8btKYH5GyNwwKxKZgJp1KeWru2aCDpvQmTIPKUUB4jO2Y9uOemJalhmrVlP8zJISt/mlTzxE20xkp1x6cwT3RWDl4cavQLDfJ4KVlAdycX6/o/AgmLyCQkKWbSjD8qw+eXtXpTc5CAkjbMeV9c1d/nvhehUCKz739P+L1/9UHacaVla1UnE+f6oU5pYSx0jie25KaY/TC4mlD/z83zCyQq3A6vrmE3aJr/pG7ly9vdyHMYBX9jmvxEu9TQjael8sFgaNHUhAbdiaxBO+tLqDiJ6V3xXzMEasJpNZknioNTmzgn4atLEFDEBdCmU6VreBZz6M8mxy+ZX1DWr/Ng8H9S8XWSbL1ppM5ekGjQsk0PLzQNleC+bHZTZeaMkmOOjcKzf9IJlMB40JJKCCVPcGwZbsq+GeIetze23P1tEBd4P6+xJQtu8KVER/OWMI/YPgOC1X7FoF5Xb9+vsSqBG6go0UkIcJQdZm9/VVEFJk/fr6EmD3tN25JoTjScx+B2oVVFTv6oL0i19fXwK6+TCZAkwYKiWBni4pv36eBNbWHrkGCG02JgVD5VM9mH77wJMAGehK1BLz8xMnUK/P2Pp9DPpzpja8CUjpImCHTNL8QSmLM9IoI269UU9C07C8+nnvAcJIL/OWQboCqY/XNydRSbgwQ56T6EkADbT1+2FefBuQdNOLRMI03f1QyvAEGtCw9XunXrcgIpQJsD/fGpZEbL7hciQNwwhPoO3ztUDSkGkYAqOQoCZXNHRZPhUN/0CmBxItqEXF0CSQtnu6+MehgECGp71rSI2yAaOSaOVhQN1URg9q/fAlwJlnXrs1P17XnsMI8CKBQOdeLromRcalC9eQ/OQGnshWH26caCmFs7w49+OoMUF5FzFbO0GQ5+VSMdv/vP8EqMyn/PZyy09e4HmA94Ge/5uVzzeBuXkYdFbCS3mF/hNgnw53EFiVuK5U7MXEUrojkG3x5ygVAz9Uq94VjFbZBuD3XgvmefYPgmQNPBM3kZ7pLlVVD/gAnoExY3XtUcZVcwp5AhxIQMUEIeRTvY2rB/lWKWRMUDNP6HIa7EVxJ8wZJFRhS5mSMh2+fKw1P16ML1vfLcXPK1yogiGgXKiYWfiDL/fcTyh3Vbr8M4yM0JU5ZfcL8QR7LUxrzakmHz0X4ssmE/kvLJGW4rOLv93Um3+zqaRdD1XBrFR8ASERubjbqtIhvbxb02y5vCPe6KcCjbPEfOys43KVwp84n2pySqKiuvc3BHSUqYapxo1EQKFdnBV7rMRYNrPy9cpZOJMor+tQRSdVtxntAwflos66SwaMAa0V4b3QNo8gMrffEIhOVZnG+dqfmPxgclEgZjRNksLkoo7JL7Eb/JtkaWaKKab4RvAFCRxN1CH9nQcAAAAASUVORK5CYII=",Z4="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAQcSURBVHgB7VlhUtNQEN59QURAJ1KLwTpjOAH1BuUGcgLrDfQEHU4gnoB6Av3pP7gB9QTEH0pAwegwDlb61t2UDkma1/aVhD/wzWSavL68frvfZt9uCnCLW9xsOFAiqrVa/cHCwtzp6WkEJUFBSVhaftLq/dN7XY37S49XXkNJQCgBnuf5THyHT/2LoejB/N3VIAgKV6IUBbpavYRL8gL3959uKSoUrkCO9wcoRYXCFeie4wsYJi8oRYXCFVhaXtmHfAMEhatQqAKVaq0JKfLY5uN9YkrhKhSqQNb7WtGqfCpOpYlphapQmAJ53o/CMJCjTBUKUyDP+33yzJgzU1kqFKKAyfuDqzJVmEoB1/N9R3frhOSzB55pglTqTHr/8p5hFRRCWwN8Ueh00KHo+9evHbAEjiMJCl0kvUZMkI86ELoA5I5Ysn1y9O1V3jdcH7X53pcwGh1ECJhYMDBucW6mYwo3zPmRFn+8Hk3SjDzvD5CjggVQDNhi52wmR1PltOv6Ljrnn/h0DmzBXuNj8+dh+Mk05YzL6nsLi+w0Jc7xwA7CqXH3/uL7s0R5nlJADFCz3X2j95kkUSzxF74KkDDoKd0xeXwc3GqtPuP0XE1Y50ufyaxRP0Trpns4ez1MhtNQCFU8r0katzPDkXNHrU/zkE2DfkGo9rKORKJXx9/DdnJsKI0eh2EbFWUfQpebkw+yMJSMy2p2PPl43LSQyKuQsgsFs4rWwylDZhzyS3GMlNIbP8JwN++ekfvAdRphIs/PxzpvEMbQHbuRXYcR05KPZ8EEKNOIq5CPZ8KEKMOIq5IXTFzMyYKycLxhXcL/S7gFU+KfVm/hCuQFVtVobIRWG1AQuLb6lbzWd+zIC6zL6RkH0/kZYBemBVKKrEPkgyWsDdDUS23zKkPCBlKKJK+pdw0G8I+uJa/PIfVMWEHqqNRAZu1JYN+RYcpLkamQ833frXi15kW3louLTi1KrG0s4kywNoD6lWP/9xByw0dI//7zd4+03ibU29IvmwzBZAiS8X2SEVYGVHkvyOwDn5PfP/KeNiqPV3aENKRfbvkmQ0in1nBdy4LRygCdDh9Jg4F8Dohr3dvhfqExYonYEJkr/x3IAIJKZyKlrMLIyoC4J04NoDuKOIfYLgF+HFqH58p/B5XlJ9sc96lel86hPAOkY0qPUMtEXHGJcXx4sP7z6NuGnGd28P7dQE0+PmSGn4EFbBXwR05gkgPiyfpdzk8OD1bjRgnHpV1qgAWs3gvxQ0iGZSJU+o10czAB4raVsGXKOtm+dxQmViA/O0gOp009P7s6KXmBzBVF2H1v8hSJzs4mfqVjpUCcIuOYF+L6nZ6f24qu+H6z/64Imrxm62Koc3J08BzKwiPPa7i8y0LBEEPcas16J77FLW46/gNbODnA4Q2jpQAAAABJRU5ErkJggg==",$4="data:image/png;base64,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",Y4="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFgAAABZCAYAAAC6yeORAAAACXBIWXMAACxLAAAsSwGlPZapAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAU0SURBVHgB7ZxfUhtHEMa7RwZEVaqyERIWlYcoJzA5gaUTWJwAfIKQE4BPQHjLG+IEwSdAnCD4BNk8pCwDptZVKfPH1nZ6VohUyOxqV55Bs1H/qlSidkZC+jQ70/N17wIIgiAIgiAIgiAI5QLzdAqYykK1SwDP+dGCeQYpIsLX0fmgl6v7pA7BympXVZ4cAFAAwj080EIiejVJ6EpWY63e3MGK+oX/rILwL3hkBojYXVr+Cq4//nWS0c9MUG+2lcJjECYSx9SJLgZ9U5tKexEqPAAhFzySd9LajAIHjW/Xcd4XswIgQluf8aY2o8BKxcbOQjoKaN10/ImxN+mIIWV6Joi46TXMJfgiNZpSaDxuFjjzf0B0efZ2C+aQ2upayE+FwlUFglOKj2DPSMLJCibzX3xDR1E0CMEjSj2C9UYoidUJ9vRDLeLvLPgOeERpBa7rsEjh7sPjLPhuWsg0C0orcMzb1LQ2hdQFTyjvFIEZ5hPi1+AJEkU4xnoUUauvbYOiH3kYtcAjgqDZwgU4QFTraZsFIjgloP28Xm8erI5gvarzO+55Ke4iHrMp087ytdlTWFeIB0H96TZYwqrApHALfGQB2kXMK4XKWqhnTeCgyaPEUwdOsdtVpD/bAYH+PmABeyP4uhqBrxAW/2zX11a+jzWBoyiMkKAPHqKIjgq9ALAXMWABq3Pw8NPSho8iX3A6JyZ6ycJNFI0XupP49uNPYAmrYZoexfzUqTebbefpfcJNDqvaebvr0Ivn1X4lZmNImSOJ4bByGp39eQoWceKmXQzMCUCb1Fa135CrrOOeaJA4bSE8IrKTc0yJvQhMPZV5HTgBTyivm3az1CP4r8h6u/ve4lb3S/FWYL291TVxae16QaXbxQ5vH/d5zL65e7yiT1cd8AjvUkajmozh3sg3WIZvVpdDuqWOKRV0F7VY8w1c4NUITkwZjH8diTtCb7/VAv6WNZp9xiuBWchto5/B3gAsVr0eqWn4NQdnZCn4g34HJUTiYMdYW+T0HKmWlrcgpmfZPelDzOFV0awBh2TtWqPptuIT4Y/4Fno2ayusCDzOGLAKLe2WZIPJaVNrPH12ef4ut6mSzM3o3tDHRdgMGo2N6PzciidhZYpQC7Bb2GxHte1T/cIY/T0QnuyBJezMwahewBQoBZvgIUm9r3cZDcGIFYGRaKr5yidT5gHhnbX5xVgReFjR2YLCPuuhT6bMP2AUk9oAS1iJIvSvzXNWRxF2OZJYn9Q/huFRdHZWME+W/ICORzyGsYp7tkavxlocfPehfgZnUP/ybPASSoYsco7xTeAwtYXoA5QQrwSOFfTMqXVeeCrocPpxh1cC63k8JtQZiXB8jNP/p/qYzYXnMfEuoxGdJ3UJ3493UmUVdoy3VxmVXdgxUwnso0nzOOhLE6jQK8wCIy806e/Tmt/bHBQTV2O+GHxIVrzQeYIjoL7puFHgz5+rp3kqEYV7tDnUNzUYBR7VG8T7IOSCU2CHaW2Z+Z2VxtoxFS2/nzN0PfH7d2/bae2ZGw1dUM1PhyCkcTi8ucq8qjRXge3K6lqXT4Mt7vxcbu+FESK94cdunjroYhXMMErPQ7U6tyL/XzZAgiAIgiD4TuEwrQj5Ky5nwXRVnkVxJvD9PRo8vwdmch/g26sfbF2b/BBnObmpKi5nwOgakKq1asqHuEt6TllxORvQ2V2qpPBEg+Bs6+9M4GkrLmcBz8PO7ibrTOApKy5nQUiKnF0i5jZMazZbaojbqCZXXM4CIuzbrqYUBEEQBEEQSsHfyiKm41WNM+kAAAAASUVORK5CYII=",vk=s=>{const o=document.createElement("a");o.href=s,o.target="_blank",o.rel="noopener noreferrer",document.body.appendChild(o),o.click(),document.body.removeChild(o)},cn=s=>{const o=document.createElement("a");o.href=s,o.target="_self",o.rel="noopener noreferrer",document.body.appendChild(o),o.click(),document.body.removeChild(o)},ko=s=>(be("data-v-84f332ea"),s=s(),we(),s),j4={class:"layout-nav"},J4={class:"layout-nav-item layout-nav-item1 flex justify-center items-center"},G4=["alt"],W4=ko(()=>e("img",{src:Vs,alt:"",style:{height:"1px",width:"50px","object-fit":"contain","object-position":"center",position:"absolute",top:"75px",left:"15px"}},null,-1)),X4={class:"layout-nav-item layout-nav-item2 !pb-0"},e7=ko(()=>e("img",{class:"!w-[24px] !h-[24px]",src:O4,alt:""},null,-1)),t7=[e7],o7=ko(()=>e("img",{class:"!w-[24px] !h-[24px]",src:Z4,alt:""},null,-1)),s7=[o7],n7=ko(()=>e("img",{class:"!w-[24px] !h-[24px]",src:$4,alt:""},null,-1)),a7=[n7],l7=ko(()=>e("img",{class:"!w-[24px] !h-[24px]",src:Y4,alt:""},null,-1)),i7=[l7],c7={__name:"AppSidebar",props:{isShowTopicHistory:Boolean,isSlide:Boolean,isShowFavorite:Boolean},emits:["userinfo","isShowTopicHistory","update:isSlide","isShowFavorite"],setup(s,{emit:o}){const{t:l}=Ss(),t=Bt();Ce(),De();const{showTip:d}=qo(),m=n(!1),u=n(!1);et(()=>t.userInfo);const r=()=>{u.value=!0},v=()=>{u.value=!1},k=()=>{m.value=!m.value,h("update:isSlide",m.value)},h=o;return(g,x)=>{const y=_("router-link"),w=_("el-tooltip");return a(),c(N,null,[e("div",j4,[e("div",J4,[i(y,{to:"/apps"},{default:p(()=>[e("img",{src:Q4,alt:oe(l)("layout.sidebar.logo"),class:"w-[34px] h-[34px]"},null,8,G4)]),_:1})]),W4,e("div",X4,[i(w,{class:"box-item",effect:"dark",content:oe(l)("layout.sidebar.historyChat"),placement:"right"},{default:p(()=>[e("button",{class:"layout-nav-item2-item",onClick:x[0]||(x[0]=T=>{oe(d)()})},t7)]),_:1},8,["content"]),i(w,{class:"box-item",effect:"dark",content:oe(l)("layout.sidebar.favorites"),placement:"right"},{default:p(()=>[e("button",{class:"layout-nav-item2-item",onClick:x[1]||(x[1]=T=>{oe(d)()})},s7)]),_:1},8,["content"]),i(w,{class:"box-item",effect:"dark",content:oe(l)("layout.sidebar.workspace"),placement:"right"},{default:p(()=>[e("button",{class:"layout-nav-item2-item",onClick:x[2]||(x[2]=T=>{oe(cn)("/apps")})},a7)]),_:1},8,["content"]),i(w,{class:"box-item",effect:"dark",content:oe(l)("layout.sidebar.systemManagement"),placement:"right"},{default:p(()=>[e("button",{class:"layout-nav-item2-item",onClick:x[3]||(x[3]=T=>{oe(cn)("/nq/setting/department")})},i7)]),_:1},8,["content"])])]),e("div",{class:"layout-nav-hot",onMouseenter:r,onMouseleave:v,onClick:k},[e("div",{class:W(["layout-nav-line",m.value?"right":u.value?"left":""])},null,2),e("div",{class:W(["layout-nav-line",m.value?"right":u.value?"left":""])},null,2)],32)],64)}}},r7=ue(c7,[["__scopeId","data-v-84f332ea"]]),xo=s=>(be("data-v-913d1843"),s=s(),we(),s),d7={key:0,class:"new-modal-overlay"},u7={class:"new-modal-container"},m7=xo(()=>e("div",{class:"new-modal-top"},[e("div",{class:"font18 font-zhongcu"},"修改密码")],-1)),p7={class:"new-modal-center"},v7={class:"new-modal-center-item"},h7=xo(()=>e("label",{class:"font16 font-zhongcu"},"旧密码",-1)),f7={class:"new-modal-center-item"},_7=xo(()=>e("label",{class:"font16 font-zhongcu"},"新密码",-1)),g7={class:"new-modal-center-item"},A7=xo(()=>e("label",{class:"font16 font-zhongcu"},"确认新密码",-1)),y7=xo(()=>e("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu"}," 提交 ",-1)),b7={__name:"ModifyPasswordModal",props:{showModifyPasswordModal:Boolean,userInfo:Object},emits:["cancelModifyPasswordModal"],setup(s,{emit:o}){const l=Ce(),t=n(""),d=n(""),m=n(""),u=s,r=o,v=()=>{r("cancelModifyPasswordModal",!1),t.value="",d.value="",m.value=""},k=Ze(async()=>{if(d.value!=m.value)E.error("两次密码不一致");else{const h=await Vn(u.userInfo.id,t.value,d.value);h.data.message?E.error(h.data.message):h.data.error=="0"&&(E.success({message:"修改成功",duration:500}),setTimeout(()=>{localStorage.removeItem("console_token"),l.push({name:"Login"})},500))}});return(h,g)=>{const x=_("el-input");return s.showModifyPasswordModal?(a(),c("div",d7,[e("div",u7,[m7,e("form",{onSubmit:g[3]||(g[3]=_e((...y)=>oe(k)&&oe(k)(...y),["prevent"]))},[e("div",p7,[e("div",v7,[h7,i(x,{modelValue:t.value,"onUpdate:modelValue":g[0]||(g[0]=y=>t.value=y),type:"password",placeholder:"旧密码",class:"field-ipt font14 font-zhongcu","show-password":"",required:""},null,8,["modelValue"])]),e("div",f7,[_7,i(x,{modelValue:d.value,"onUpdate:modelValue":g[1]||(g[1]=y=>d.value=y),type:"password",placeholder:"新密码",class:"field-ipt font14 font-zhongcu","show-password":"",required:""},null,8,["modelValue"])]),e("div",g7,[A7,i(x,{modelValue:m.value,"onUpdate:modelValue":g[2]||(g[2]=y=>m.value=y),type:"password",placeholder:"确认新密码",class:"field-ipt font14 font-zhongcu","show-password":"",required:""},null,8,["modelValue"])])]),e("div",{class:"new-modal-bottom"},[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu",onClick:v}," 取消 "),y7])],32)])])):z("",!0)}}},w7=ue(b7,[["__scopeId","data-v-913d1843"]]);function C7(){const s=Bt(),o=et(()=>s.userInfo),l=et(()=>{var u,r;return((r=(u=o.value)==null?void 0:u.roles)==null?void 0:r.some(v=>v.type==="super"))||!1}),t=u=>{var v;const r=((v=o.value)==null?void 0:v.permissions)||[];return l.value?!0:r.includes(u)};return{hasPermission:t,hasAnyPermission:u=>l.value?!0:u.some(r=>t(r)),hasAllPermissions:u=>l.value?!0:u.every(r=>t(r))}}const{serverConfig:Hs}=lt(),Je=new zt({baseURL:`${Hs.VITE_API_BASE_URL}/intellido`,headers:{"Content-Type":"multipart/form-data"}}),hk=async()=>Je.get("/api/cards/all",{}),fk=async s=>Je.get(`/api/cards/${s}`,{}),_k=async()=>Je.get("/api/apps/all",{}),gk=async(s,o,l,t)=>{const d=localStorage.getItem("console_token"),m=new FormData;m.append("content",s),l&&m.append("topic_id",l),o&&m.append("app_id",o),t&&m.append("scene_token",t);const u=await fetch(`${Hs.VITE_API_BASE_URL}/intellido/api/messages/add`,{method:"POST",headers:{Authorization:`Bearer ${d}`},body:m,timeout:0});if(!u.ok)throw new Error("Network response was not ok");return u.body},k7=async(s,o)=>{const l={};return s&&(l.app_id=s),o&&(l.scene_token=o),Je.get("/api/topics/by-time",{params:l})},Ak=async s=>Je.get("/api/messages/all",{params:{topic_id:s}}),x7=async(s,o,l)=>{const t={keywords:l};return s&&(t.app_id=s),o&&(t.scene_token=o),Je.get("/api/topics/search",{params:t})},yk=s=>{const o=localStorage.getItem("console_token"),l=encodeURIComponent(o);return`${Hs.VITE_API_BASE_URL}/intellido/api/files/file-convert?url=${s.url}&token=${l}`},S7=async s=>Je.post(`/api/topics/${s}/delete`),bk=async s=>Je.post("/api/messages/delete",{message_id:s}),wk=async(s,o)=>Je.post("/api/messages_comment/add",{message_id:s,vote:o}),Ck=async s=>Je.post("/api/messages_comment/delete",{message_id:s}),I7=async(s,o,l)=>Je.get("/api/favorites/page",{params:{page:s,count:o,keywords:l}}),kk=async(s,o,l)=>{let t;return l?t={message_id:s,content:o,idapp_id:l}:t={message_id:s,content:o},Je.post("/api/favorites/add",t)},B7=async s=>Je.get(`/api/favorites/${s}/delete`),T7=async(s,o)=>Je.post(`/api/favorites/${s}/update`,{title:o}),M7={name:"BaseSvgIcon"},Qe=ye({...M7,props:{size:{},color:{default:"#666666"},width:{default:40},height:{default:40},viewBox:{default:"0 0 40 40"},className:{default:""}},setup(s){return(o,l)=>{const t=_("el-icon");return a(),X(t,{color:o.color,size:o.size??o.width,class:W(o.className)},{default:p(()=>[We(o.$slots,"default")]),_:3},8,["color","size","class"])}}}),D7={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 14 14"},V7=["fill"],E7=["fill"],L7={name:"PopEditIcon"},z7=ye({...L7,props:{color:{default:"#A2A9B0"},size:{default:14},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[(a(),c("svg",D7,[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",fill:o.color,d:"M1.45801 12.2501C1.45801 11.9279 1.71918 11.6667 2.04134 11.6667H12.5413C12.8635 11.6667 13.1247 11.9279 13.1247 12.2501C13.1247 12.5722 12.8635 12.8334 12.5413 12.8334H2.04134C1.71918 12.8334 1.45801 12.5722 1.45801 12.2501Z"},null,8,V7),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",fill:o.color,d:"M9.24429 1.16675C9.39909 1.16672 9.54754 1.22821 9.65697 1.33769L11.7876 3.46922C12.0153 3.69701 12.0153 4.06621 11.7876 4.294L5.75509 10.3291C5.64568 10.4386 5.49727 10.5001 5.34252 10.5001H3.20833C2.88617 10.5001 2.625 10.2389 2.625 9.91675V7.79339C2.625 7.6388 2.68636 7.49053 2.79561 7.38115L8.83168 1.33785C8.94107 1.22833 9.0895 1.16678 9.24429 1.16675ZM9.24456 2.57538L3.79167 8.03481V9.33342H5.10082L10.5502 3.88161L9.24456 2.57538Z"},null,8,E7)]))]),_:1},8,["color","size","class"]))}}),F7=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 18 18"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M12 5.25V3.4275L13.6688 5.25H12ZM15.9263 5.49C15.9263 5.49 15.9487 5.5125 15.9562 5.5275C16.005 5.5875 16.0463 5.65875 16.0725 5.73C16.1025 5.8125 16.1213 5.9025 16.1213 5.99625V14.2463C16.1213 14.6588 15.7838 14.9963 15.3713 14.9963H4.875C4.4625 14.9963 4.125 14.6588 4.125 14.2463V1.5C4.125 1.0875 4.4625 0.75 4.875 0.75H11.25C11.3587 0.75 11.46 0.7725 11.5537 0.81375C11.6437 0.85125 11.7263 0.91125 11.7938 0.98625C11.7938 0.98625 11.8012 0.99375 11.805 0.9975L15.9263 5.49375V5.49ZM5.625 2.25V13.5H14.625V6.75H11.25C10.8375 6.75 10.5 6.4125 10.5 6V2.25H5.625ZM7.125 6.75C6.7125 6.75 6.375 7.0875 6.375 7.5C6.375 7.9125 6.7125 8.25 7.125 8.25H8.625C9.0375 8.25 9.375 7.9125 9.375 7.5C9.375 7.0875 9.0375 6.75 8.625 6.75H7.125ZM6.375 10.5C6.375 10.0875 6.7125 9.75 7.125 9.75H11.625C12.0375 9.75 12.375 10.0875 12.375 10.5C12.375 10.9125 12.0375 11.25 11.625 11.25H7.125C6.7125 11.25 6.375 10.9125 6.375 10.5ZM3.375 7.5C3.375 7.0875 3.0375 6.75 2.625 6.75C2.2125 6.75 1.875 7.0875 1.875 7.5V16.5C1.875 16.9125 2.2125 17.25 2.625 17.25H10.5C10.9125 17.25 11.25 16.9125 11.25 16.5C11.25 16.0875 10.9125 15.75 10.5 15.75H3.375V7.5Z",fill:"#A2A9B0"})],-1),U7={name:"CopyIcon"},P7=ye({...U7,props:{color:{default:"#A2A9B0"},size:{default:18},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[F7]),_:1},8,["color","size","class"]))}}),R7=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 18 18"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M6.075 1.125C5.71125 1.125 5.39625 1.3875 5.33625 1.7475L4.99125 3.75H2.25C1.8375 3.75 1.5 4.0875 1.5 4.5C1.5 4.9125 1.8375 5.25 2.25 5.25H3.42L4.125 16.1738C4.15125 16.5675 4.4775 16.875 4.875 16.875H13.125C13.5187 16.875 13.8487 16.5675 13.875 16.1738L14.58 5.25H15.75C16.1625 5.25 16.5 4.9125 16.5 4.5C16.5 4.0875 16.1625 3.75 15.75 3.75H13.0088L12.6638 1.7475C12.6038 1.3875 12.2887 1.125 11.925 1.125L6.075 1.125ZM11.4862 3.75H6.51375L6.705 2.625H11.2913L11.4825 3.75H11.4862ZM5.58 15.375L4.9275 5.25H13.0763L12.4237 15.375H5.58ZM7.125 12.375C6.7125 12.375 6.375 12.7125 6.375 13.125C6.375 13.5375 6.7125 13.875 7.125 13.875H10.875C11.2875 13.875 11.625 13.5375 11.625 13.125C11.625 12.7125 11.2875 12.375 10.875 12.375H7.125Z",fill:"#A2A9B0"})],-1),N7={name:"DelIcon"},q7=ye({...N7,props:{color:{default:"#A2A9B0"},size:{default:18},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[R7]),_:1},8,["color","size","class"]))}}),K7=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 18 18"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M1.5 1.5C1.08579 1.5 0.75 1.83579 0.75 2.25V13.5C0.75 13.9142 1.08579 14.25 1.5 14.25H6.81434L8.46967 15.9053C8.76256 16.1982 9.23744 16.1982 9.53033 15.9053L11.1857 14.25H16.5C16.9142 14.25 17.25 13.9142 17.25 13.5V2.25C17.25 1.83579 16.9142 1.5 16.5 1.5H1.5ZM2.25 12.75V3H15.75V12.75H10.875C10.6761 12.75 10.4853 12.829 10.3447 12.9697L9 14.3143L7.65533 12.9697C7.51468 12.829 7.32391 12.75 7.125 12.75H2.25ZM8.625 7.125C8.21079 7.125 7.875 7.46079 7.875 7.875C7.875 8.28921 8.21079 8.625 8.625 8.625H9.37594C9.79015 8.625 10.1259 8.28921 10.1259 7.875C10.1259 7.46079 9.79015 7.125 9.37594 7.125H8.625ZM11.6254 7.875C11.6254 7.46079 11.9612 7.125 12.3754 7.125H13.125C13.5392 7.125 13.875 7.46079 13.875 7.875C13.875 8.28921 13.5392 8.625 13.125 8.625H12.3754C11.9612 8.625 11.6254 8.28921 11.6254 7.875ZM4.87538 7.125C4.46116 7.125 4.12538 7.46079 4.12538 7.875C4.12538 8.28921 4.46116 8.625 4.87538 8.625H5.62496C6.03918 8.625 6.37496 8.28921 6.37496 7.875C6.37496 7.46079 6.03918 7.125 5.62496 7.125H4.87538Z",fill:"#A2A9B0"})],-1),H7={name:"MessageIcon"},Q7=ye({...H7,props:{color:{default:"#A2A9B0"},size:{default:18},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[K7]),_:1},8,["color","size","class"]))}}),O7=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 18 18"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M16.8341 2.11934C16.9279 1.84559 16.8566 1.54184 16.6466 1.33934C16.4366 1.13684 16.1329 1.07309 15.8629 1.17434L1.61287 6.47309C1.29787 6.59309 1.09912 6.90434 1.12537 7.23809C1.15162 7.57184 1.39912 7.84934 1.72912 7.91309L8.76037 9.27434L10.5229 16.3093C10.6054 16.6318 10.8866 16.8643 11.2204 16.8756C11.5541 16.8906 11.8541 16.6843 11.9629 16.3693L16.8341 2.11934ZM9.12787 7.81559L4.63912 6.94559L14.9029 3.12809L11.3629 13.4781L10.2041 8.85809L12.0266 7.03559C12.3191 6.74309 12.3191 6.26684 12.0266 5.97434C11.7341 5.68184 11.2579 5.68184 10.9654 5.97434L9.12412 7.81559H9.12787Z",fill:"#A2A9B0"})],-1),Z7={name:"SendMessageIcon"},$7=ye({...Z7,props:{color:{default:"#A2A9B0"},size:{default:18},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[O7]),_:1},8,["color","size","class"]))}}),Y7=s=>(be("data-v-ad412fc6"),s=s(),we(),s),j7={class:"favorite w-full h-full"},J7={class:"max-w-[1248px] mx-auto py-[35px]"},G7={class:"text-center"},W7=Y7(()=>e("div",{class:"font-zhongcu text-2xl mb-[30px]"},"指令收藏夹",-1)),X7={class:"favorite-list grid grid-cols-4 gap-4"},e9={class:"favorite-item-title relative h-6"},t9={class:"flex items-center w-full"},o9={class:"truncate font-bold text-sm"},s9={class:"ml-2"},n9={key:0,class:"absolute top-0 left-0 w-full h-full flex items-center justify-between bg-white border border-solid border-[rgba(0,0,0,0.2)] rounded-md px-[6px] py-[2px]"},a9={class:"flex-shrink-0 ml-2"},l9={class:"favorite-item-time text-[#A2A9B0] my-2"},i9={class:"favorite-item-content my-[15px] text-[#697077]"},c9={class:"favorite-item-bottom flex justify-between items-center"},r9={key:0,class:"bg-[#CFEAFE] text-[#0C78DB] rounded-md px-[8px] truncate leading-6 max-w-[45%] w-auto"},d9={key:1},u9={class:"flex-shrink-0"},m9={class:"flex items-center justify-end my-3 px-5 py-3"},p9=ye({__name:"FavoriteComponent",props:{isShowFavorite:Boolean},emits:["isShowFavorite"],setup(s,{emit:o}){const l=Ce(),t=De(),d=n(""),m=n([]),u=n(1),r=n(500),v=n(0),k=async()=>{var I;try{const U=await I7(u.value,r.value,d.value);m.value=U.data.data,v.value=((I=U.data.total_num)==null?void 0:I[0])||0}catch(U){console.log(U)}},h=I=>{u.value=I,k()},g=n("确认收藏"),x=n("确定要收藏吗？"),y=n(!1),w=n(0),T=I=>{console.log(I),w.value=I,y.value=!0},A=()=>{y.value=!1,w.value=0},C=async()=>{try{console.log(w.value);const I=await B7(w.value);I.data.error=="0"?(E.success("删除成功"),k()):I.data.message?E.error("删除失败，"+I.data.message):E.error("删除失败")}catch{E.error("删除失败")}finally{y.value=!1,w.value=0}},L=I=>{try{var U=/([\n\r])+/g;const J=I.replace(U,`
`),O=document.createElement("textarea");O.value=J,document.body.appendChild(O),O.select(),document.execCommand("copy"),document.body.removeChild(O),E.success("内容已复制")}catch{E.error("复制失败")}},B=o,P=(I,U,J)=>{const O=t.query._t,Y={},R=encodeURIComponent(I);J=="fuyong"?Y.content=R:Y.question=R,U&&(Y.app_id=U),O&&(Y._t=O),B("isShowFavorite",!1),l.replace({path:"Chat",query:Y}).then(()=>{window.location.reload()})},b=n(""),M=n(""),D=(I,U)=>{b.value=I,M.value=U},V=fn.debounce(async I=>{try{const U=await T7(b.value,M.value);U.data.error=="0"?(E.success("保存成功"),I.title=M.value):U.data.message?E.error("保存失败，"+U.data.message):E.error("保存失败")}catch{E.error("保存失败")}finally{b.value="",M.value=""}},100);ge(()=>{k()});const f=I=>{I.key==="Enter"&&k()};return(I,U)=>{const J=_("el-input"),O=_("el-pagination"),Y=_("v2ConfirmsModal");return a(),c(N,null,[e("div",j7,[e("div",J7,[e("div",G7,[W7,i(J,{modelValue:d.value,"onUpdate:modelValue":U[0]||(U[0]=R=>d.value=R),placeholder:"输入指令关键字或应用名称","prefix-icon":oe(xs),class:"h-[60px] mb-[30px]",style:{"border-radius":"15px",overflow:"hidden",width:"960px"},onKeyup:f},null,8,["modelValue","prefix-icon"])]),e("div",X7,[(a(!0),c(N,null,le(m.value,R=>(a(),c("div",{class:"favorite-item bg-white p-5 text-xs rounded-[20px]",key:R.id},[e("div",e9,[e("div",t9,[e("div",o9,S(R.title),1),e("div",s9,[i(oe(mt),{type:"",size:"small",text:"",class:"w-5 h-5",onClick:j=>D(R.id,R.title)},{default:p(()=>[i(z7,{size:"18"})]),_:2},1032,["onClick"])])]),b.value==R.id?(a(),c("div",n9,[i(J,{modelValue:M.value,"onUpdate:modelValue":U[1]||(U[1]=j=>M.value=j),size:"small",class:"w-[80%]",style:{height:"16px"}},null,8,["modelValue"]),e("div",a9,[i(oe(mt),{type:"",size:"small",text:"",onClick:j=>oe(V)(R),style:{width:"16px",height:"16px","margin-left":"4px"}},{default:p(()=>[i(oe(tt),{color:"#52C668",size:"16"},{default:p(()=>[i(oe(wa))]),_:1})]),_:2},1032,["onClick"]),i(oe(mt),{type:"",size:"small",text:"",onClick:U[2]||(U[2]=j=>{b.value="",M.value=""}),style:{width:"16px",height:"16px","margin-left":"4px"}},{default:p(()=>[i(oe(tt),{color:"#CD1525",size:"16"},{default:p(()=>[i(oe(Ca))]),_:1})]),_:1})])])):z("",!0)]),e("div",l9,S(R.create_time),1),e("div",i9,S(R.content),1),e("div",c9,[R.idapp?(a(),c("div",r9,S(R.idapp.name),1)):(a(),c("div",d9)),e("div",u9,[i(oe(Uo),{class:"box-item",effect:"dark",content:"复用",placement:"bottom"},{default:p(()=>[i(oe(mt),{type:"",size:"small",text:"",onClick:j=>P(R.content,R.idapp.id,"fuyong")},{default:p(()=>[i(Q7,{size:"18"})]),_:2},1032,["onClick"])]),_:2},1024),i(oe(Uo),{class:"box-item",effect:"dark",content:"运行",placement:"bottom"},{default:p(()=>[i(oe(mt),{type:"",size:"small",text:"",onClick:j=>P(R.content,R.idapp.id,"yunxing")},{default:p(()=>[i($7,{size:"18"})]),_:2},1032,["onClick"])]),_:2},1024),i(oe(Uo),{class:"box-item",effect:"dark",content:"复制文本",placement:"bottom"},{default:p(()=>[i(oe(mt),{type:"",size:"small",text:"",onClick:j=>L(R.content)},{default:p(()=>[i(P7,{size:"18"})]),_:2},1032,["onClick"])]),_:2},1024),i(oe(Uo),{class:"box-item",effect:"dark",content:"删除",placement:"bottom"},{default:p(()=>[i(oe(mt),{type:"",size:"small",text:"",onClick:j=>T(R.id)},{default:p(()=>[i(q7,{size:"18"})]),_:2},1032,["onClick"])]),_:2},1024)])])]))),128))])]),e("div",m9,[i(O,{background:"",layout:"prev, pager, next",total:v.value,"page-size":r.value,"current-page":u.value,onCurrentChange:h,"hide-on-single-page":!0},null,8,["total","page-size","current-page"])])]),i(Y,{show:y.value,title:g.value,message:x.value,onClose:A,onConfirm:C},null,8,["show","title","message"])],64)}}}),v9=ue(p9,[["__scopeId","data-v-ad412fc6"]]),$n="/nq/static/png/admin-logo-CIt39KKz.png",h9={xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},f9=["fill"],_9={name:"DepartmentIcon"},g9=ye({..._9,props:{color:{default:"#343A3F"},size:{default:14},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[(a(),c("svg",h9,[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.95768 1.16699C4.63552 1.16699 4.37435 1.42816 4.37435 1.75033V4.37533C4.37435 4.69749 4.63552 4.95866 4.95768 4.95866H6.41602V6.41699H3.79102C3.46885 6.41699 3.20768 6.67816 3.20768 7.00033V9.04199H1.74935C1.42718 9.04199 1.16602 9.30316 1.16602 9.62533V12.2503C1.16602 12.5725 1.42718 12.8337 1.74935 12.8337H5.83268C6.15485 12.8337 6.41602 12.5725 6.41602 12.2503V9.62533C6.41602 9.30316 6.15485 9.04199 5.83268 9.04199H4.37435V7.58366H6.99935H9.62435V9.04199H8.16602C7.84385 9.04199 7.58268 9.30316 7.58268 9.62533V12.2503C7.58268 12.5725 7.84385 12.8337 8.16602 12.8337H12.2493C12.5715 12.8337 12.8327 12.5725 12.8327 12.2503V9.62533C12.8327 9.30316 12.5715 9.04199 12.2493 9.04199H10.791V7.00033C10.791 6.67816 10.5298 6.41699 10.2077 6.41699H7.58268V4.95866H9.04102C9.36318 4.95866 9.62435 4.69749 9.62435 4.37533V1.75033C9.62435 1.42816 9.36318 1.16699 9.04102 1.16699H4.95768Z",fill:o.color},null,8,f9)]))]),_:1},8,["color","size","class"]))}}),A9={xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},y9=["fill"],b9=["fill"],w9={name:"UserIcon"},C9=ye({...w9,props:{color:{default:"#343A3F"},size:{default:14},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[(a(),c("svg",A9,[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.37533 3.7915C4.37533 5.23817 5.55366 6.4165 7.00033 6.4165C8.44699 6.4165 9.62533 5.23817 9.62533 3.7915C9.62533 2.34484 8.44699 1.1665 7.00033 1.1665C5.55366 1.1665 4.37533 2.34484 4.37533 3.7915ZM5.54199 3.7915C5.54199 2.9865 6.19533 2.33317 7.00033 2.33317C7.80533 2.33317 8.45866 2.9865 8.45866 3.7915C8.45866 4.5965 7.80533 5.24984 7.00033 5.24984C6.19533 5.24984 5.54199 4.5965 5.54199 3.7915Z",fill:o.color},null,8,y9),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M1.16699 12.2498C1.16699 12.5707 1.42949 12.8332 1.75033 12.8332H12.2503C12.5712 12.8332 12.8337 12.5707 12.8337 12.2498V11.8998C12.8337 10.4998 12.8337 9.79984 12.5157 9.17567C12.2357 8.62442 11.7924 8.18109 11.2412 7.90109C10.617 7.58317 9.91408 7.58317 8.51699 7.58317H5.48366C4.08658 7.58317 3.38366 7.58317 2.75949 7.90109C2.20824 8.18109 1.76491 8.62442 1.48491 9.17567C1.16699 9.79984 1.16699 10.5028 1.16699 11.8998V12.2498ZM11.667 11.6665H2.33949C2.33949 10.6107 2.35116 10.0565 2.52908 9.7065C2.69824 9.37692 2.96074 9.1115 3.29324 8.94234C3.66658 8.75275 4.27616 8.75275 5.48658 8.75275H8.51991C9.73033 8.75275 10.3399 8.75275 10.7132 8.94234C11.0428 9.1115 11.3082 9.374 11.4774 9.7065C11.6553 10.0536 11.667 10.6078 11.667 11.6665Z",fill:o.color},null,8,b9)]))]),_:1},8,["color","size","class"]))}}),k9={xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},x9=["fill"],S9={name:"RoleIcon"},I9=ye({...S9,props:{color:{default:"#343A3F"},size:{default:14},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[(a(),c("svg",k9,[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.33268 0.583008C2.01052 0.583008 1.74935 0.844175 1.74935 1.16634V3.49967C1.74935 3.82184 2.01052 4.08301 2.33268 4.08301C2.65485 4.08301 2.91602 3.82184 2.91602 3.49967V1.74967H11.0827V12.2497H2.91602V10.4997C2.91602 10.1775 2.65485 9.91634 2.33268 9.91634C2.01052 9.91634 1.74935 10.1775 1.74935 10.4997V12.833C1.74935 13.1552 2.01052 13.4163 2.33268 13.4163H11.666C11.9882 13.4163 12.2493 13.1552 12.2493 12.833V1.16634C12.2493 0.844175 11.9882 0.583008 11.666 0.583008H2.33268ZM1.74935 8.16634C1.42718 8.16634 1.16602 8.42751 1.16602 8.74967C1.16602 9.07184 1.42718 9.33301 1.74935 9.33301H2.91602C3.23818 9.33301 3.49935 9.07184 3.49935 8.74967C3.49935 8.42751 3.23818 8.16634 2.91602 8.16634H1.74935ZM1.16602 6.99967C1.16602 6.67751 1.42718 6.41634 1.74935 6.41634H2.91602C3.23818 6.41634 3.49935 6.67751 3.49935 6.99967C3.49935 7.32184 3.23818 7.58301 2.91602 7.58301H1.74935C1.42718 7.58301 1.16602 7.32184 1.16602 6.99967ZM1.74935 4.66634C1.42718 4.66634 1.16602 4.92751 1.16602 5.24967C1.16602 5.57184 1.42718 5.83301 1.74935 5.83301H2.91602C3.23818 5.83301 3.49935 5.57184 3.49935 5.24967C3.49935 4.92751 3.23818 4.66634 2.91602 4.66634H1.74935ZM6.99935 4.37467C6.67718 4.37467 6.41602 4.63584 6.41602 4.95801C6.41602 5.28017 6.67718 5.54134 6.99935 5.54134C7.32152 5.54134 7.58268 5.28017 7.58268 4.95801C7.58268 4.63584 7.32152 4.37467 6.99935 4.37467ZM5.24935 4.95801C5.24935 3.99151 6.03285 3.20801 6.99935 3.20801C7.96585 3.20801 8.74935 3.99151 8.74935 4.95801C8.74935 5.92451 7.96585 6.70801 6.99935 6.70801C6.03285 6.70801 5.24935 5.92451 5.24935 4.95801ZM6.99935 7.29134C5.38851 7.29134 4.08268 8.59717 4.08268 10.208C4.08268 10.5302 4.34385 10.7913 4.66602 10.7913C4.98818 10.7913 5.24935 10.5302 5.24935 10.208C5.24935 9.2415 6.03284 8.45801 6.99935 8.45801C7.96585 8.45801 8.74935 9.2415 8.74935 10.208C8.74935 10.5302 9.01052 10.7913 9.33268 10.7913C9.65485 10.7913 9.91602 10.5302 9.91602 10.208C9.91602 8.59717 8.61019 7.29134 6.99935 7.29134Z",fill:o.color},null,8,x9)]))]),_:1},8,["color","size","class"]))}}),B9={xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},T9=["fill"],M9={name:"LicenseIcon"},D9=ye({...M9,props:{color:{default:"#343A3F"},size:{default:14},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[(a(),c("svg",B9,[e("path",{d:"M10.5003 0.583984C11.1449 0.583984 11.667 1.10607 11.667 1.75065V6.24232C11.667 6.56315 11.4045 6.82565 11.0837 6.82565C10.7628 6.82565 10.5003 6.56315 10.5003 6.24232V1.75065H2.33366V12.2507H5.25033C5.57116 12.2507 5.83366 12.5132 5.83366 12.834C5.83366 13.1548 5.57116 13.4173 5.25033 13.4173H2.33366C1.68908 13.4173 1.16699 12.8952 1.16699 12.2507V1.75065C1.16699 1.10607 1.68908 0.583984 2.33366 0.583984H10.5003ZM11.9995 8.61357C12.2357 8.39482 12.6062 8.4094 12.8249 8.64565C13.0437 8.8819 13.0291 9.25232 12.7928 9.47107L9.29283 12.6794C9.07408 12.8807 8.73866 12.8865 8.51408 12.6911L6.61824 11.0869L6.57449 11.0461C6.37324 10.8302 6.36158 10.4919 6.55991 10.2644C6.75824 10.0369 7.09366 9.99899 7.33574 10.1682L7.38241 10.2061L8.88449 11.469L12.0024 8.61357H11.9995ZM5.54199 5.54232C5.86283 5.54232 6.12533 5.80482 6.12533 6.12565C6.12533 6.44648 5.86283 6.70898 5.54199 6.70898H4.08366C3.76283 6.70898 3.50033 6.44648 3.50033 6.12565C3.50033 5.80482 3.76283 5.54232 4.08366 5.54232H5.54199ZM7.87533 3.50065C8.19616 3.50065 8.45866 3.76315 8.45866 4.08398C8.45866 4.40482 8.19616 4.66732 7.87533 4.66732H4.08366C3.76283 4.66732 3.50033 4.40482 3.50033 4.08398C3.50033 3.76315 3.76283 3.50065 4.08366 3.50065H7.87533Z",fill:o.color},null,8,T9)]))]),_:1},8,["color","size","class"]))}}),V9={xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},E9=["fill"],L9={name:"ModelProviderIcon"},z9=ye({...L9,props:{color:{default:"#343A3F"},size:{default:14},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[(a(),c("svg",V9,[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M6.7388 0.64508C6.90303 0.562968 7.09632 0.562968 7.26055 0.64508L13.0939 3.56175C13.2915 3.66056 13.4163 3.86255 13.4163 4.0835V9.91683C13.4163 10.1378 13.2915 10.3398 13.0939 10.4386L7.26055 13.3552C7.09632 13.4374 6.90303 13.4374 6.7388 13.3552L0.905467 10.4386C0.707843 10.3398 0.583008 10.1378 0.583008 9.91683V4.0835C0.583008 3.86255 0.707843 3.66056 0.905467 3.56175L6.7388 0.64508ZM1.74967 9.55631V5.02738L6.41634 7.36071V11.8896L1.74967 9.55631ZM7.58301 7.36068V11.8896L12.2497 9.55631V5.02734L7.58301 7.36068ZM6.99964 6.34799L11.5286 4.08349L9.91634 3.27735L5.38735 5.54184L6.99964 6.34799ZM4.08298 4.88965L8.61196 2.62516L6.99967 1.81902L2.47069 4.08351L4.08298 4.88965Z",fill:o.color},null,8,E9)]))]),_:1},8,["color","size","class"]))}}),F9={xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},U9=["fill"],P9={name:"DataSourceIcon"},R9=ye({...P9,props:{color:{default:"#343A3F"},size:{default:14},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[(a(),c("svg",F9,[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M1.75259 2.91683C1.76426 2.94308 1.80801 3.01308 1.96259 3.121C2.18426 3.2785 2.54592 3.44475 3.04176 3.5935C4.03051 3.891 5.42759 4.08058 6.99967 4.08058C8.57176 4.08058 9.96884 3.88808 10.9576 3.5935C11.4563 3.44475 11.8151 3.2785 12.0368 3.121C12.1913 3.01308 12.2351 2.94016 12.2468 2.91683C12.2351 2.89058 12.1913 2.82058 12.0368 2.71266C11.8151 2.55516 11.4534 2.38891 10.9576 2.24016C9.96884 1.94266 8.57176 1.75308 6.99967 1.75308C5.42759 1.75308 4.03051 1.94558 3.04176 2.24016C2.54301 2.38891 2.18426 2.55516 1.96259 2.71266C1.80801 2.82058 1.76426 2.8935 1.75259 2.91683ZM2.70634 1.12016C3.82926 0.781829 5.34884 0.583496 6.99967 0.583496C8.65051 0.583496 10.1672 0.784746 11.293 1.12016C11.8501 1.28641 12.3459 1.49933 12.7134 1.75891C13.0663 2.00975 13.4163 2.39183 13.4163 2.91683C13.4163 2.97516 13.4134 3.0335 13.4047 3.086C13.4134 3.12391 13.4163 3.16475 13.4163 3.2085H13.3784C13.4163 3.2085 13.4163 3.2085 13.4163 3.2085V8.4585V11.0835C13.4163 11.6085 13.0634 11.9906 12.7134 12.2414C12.3459 12.501 11.853 12.7139 11.293 12.8802C10.1701 13.2185 8.65051 13.4168 6.99967 13.4168C5.34884 13.4168 3.83217 13.2156 2.70634 12.8802C2.14926 12.7139 1.65342 12.501 1.28592 12.2414C0.935925 11.9906 0.583008 11.6085 0.583008 11.0835V8.46141C0.583008 8.46141 0.583008 8.46141 0.583008 8.4585V5.83933V3.21725H0.620924C0.595647 3.21725 0.583008 3.21725 0.583008 3.21725C0.583008 3.17641 0.585924 3.13558 0.594674 3.09475C0.585924 3.03933 0.583008 2.98391 0.583008 2.92558C0.583008 2.40058 0.935925 2.0185 1.28592 1.76766C1.65342 1.50808 2.14634 1.29516 2.70634 1.12891V1.12016ZM1.74967 7.26266V8.44975C1.74967 8.44975 1.78759 8.54016 1.96259 8.66266C2.18426 8.82016 2.54592 8.98641 3.04176 9.13516C4.03051 9.43266 5.42759 9.62225 6.99967 9.62225C8.57176 9.62225 9.96884 9.42975 10.9576 9.13516C11.4563 8.98641 11.8151 8.82016 12.0368 8.66266C12.2118 8.53725 12.2438 8.46433 12.2497 8.44975V7.26266C11.9668 7.40266 11.6401 7.52516 11.293 7.63016C10.1701 7.9685 8.65051 8.16683 6.99967 8.16683C5.34884 8.16683 3.83217 7.96558 2.70634 7.63016C2.35634 7.52516 2.03259 7.40266 1.74967 7.26266ZM12.2497 5.82475C12.2497 5.82475 12.2118 5.91516 12.0368 6.03766C11.8151 6.19516 11.4534 6.36141 10.9576 6.51016C9.96884 6.80766 8.57176 6.99725 6.99967 6.99725C5.42759 6.99725 4.03051 6.80475 3.04176 6.51016C2.54301 6.36141 2.18426 6.19516 1.96259 6.03766C1.78759 5.91225 1.75551 5.83933 1.74967 5.82475V4.346C2.03259 4.486 2.35926 4.6085 2.70634 4.7135C3.82926 5.05183 5.34884 5.25016 6.99967 5.25016C8.65051 5.25016 10.1672 5.04891 11.293 4.7135C11.643 4.6085 11.9668 4.486 12.2497 4.346V5.82475ZM12.2497 9.88766C11.9668 10.0277 11.6401 10.1502 11.293 10.2552C10.1701 10.5935 8.65051 10.7918 6.99967 10.7918C5.34884 10.7918 3.83217 10.5906 2.70634 10.2552C2.35634 10.1502 2.03259 10.0277 1.74967 9.88766V11.0747C1.74967 11.0747 1.78759 11.1652 1.96259 11.2877C2.18426 11.4452 2.54592 11.6114 3.04176 11.7602C4.03051 12.0577 5.42759 12.2472 6.99967 12.2472C8.57176 12.2472 9.96884 12.0547 10.9576 11.7602C11.4563 11.6114 11.8151 11.4452 12.0368 11.2877C12.2118 11.1622 12.2438 11.0893 12.2497 11.0747V9.88766Z",fill:o.color},null,8,U9)]))]),_:1},8,["color","size","class"]))}}),N9={xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},q9=["fill"],K9={name:"ApiExtensionIcon"},H9=ye({...K9,props:{color:{default:"#343A3F"},size:{default:14},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[(a(),c("svg",N9,[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7.41125 0.755592C7.35583 0.700176 7.29167 0.656426 7.22167 0.630176C7.15167 0.601009 7.07875 0.586426 6.99708 0.586426C6.83667 0.586426 6.69083 0.650592 6.58583 0.755592L7.21875 0.630176L6.58292 0.758509L5.12458 2.21684C4.89708 2.44434 4.89708 2.81476 5.12458 3.04226C5.35208 3.26976 5.7225 3.26976 5.95 3.04226L6.41375 2.57851V9.89934L4.08042 7.97143V6.90393C4.76 6.66476 5.24708 6.01434 5.24708 5.25309C5.24708 4.28768 4.4625 3.50309 3.49708 3.50309C2.53167 3.50309 1.75 4.28476 1.75 5.25018C1.75 6.01143 2.23708 6.65893 2.91667 6.90101V8.24268C2.91667 8.41768 2.99542 8.58101 3.12958 8.69184L6.41667 11.4073V11.9498C6.41667 11.9498 6.41667 11.9614 6.41667 11.9643V12.2473H6.125C5.80417 12.2473 5.54167 12.5098 5.54167 12.8306C5.54167 13.1514 5.80417 13.4139 6.125 13.4139H7.875C8.19583 13.4139 8.45833 13.1514 8.45833 12.8306C8.45833 12.5098 8.19583 12.2473 7.875 12.2473H7.60667L10.8296 10.0423C10.9871 9.93434 11.0833 9.75351 11.0833 9.56101V8.64809C11.7629 8.40893 12.25 7.75851 12.25 6.99726C12.25 6.03184 11.4654 5.24726 10.5 5.24726C9.53458 5.24726 8.75 6.03184 8.75 6.99726C8.75 7.75851 9.23708 8.40601 9.91667 8.64809V9.25476L7.58333 10.8502V2.57559L8.04708 3.03934C8.27458 3.26684 8.645 3.26684 8.8725 3.03934C9.1 2.81184 9.1 2.44143 8.8725 2.21393L7.41125 0.755592ZM4.08333 5.25018C4.08333 5.57101 3.82083 5.83351 3.5 5.83351C3.17917 5.83351 2.91667 5.57101 2.91667 5.25018C2.91667 4.92934 3.17917 4.66684 3.5 4.66684C3.82083 4.66684 4.08333 4.92934 4.08333 5.25018ZM10.5 6.41684C10.1792 6.41684 9.91667 6.67934 9.91667 7.00018C9.91667 7.32101 10.1792 7.58351 10.5 7.58351C10.8208 7.58351 11.0833 7.32101 11.0833 7.00018C11.0833 6.67934 10.8208 6.41684 10.5 6.41684Z",fill:o.color},null,8,q9)]))]),_:1},8,["color","size","class"]))}}),Q9={xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},O9=["fill"],Z9={name:"SystemConfigIcon"},$9=ye({...Z9,props:{color:{default:"#343A3F"},size:{default:14},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[(a(),c("svg",Q9,[e("path",{d:"M4.08277 1.45898C3.87568 1.45898 3.6861 1.56982 3.5811 1.74773L0.664434 6.70607C0.556517 6.88982 0.556517 7.1144 0.664434 7.29815L3.5811 12.2565C3.6861 12.4344 3.8786 12.5452 4.08277 12.5452H9.9161C10.1232 12.5452 10.3128 12.4344 10.4178 12.2565L13.3344 7.29815C13.4424 7.1144 13.4424 6.88982 13.3344 6.70607L10.4178 1.74773C10.3128 1.56982 10.1203 1.45898 9.9161 1.45898H4.08277ZM1.84277 7.00065L4.41527 2.62565H9.58068L12.1532 7.00065L9.58068 11.3757H4.41527L1.84277 7.00065ZM6.99943 4.95898C6.45693 4.95898 5.93777 5.17482 5.55568 5.5569C5.1736 5.93898 4.95777 6.45815 4.95777 7.00065C4.95777 7.54315 5.1736 8.06232 5.55568 8.4444C5.93777 8.82649 6.45693 9.04232 6.99943 9.04232C7.54193 9.04232 8.0611 8.82649 8.44318 8.4444C8.82527 8.06232 9.0411 7.54315 9.0411 7.00065C9.0411 6.45815 8.82527 5.93898 8.44318 5.5569C8.0611 5.17482 7.54193 4.95898 6.99943 4.95898ZM6.3811 6.38232C6.54443 6.21898 6.76902 6.12565 6.99943 6.12565C7.22985 6.12565 7.45443 6.21898 7.61777 6.38232C7.7811 6.54565 7.87443 6.77023 7.87443 7.00065C7.87443 7.23107 7.7811 7.45565 7.61777 7.61899C7.45443 7.78232 7.22985 7.87565 6.99943 7.87565C6.76902 7.87565 6.54443 7.78232 6.3811 7.61899C6.21777 7.45565 6.12443 7.23107 6.12443 7.00065C6.12443 6.77023 6.21777 6.54565 6.3811 6.38232Z",fill:o.color},null,8,O9)]))]),_:1},8,["color","size","class"]))}}),Yn=vo("menuV3",()=>{const s=n([{key:"enterprise_title",label:"企业管理",path:"",isTitle:!0},{key:"department",label:"部门管理",path:"/setting/department",icon:at(g9)},{key:"user",label:"用户管理",path:"/setting/user",icon:at(C9)},{key:"role",label:"角色管理",path:"/setting/role",icon:at(I9)},{key:"workspace_title",label:"工作空间设置",path:"",isTitle:!0},{key:"model_provider",label:"模型供应商",path:"/setting/model-provider",icon:at(z9)},{key:"data_source",label:"数据来源",path:"/setting/data-source",icon:at(R9)},{key:"api_extension",label:"API扩展",path:"/setting/api-extension",icon:at(H9)},{key:"license",label:"授权管理",path:"/setting/license",icon:at(D9),roles:["super"]},{key:"system_title",label:"系统设置",path:"",isTitle:!0},{key:"system-config",label:"设置",path:"/setting/system-config",icon:at($9),roles:["super","normal"]}]),o=n("department"),l=n(""),t=et(()=>{var v;const r=s.value.find(k=>k.key===o.value);return!!((v=r==null?void 0:r.children)!=null&&v.length)}),d=et(()=>{const r=s.value.find(v=>v.key===o.value);return(r==null?void 0:r.children)||[]});return{menuItems:s,activeMenu:o,activeSubMenu:l,showSubMenu:t,currentSubMenus:d,setActiveMenu:r=>{o.value=r},setActiveSubMenu:r=>{l.value=r}}}),Y9={class:"fixed inset-0 min-h-screen w-full bg-white/50 p-6 backdrop-blur-[20px] z-[100]"},j9=["src"],J9=ye({__name:"index",props:{onCancel:{type:Function,required:!0}},setup(s){const{globalConfig:o}=lt(),l=n(""),t=n(null),d=s,m=u=>{const r=u.data;r&&r.from==="niepan-embed"&&r&&r.type==="close-modal"&&d.onCancel()};return ge(()=>{window.addEventListener("message",m),o.workspaceSettings&&o.workspaceSettings.modalLanguage?l.value=o.workspaceSettings.modalLanguage:(console.warn("API扩展嵌入URL未配置，请检查config.js文件"),l.value="http://localhost:3000/embed/modal-language")}),Yt(()=>{window.removeEventListener("message",m)}),(u,r)=>(a(),c("div",Y9,[e("iframe",{src:l.value,class:"w-full h-full border-0",frameborder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowfullscreen:"",ref_key:"iframeRef",ref:t},null,8,j9)]))}}),G9={class:"fixed inset-0 min-h-screen w-full bg-white/50 p-6 backdrop-blur-[20px] z-[100]"},W9=["src"],X9=ye({__name:"index",props:{onCancel:{type:Function,required:!0}},setup(s){const{globalConfig:o}=lt(),l=n(""),t=n(null),d=s,m=u=>{const r=u.data;r&&r.from==="niepan-embed"&&r&&r.type==="close-modal"&&d.onCancel()};return ge(()=>{window.addEventListener("message",m),o.workspaceSettings&&o.workspaceSettings.modalUserinfo?l.value=o.workspaceSettings.modalUserinfo:(console.warn("API扩展嵌入URL未配置，请检查config.js文件"),l.value="http://localhost:3000/embed/modal-userinfo")}),Yt(()=>{window.removeEventListener("message",m)}),(u,r)=>(a(),c("div",G9,[e("iframe",{src:l.value,class:"w-full h-full border-0",frameborder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowfullscreen:"",ref_key:"iframeRef",ref:t},null,8,W9)]))}}),ey={class:"fixed inset-0 min-h-screen w-full bg-white/50 p-6 backdrop-blur-[20px] z-[100]"},ty=["src"],oy=ye({__name:"index",props:{onCancel:{type:Function,required:!0}},setup(s){const{globalConfig:o}=lt(),l=n(""),t=n(null),d=s,m=u=>{const r=u.data;r&&r.from==="niepan-embed"&&r&&r.type==="close-modal"&&d.onCancel()};return ge(()=>{window.addEventListener("message",m),o.workspaceSettings&&o.workspaceSettings.modalAbout?l.value=o.workspaceSettings.modalAbout:(console.warn("API扩展嵌入URL未配置，请检查config.js文件"),l.value="http://localhost:3000/embed/modal-about")}),Yt(()=>{window.removeEventListener("message",m)}),(u,r)=>(a(),c("div",ey,[e("iframe",{src:l.value,class:"w-full h-full border-0",frameborder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowfullscreen:"",ref_key:"iframeRef",ref:t},null,8,ty)]))}}),Rt=()=>{const{serverConfig:s}=lt();return new zt({baseURL:`${s.CONSOLE_API_BASE_URL}/console`,headers:{"Content-Type":"application/json"}})},xk=async s=>Rt().get("/api/installed-apps",{params:s}),Sk=async()=>Rt().get("/api/tags",{params:{type:"app"}}),Ik=async s=>Rt().request("PATCH",`/api/installed-apps/${s}`,{},{headers:{"Content-Type":"application/json"},data:{is_pinned:!0}}),Bk=async s=>Rt().request("PATCH",`/api/installed-apps/${s}`,{},{headers:{"Content-Type":"application/json"},data:{is_pinned:!1}}),sy=async()=>Rt().get("/api/version",{}),ny=async()=>Rt().get("/api/workspaces/current/sys-configs",{}),Tk=async s=>Rt().post("/api/workspaces/current/sys-configs",s),ay=vo("version",{state:()=>({versionsData:{}}),actions:{async fetchVersion(){try{const s=await sy();s.data&&(this.versionsData=s.data)}catch(s){console.error("获取版本信息失败:",s)}}}}),ly=vo("sysConfigs",{state:()=>({sysConfigsData:{}}),actions:{async fetchSysConfigs(){try{const s=await ny();s.data&&(this.sysConfigsData=s.data)}catch(s){console.error("获取版本信息失败:",s)}}}}),iy={class:"flex items-center"},cy={href:"/",class:"flex items-center"},ry=["alt"],dy={class:"flex items-center"},uy=e("div",{class:"text-[#10182824] mx-2 font-light"},"/",-1),my=e("div",{class:"flex h-7 w-7 items-center justify-center rounded-lg bg-[#EBF7FF] text-xs font-medium text-[#129bfe]"}," R ",-1),py={class:"flex flex-row items-center"},vy={class:"system-sm-medium max-w-[80px] truncate text-[#343a3f] ml-1.5"},hy=e("path",{d:"M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"},null,-1),fy=[hy],_y={key:0,class:"absolute top-full left-0 mt-1 bg-white shadow-lg rounded-xl w-[200px] z-[100]",style:{border:"1px solid #eaeaea"}},gy={class:"py-1"},Ay={class:"text-[#343a3f] text-xs px-3 py-1.5"},yy={class:"flex items-center px-3 py-2 hover:bg-[#F5F5F5] cursor-pointer"},by=e("div",{class:"flex h-6 w-6 items-center justify-center rounded-lg bg-[#EBF7FF] text-xs font-medium text-[#129bfe] mr-2"}," R ",-1),wy={class:"text-[#343a3f] text-[14px] leading-[20px]"},Cy={class:"flex items-center gap-2 relative"},ky={key:0,href:"/apps",class:"flex items-center gap-1 text-[#129BFE] text-[14px] px-3 py-1.5 rounded-[6px] bg-[#CFEAFE] font-medium leading-[22px] font-['PingFang_SC']",style:{"font-family":"'PingFang SC', sans-serif"}},xy=_n('<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none" class="flex-shrink-0"><g clip-path="url(#clip0_928_19457)"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.5 3.75C7.5 2.92125 8.17125 2.25 9 2.25C9.82875 2.25 10.5 2.92125 10.5 3.75V4.50375C10.5 4.91625 10.8375 5.25375 11.25 5.25375C11.6625 5.25375 12 4.91625 12 4.50375V3.75C12 2.0925 10.6575 0.75 9 0.75C7.3425 0.75 6 2.0925 6 3.75V8.9775C6 9.39 6.3375 9.7275 6.75 9.7275C7.1625 9.7275 7.5 9.39 7.5 8.9775V3.75ZM12 9C12 8.5875 11.6625 8.25 11.25 8.25C10.8375 8.25 10.5 8.5875 10.5 9V14.25C10.5 15.0788 9.82875 15.75 9 15.75C8.17125 15.75 7.5 15.0788 7.5 14.25V13.4888C7.5 13.0763 7.1625 12.7387 6.75 12.7387C6.3375 12.7387 6 13.0763 6 13.4888V14.25C6 15.9075 7.3425 17.25 9 17.25C10.6575 17.25 12 15.9075 12 14.25V9ZM3.7425 7.5C2.9175 7.5 2.25 8.17125 2.25 9C2.25 9.82875 2.92125 10.5 3.7425 10.5H9C9.4125 10.5 9.75 10.8375 9.75 11.25C9.75 11.6625 9.4125 12 9 12H3.7425C2.08875 12 0.75 10.6537 0.75 9C0.75 7.34625 2.08875 6 3.7425 6H4.4925C4.905 6 5.2425 6.3375 5.2425 6.75C5.2425 7.1625 4.905 7.5 4.4925 7.5H3.7425ZM9 6C8.5875 6 8.25 6.3375 8.25 6.75C8.25 7.1625 8.5875 7.5 9 7.5H14.2463C15.0788 7.5 15.75 8.17125 15.75 9C15.75 9.82875 15.0788 10.5 14.2463 10.5H13.5262C13.1137 10.5 12.7762 10.8375 12.7762 11.25C12.7762 11.6625 13.1137 12 13.5262 12H14.2463C15.9038 12 17.25 10.6575 17.25 9C17.25 7.3425 15.9038 6 14.2463 6H9Z" fill="#129BFE"></path></g><defs><clipPath id="clip0_928_19457"><rect width="18" height="18" fill="white"></rect></clipPath></defs></svg>',1),Sy=e("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 18 18",fill:"none",class:"flex-shrink-0"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M13.0688 5.33625C13.0313 5.25 12.9788 5.1675 12.9075 5.09625C12.9075 5.09625 12.9075 5.09625 12.9037 5.0925C12.7687 4.9575 12.5813 4.875 12.375 4.875H5.625C5.2125 4.875 4.875 5.2125 4.875 5.625C4.875 6.0375 5.2125 6.375 5.625 6.375H10.5638L5.47125 11.4713C5.17875 11.7638 5.17875 12.24 5.47125 12.5325C5.76375 12.825 6.24 12.825 6.5325 12.5325L11.6288 7.43625V12.375C11.6288 12.7875 11.9663 13.125 12.3788 13.125C12.7913 13.125 13.1288 12.7875 13.1288 12.375V5.625C13.1288 5.52 13.1063 5.42625 13.0725 5.33625H13.0688Z",fill:"#129BFE"})],-1),Iy={class:"w-8 h-8 rounded-full bg-gradient-to-br from-[#5F6C90] from-[12.46%] to-[#393D49] to-[85.12%] ml-[10px]"},By={class:"w-full h-full flex justify-center leading-7 text-white text-base font-medium"},Ty={class:"text-[14px] text-[#343A3F] font-medium"},My=e("path",{d:"M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"},null,-1),Dy=[My],Vy={key:1,class:"absolute top-full right-0 mt-1 bg-white shadow-lg rounded-xl w-[240px] z-[100] px-3 pb-2 border border-[#EAEAEA] text-[#343A3F]"},Ey={class:"flex flex-nowrap items-center justify-between p-2",style:{"border-bottom":"1px solid #e5e5e5"}},Ly={class:"text-[#343a3f] text-sm truncate text-text-primary font-medium"},zy={class:"w-8 h-8 rounded-full bg-gradient-to-br from-[#5F6C90] from-[12.46%] to-[#393D49] to-[85.12%] ml-[10px] flex-shrink-0"},Fy={class:"w-full h-full flex justify-center leading-7 text-white text-base font-medium"},Uy=e("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M6.83892 0.606611C6.94569 0.575451 7.05915 0.575466 7.16591 0.606653L12.4134 2.13955C12.6622 2.21222 12.8332 2.44031 12.8332 2.69948V5.84308C12.8332 9.42479 10.5319 12.2716 7.185 13.3868C7.06531 13.4267 6.93593 13.4267 6.81625 13.3868C3.46851 12.2717 1.1665 9.42416 1.1665 5.84162V2.69948C1.1665 2.44026 1.33757 2.21213 1.58641 2.13951L6.83892 0.606611ZM2.33317 3.13691V5.84162C2.33317 8.8049 4.18075 11.2027 7.00057 12.2161C9.81957 11.2027 11.6665 8.80556 11.6665 5.84308V3.13679L7.00227 1.77428L2.33317 3.13691ZM6.99984 4.37492C6.51659 4.37492 6.12484 4.76667 6.12484 5.24992C6.12484 5.73317 6.51659 6.12492 6.99984 6.12492C7.48309 6.12492 7.87484 5.73317 7.87484 5.24992C7.87484 4.76667 7.48309 4.37492 6.99984 4.37492ZM4.95817 5.24992C4.95817 4.12234 5.87226 3.20825 6.99984 3.20825C8.12742 3.20825 9.0415 4.12234 9.0415 5.24992C9.0415 5.75485 8.85821 6.21697 8.55452 6.57337C9.37291 7.08993 9.9165 8.00227 9.9165 9.04159C9.9165 9.36375 9.65534 9.62492 9.33317 9.62492C9.011 9.62492 8.74984 9.36375 8.74984 9.04159C8.74984 8.07508 7.96634 7.29159 6.99984 7.29159C6.03333 7.29159 5.24984 8.07508 5.24984 9.04159C5.24984 9.36375 4.98867 9.62492 4.6665 9.62492C4.34434 9.62492 4.08317 9.36375 4.08317 9.04159C4.08317 8.00227 4.62677 7.08993 5.44515 6.57337C5.14147 6.21697 4.95817 5.75485 4.95817 5.24992Z",fill:"#697077"})],-1),Py={class:"ml-2"},Ry=e("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M0.583496 6.99992C0.583496 3.45617 3.45641 0.583252 7.00016 0.583252C10.5439 0.583252 13.4168 3.45617 13.4168 6.99992C13.4168 10.5437 10.5439 13.4166 7.00016 13.4166C3.45641 13.4166 0.583496 10.5437 0.583496 6.99992ZM11.1243 10.2491C11.7164 9.4995 12.1072 8.58367 12.2181 7.58325H9.90516C9.88183 8.20159 9.82058 8.7995 9.72725 9.35659C10.2318 9.59284 10.7014 9.89617 11.1243 10.2491ZM10.2989 11.0833C10.0393 10.8733 9.75933 10.6895 9.46475 10.5291C9.38308 10.8178 9.29266 11.0891 9.19058 11.3399C9.11475 11.5266 9.036 11.7045 8.9485 11.8737C9.4385 11.6783 9.8935 11.407 10.2989 11.0803V11.0833ZM8.61016 8.95409C8.67433 8.52242 8.71808 8.0645 8.7385 7.58325H5.26183C5.28225 8.0645 5.326 8.52242 5.39016 8.95409C5.9035 8.81992 6.44308 8.74992 7.00016 8.74992C7.55725 8.74992 8.09683 8.81992 8.61016 8.95409ZM8.37391 10.0974C7.93641 9.97784 7.47558 9.91659 7.00016 9.91659C6.52475 9.91659 6.06391 9.98075 5.62641 10.0974C5.70516 10.392 5.79558 10.6633 5.89183 10.9083C6.08725 11.3953 6.30308 11.7512 6.516 11.9728C6.726 12.1945 6.88933 12.2499 7.00016 12.2499C7.111 12.2499 7.27433 12.1974 7.48433 11.9728C7.69725 11.7483 7.91308 11.3953 8.1085 10.9083C8.20766 10.6633 8.29516 10.392 8.37391 10.0974ZM4.27308 9.35659C4.17975 8.7995 4.1185 8.20159 4.09516 7.58325H1.78225C1.89308 8.58367 2.28391 9.4995 2.876 10.2491C3.29891 9.89325 3.7685 9.59284 4.27308 9.35659ZM3.70141 11.0833C3.961 10.8733 4.241 10.6895 4.53558 10.5291C4.61725 10.8178 4.70766 11.0891 4.80975 11.3399C4.88558 11.5266 4.96433 11.7045 5.05183 11.8737C4.55891 11.6783 4.10683 11.407 3.70141 11.0803V11.0833ZM9.90516 6.41659H12.2181C12.1072 5.41617 11.7164 4.50034 11.1243 3.75075C10.7014 4.10659 10.2318 4.407 9.72725 4.64325C9.82058 5.20034 9.88183 5.79825 9.90516 6.41659ZM10.3018 2.91659C10.0422 3.12659 9.76225 3.31034 9.46766 3.47075C9.386 3.182 9.29558 2.91075 9.1935 2.65992C9.11766 2.47325 9.03891 2.29534 8.95141 2.12617C9.44433 2.32159 9.89641 2.59284 10.3018 2.9195V2.91659ZM7.00016 4.08325C7.47558 4.08325 7.93641 4.01909 8.37391 3.90242C8.29516 3.60784 8.20475 3.33659 8.1085 3.09159C7.91308 2.6045 7.69725 2.24867 7.48433 2.027C7.27433 1.80534 7.111 1.74992 7.00016 1.74992C6.88933 1.74992 6.726 1.80242 6.516 2.027C6.30308 2.25159 6.08725 2.6045 5.89183 3.09159C5.79266 3.33659 5.70516 3.60784 5.62641 3.90242C6.06391 4.022 6.52475 4.08325 7.00016 4.08325ZM7.00016 5.24992C6.44308 5.24992 5.9035 5.17992 5.39016 5.04575C5.326 5.47742 5.28225 5.93534 5.26183 6.41659H8.7385C8.71808 5.93534 8.67433 5.47742 8.61016 5.04575C8.09683 5.17992 7.55725 5.24992 7.00016 5.24992ZM4.80975 2.65992C4.70766 2.91075 4.61725 3.182 4.53558 3.47075C4.23808 3.31325 3.961 3.12659 3.70141 2.91659C4.10683 2.58992 4.56183 2.31867 5.05183 2.12325C4.96433 2.29242 4.88266 2.47034 4.80975 2.657V2.65992ZM4.27308 4.64325C3.7685 4.407 3.29891 4.10367 2.876 3.75075C2.28391 4.50034 1.89308 5.41617 1.78225 6.41659H4.09516C4.1185 5.79825 4.17975 5.20034 4.27308 4.64325Z",fill:"#697077"})],-1),Ny={class:"ml-2"},qy={class:"flex items-center"},Ky=e("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.04167 1.3125C1.39708 1.3125 0.875 1.83458 0.875 2.47917V9.1875C0.875 9.83208 1.39708 10.3542 2.04167 10.3542H11.9583C12.6029 10.3542 13.125 9.83208 13.125 9.1875V2.47917C13.125 1.83458 12.6029 1.3125 11.9583 1.3125H2.04167ZM9.33333 11.5208H4.08333C3.7625 11.5208 3.5 11.7833 3.5 12.1042C3.5 12.425 3.7625 12.6875 4.08333 12.6875H9.91667C10.2375 12.6875 10.5 12.425 10.5 12.1042C10.5 11.7833 10.2375 11.5208 9.91667 11.5208H9.33333ZM2.04167 9.1875H11.9583V2.47917H2.04167V9.1875ZM7.95375 2.94583C8.27458 2.94583 8.53708 3.20833 8.53708 3.52917V5.27917C8.53708 5.6 8.27458 5.8625 7.95375 5.8625C7.63292 5.8625 7.37042 5.6 7.37042 5.27917V4.9875H3.57875C3.25792 4.9875 2.99542 4.725 2.99542 4.40417C2.99542 4.08333 3.25792 3.82083 3.57875 3.82083H7.37042V3.52917C7.37042 3.20833 7.63292 2.94583 7.95375 2.94583ZM9.12042 4.40417C9.12042 4.08333 9.38292 3.82083 9.70375 3.82083H10.5788C10.8996 3.82083 11.1621 4.08333 11.1621 4.40417C11.1621 4.725 10.8996 4.9875 10.5788 4.9875H9.70375C9.38292 4.9875 9.12042 4.725 9.12042 4.40417ZM6.20375 5.8625C6.52458 5.8625 6.78708 6.125 6.78708 6.44583V6.7375H10.5788C10.8996 6.7375 11.1621 7 11.1621 7.32083C11.1621 7.64167 10.8996 7.90417 10.5788 7.90417H6.78708V8.19583C6.78708 8.51667 6.52458 8.77917 6.20375 8.77917C5.88292 8.77917 5.62042 8.51667 5.62042 8.19583V6.44583C5.62042 6.125 5.88292 5.8625 6.20375 5.8625ZM2.70375 7.32083C2.70375 7 2.96625 6.7375 3.28708 6.7375H4.45375C4.77458 6.7375 5.03708 7 5.03708 7.32083C5.03708 7.64167 4.77458 7.90417 4.45375 7.90417H3.28708C2.96625 7.90417 2.70375 7.64167 2.70375 7.32083Z",fill:"#697077"})],-1),Hy={class:"ml-2"},Qy=e("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 12 12",fill:"none"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M8.7125 3.5575C8.6875 3.5 8.6525 3.445 8.605 3.3975C8.605 3.3975 8.605 3.3975 8.6025 3.395C8.5125 3.305 8.3875 3.25 8.25 3.25H3.75C3.475 3.25 3.25 3.475 3.25 3.75C3.25 4.025 3.475 4.25 3.75 4.25H7.0425L3.6475 7.6475C3.4525 7.8425 3.4525 8.16 3.6475 8.355C3.8425 8.55 4.16 8.55 4.355 8.355L7.7525 4.9575V8.25C7.7525 8.525 7.9775 8.75 8.2525 8.75C8.5275 8.75 8.7525 8.525 8.7525 8.25V3.75C8.7525 3.68 8.7375 3.6175 8.715 3.5575H8.7125Z",fill:"#697077"})],-1),Oy=e("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.4629 2.46265C3.62346 1.3021 5.22842 0.583252 7.00016 0.583252C8.7719 0.583252 10.3769 1.3021 11.5374 2.46265L11.1249 2.87513L11.5374 2.46265C12.698 3.62321 13.4168 5.22818 13.4168 6.99992C13.4168 8.77166 12.698 10.3766 11.5374 11.5372C10.3769 12.6977 8.77191 13.4166 7.00016 13.4166C5.22842 13.4166 3.62346 12.6977 2.4629 11.5372L2.87537 11.1247L2.46289 11.5372C1.30235 10.3766 0.583496 8.77166 0.583496 6.99992C0.583496 5.22818 1.30235 3.62321 2.46289 2.46265L2.4629 2.46265ZM7.00016 1.74992C5.55027 1.74992 4.23857 2.33691 3.28785 3.28761C2.33714 4.23833 1.75016 5.55003 1.75016 6.99992C1.75016 8.44981 2.33715 9.76152 3.28785 10.7122C4.23857 11.6629 5.55027 12.2499 7.00016 12.2499C8.45006 12.2499 9.76176 11.6629 10.7125 10.7122C11.6632 9.76151 12.2502 8.44981 12.2502 6.99992C12.2502 5.55003 11.6632 4.23833 10.7125 3.28761C9.76176 2.33691 8.45006 1.74992 7.00016 1.74992ZM4.66683 5.43221C4.66683 4.14355 5.7115 3.09888 7.00016 3.09888C8.28883 3.09888 9.3335 4.14355 9.3335 5.43221C9.3335 6.51945 8.58988 7.43301 7.5835 7.69203V8.34888C7.5835 8.67104 7.32233 8.93221 7.00016 8.93221C6.678 8.93221 6.41683 8.67104 6.41683 8.34888V7.18221C6.41683 6.86004 6.678 6.59888 7.00016 6.59888C7.64449 6.59888 8.16683 6.07654 8.16683 5.43221C8.16683 4.78788 7.64449 4.26554 7.00016 4.26554C6.35583 4.26554 5.8335 4.78788 5.8335 5.43221C5.8335 5.75438 5.57233 6.01554 5.25016 6.01554C4.928 6.01554 4.66683 5.75438 4.66683 5.43221ZM7.72933 10.2447C7.72933 10.6474 7.40287 10.9739 7.00016 10.9739C6.59746 10.9739 6.271 10.6474 6.271 10.2447C6.271 9.84201 6.59746 9.51554 7.00016 9.51554C7.40287 9.51554 7.72933 9.84201 7.72933 10.2447Z",fill:"#697077"})],-1),Zy={class:"ml-2"},$y={class:"flex items-center"},Yy=_n('<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14" fill="none"><g clip-path="url(#clip0_2262_73890)"><path fill-rule="evenodd" clip-rule="evenodd" d="M6.99992 0.583496C5.2295 0.583496 3.62242 1.301 2.46159 2.46183C1.30075 3.62266 0.583252 5.22975 0.583252 7.00016C0.583252 8.77058 1.30075 10.3777 2.46159 11.5385C3.62242 12.6993 5.22659 13.4168 6.99992 13.4168C8.77325 13.4168 10.3774 12.6993 11.5383 11.5385C12.6991 10.3777 13.4166 8.7735 13.4166 7.00016C13.4166 5.22683 12.6991 3.62266 11.5383 2.46183C10.3774 1.301 8.77325 0.583496 6.99992 0.583496ZM3.287 3.28725C4.23784 2.33641 5.55034 1.75016 6.99992 1.75016C8.4495 1.75016 9.762 2.33641 10.7128 3.28725C11.6637 4.23808 12.2499 5.55058 12.2499 7.00016C12.2499 8.44975 11.6637 9.76225 10.7128 10.7131C9.762 11.6639 8.4495 12.2502 6.99992 12.2502C5.55034 12.2502 4.23784 11.6639 3.287 10.7131C2.33617 9.76225 1.74992 8.44975 1.74992 7.00016C1.74992 5.55058 2.33617 4.23808 3.287 3.28725ZM7.72909 3.93766C7.72909 3.53516 7.40242 3.2085 6.99992 3.2085C6.59742 3.2085 6.27075 3.53516 6.27075 3.93766C6.27075 4.34016 6.59742 4.66683 6.99992 4.66683C7.40242 4.66683 7.72909 4.34016 7.72909 3.93766ZM5.97909 5.8335C5.97909 5.51266 6.24159 5.25016 6.56242 5.25016H7.14575C7.46659 5.25016 7.72909 5.51266 7.72909 5.8335V9.3335H8.16659C8.48742 9.3335 8.74992 9.596 8.74992 9.91683C8.74992 10.2377 8.48742 10.5002 8.16659 10.5002H6.12492C5.80409 10.5002 5.54159 10.2377 5.54159 9.91683C5.54159 9.596 5.80409 9.3335 6.12492 9.3335H6.56242V6.41683C6.24159 6.41683 5.97909 6.15433 5.97909 5.8335Z" fill="#697077"></path></g><defs><clipPath id="clip0_2262_73890"><rect width="14" height="14" fill="white"></rect></clipPath></defs></svg>',1),jy={class:"ml-2"},Jy={key:0},Gy=e("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",fill:"none"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M1.74984 1.16675C1.42767 1.16675 1.1665 1.42792 1.1665 1.75008V12.2501C1.1665 12.5722 1.42767 12.8334 1.74984 12.8334H6.99984C7.322 12.8334 7.58317 12.5722 7.58317 12.2501C7.58317 11.9279 7.322 11.6667 6.99984 11.6667H2.33317V2.33341H6.99742C7.31958 2.33341 7.58075 2.07225 7.58075 1.75008C7.58075 1.42792 7.31958 1.16675 6.99742 1.16675H1.74984ZM9.21236 3.9626C9.44016 3.7348 9.80951 3.7348 10.0373 3.9626L12.6304 6.55573C12.7546 6.66271 12.8332 6.82108 12.8332 6.9978L12.8332 6.99891C12.8335 7.14859 12.7765 7.29836 12.6623 7.41256L10.0373 10.0376C9.80951 10.2654 9.44016 10.2654 9.21236 10.0376C8.98455 9.80976 8.98455 9.44041 9.21236 9.2126L10.8438 7.58114H4.6665C4.34434 7.58114 4.08317 7.31997 4.08317 6.9978C4.08317 6.67564 4.34434 6.41447 4.6665 6.41447H10.8393L9.21236 4.78756C8.98455 4.55975 8.98455 4.19041 9.21236 3.9626Z",fill:"#697077"})],-1),Wy={class:"ml-2"},jn=ye({__name:"Header",props:{showWorkspaceButton:{type:Boolean,default:!0}},setup(s){const{t:o}=Ss(),l=n(!1),t=()=>{console.log("点击了蒙版")},d=()=>{const J=O=>{const Y=O.data;Y&&Y.from==="niepan-embed"&&(Y.type==="modal-opened"?l.value=!0:Y.type==="modal-closed"?l.value=!1:Y.type==="reload-page"&&window.location.reload())};return window.addEventListener("message",J),()=>{window.removeEventListener("message",J)}};De();const m=Ce();Yn();const u=Bt(),r=n(!1),v=n(null),k=()=>{r.value=!r.value},h=J=>{v.value&&!v.value.contains(J.target)&&(r.value=!1),x.value&&!x.value.contains(J.target)&&(g.value=!1)};ge(()=>{document.addEventListener("click",h);const J=d();Yt(()=>{document.removeEventListener("click",h),J()})});const g=n(!1),x=n(null),y=()=>{g.value=!g.value},w=async()=>{localStorage.removeItem("token"),localStorage.removeItem("setup_status"),localStorage.removeItem("console_token"),localStorage.removeItem("refresh_token"),m.push("/login")},T=()=>{g.value=!1,m.push("/setting/department")},A=n(!1),C=()=>{g.value=!1,A.value=!A.value},L=n(!1),B=()=>{g.value=!1,L.value=!L.value},P=ay(),b=n(!1),M=n(""),D=()=>{g.value=!1,b.value=!b.value},V=ly(),f=et(()=>V.sysConfigsData),I=n(""),U=()=>{I.value&&window.open(I.value,"_blank")};return ge(async()=>{if(await P.fetchVersion(),M.value=P.versionsData.current_version,await V.fetchSysConfigs(),f.value&&f.value.length>0){const J=f.value.find(O=>O.name==="HELP_DOCS_URL");J&&(I.value=J.value)}}),(J,O)=>{var Y,R,j,ee;return a(),c("div",null,[l.value?(a(),c("div",{key:0,class:"fixed inset-0 bg-[#10182899] bg-opacity-50 z-40",onClick:t})):z("",!0),e("div",{class:W(["top-0 left-0 right-0 h-[56px] flex items-center justify-between px-4 border-solid border-0 border-b border-[#EAEAEA]",{"z-30":l.value,"z-[60]":!l.value}])},[e("div",iy,[e("a",cy,[e("img",{src:$n,alt:oe(o)("layout.logo"),class:"h-[24px]"},null,8,ry)]),e("div",{class:"relative",ref_key:"workspaceDropdownRef",ref:v},[e("div",dy,[uy,e("div",{onClick:k,class:"flex items-center text-sm cursor-pointer hover:bg-[#f7f9fc] rounded px-1 py-0.5"},[my,e("div",py,[e("div",vy,S(oe(o)("layout.workspace")),1),(a(),c("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",class:W(["ml-1 transition-transform duration-200",{"rotate-180":r.value}])},fy,2))])])]),r.value&&!l.value?(a(),c("div",_y,[e("div",gy,[e("div",Ay,S(oe(o)("layout.workspace")),1),e("div",yy,[by,e("span",wy,S(oe(o)("layout.workspace")),1)])])])):z("",!0)],512)]),e("div",Cy,[s.showWorkspaceButton?(a(),c("a",ky,[xy,e("span",null,S(oe(o)("layout.goToWorkspace")),1),Sy])):z("",!0),e("div",{class:"flex items-center gap-2 cursor-pointer",onClick:y,ref_key:"userInfoDropdownRef",ref:x},[e("div",Iy,[e("div",By,S(oe(He)(((Y=oe(u).userInfo)==null?void 0:Y.name)||"")),1)]),e("div",Ty,S(((R=oe(u).userInfo)==null?void 0:R.name)||oe(o)("layout.username")),1),(a(),c("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",fill:"currentColor",class:W(["ml-1 transition-transform duration-200",{"rotate-180":g.value}])},Dy,2))],512),g.value?(a(),c("div",Vy,[e("div",Ey,[e("div",Ly,S(((j=oe(u).userInfo)==null?void 0:j.email)||""),1),e("div",zy,[e("div",Fy,S(oe(He)(((ee=oe(u).userInfo)==null?void 0:ee.name)||"")),1)])]),e("div",{class:"p-2 flex items-center text-sm cursor-pointer hover:bg-[#F4F9FD]",onClick:O[0]||(O[0]=K=>B())},[Uy,e("div",Py,S(oe(o)("layout.accountInfo")),1)]),e("div",{class:"p-2 flex items-center text-sm cursor-pointer hover:bg-[#F4F9FD]",onClick:O[1]||(O[1]=K=>C())},[Ry,e("div",Ny,S(oe(o)("layout.languageAndTimezone")),1)]),e("div",{class:"p-2 flex justify-between items-center text-sm cursor-pointer hover:bg-[#F4F9FD]",style:{"border-bottom":"1px solid #e5e5e5"},onClick:O[2]||(O[2]=K=>T())},[e("div",qy,[Ky,e("div",Hy,S(oe(o)("layout.systemManagement")),1)]),Qy]),e("div",{class:"p-2 flex items-center text-sm hover:bg-[#F4F9FD]",onClick:O[3]||(O[3]=K=>U())},[Oy,e("div",Zy,S(oe(o)("layout.help")),1)]),e("div",{class:"p-2 flex items-center text-sm cursor-pointer justify-between hover:bg-[#F4F9FD]",style:{"border-bottom":"1px solid #e5e5e5"},onClick:O[4]||(O[4]=K=>D())},[e("div",$y,[Yy,e("div",jy,S(oe(o)("layout.about")),1)]),M.value?(a(),c("div",Jy,"v "+S(M.value),1)):z("",!0)]),e("div",{class:"p-2 flex items-center text-sm cursor-pointer hover:bg-[#F4F9FD]",onClick:O[5]||(O[5]=K=>w())},[Gy,e("div",Wy,S(oe(o)("layout.logout")),1)])])):z("",!0)])],2),A.value?(a(),X(J9,{key:1,onCancel:()=>A.value=!1},null,8,["onCancel"])):z("",!0),L.value?(a(),X(X9,{key:2,onCancel:()=>L.value=!1},null,8,["onCancel"])):z("",!0),b.value?(a(),X(oy,{key:3,onCancel:()=>b.value=!1},null,8,["onCancel"])):z("",!0)])}}}),Be=s=>(be("data-v-f13fee06"),s=s(),we(),s),Xy={key:0,class:"information-div"},eb={class:"information-top font14"},tb={class:"siteuser-headimg"},ob={class:"overflow-one"},sb={class:"information-center"},nb={key:0,class:"information-item font12"},ab=Be(()=>e("div",null,"部门",-1)),lb={class:"overflow-one"},ib={class:"information-item font12"},cb=Be(()=>e("div",null,"职位",-1)),rb={class:"overflow-one"},db=Be(()=>e("div",{class:"information-item font12"},[e("div",null,"部门"),e("div",{class:"overflow-one"},"暂无部门")],-1)),ub=Be(()=>e("div",{class:"information-item font12"},[e("div",null,"职位"),e("div",{class:"overflow-one"},"暂无岗位")],-1)),mb={class:"information-item information-item1 font12"},pb=Be(()=>e("div",null,"权限",-1)),vb={key:0},hb=Be(()=>e("img",{src:Ds,alt:""},null,-1)),fb={key:1},_b={class:"information-item font12"},gb=Be(()=>e("div",null,"后台",-1)),Ab={key:1},yb={class:"information-item information-item1 font12"},bb=Be(()=>e("div",null,"后台",-1)),wb={key:1},Cb={class:"information-bottom font12"},kb={key:0,class:"fixed top-0 left-0 w-full h-full z-[120] bg-white/30 backdrop-blur-[14px]"},xb={class:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-[30px] w-[420px] p-6",style:{"box-shadow":"0px 10px 44px 1px rgba(0, 0, 0, 0.1)"}},Sb=Be(()=>e("div",{class:"text-xl mb-[24px]"},"功能正在开发中，敬请期待",-1)),Ib={key:0,class:"w-full h-full absolute top-0 left-0 topic-history"},Bb=Be(()=>e("img",{src:Ls,alt:"",class:"w-[26px] relative top-[-2px]"},null,-1)),Tb=[Bb],Mb={class:"w-full"},Db={class:"max-w-[1000px] mx-auto py-[35px]"},Vb=Be(()=>e("div",{class:"text-center font-zhongcu text-2xl mb-[30px]"}," 历史对话 ",-1)),Eb={class:"topic-history-list overflow-y"},Lb=Be(()=>e("div",{class:"text-lg font-zhongcu mb-[15px] font-bold"}," 今天 ",-1)),zb=["onClick"],Fb={class:"topic-item-left"},Ub=Be(()=>e("img",{src:fo,alt:"",class:"topic-item-img"},null,-1)),Pb={class:"topic-item-title truncate"},Rb={class:"topic-item-time"},Nb=["onClick"],qb=Be(()=>e("button",null,[e("img",{src:_o,alt:""})],-1)),Kb=[qb],Hb=Be(()=>e("div",{class:"text-lg font-zhongcu mb-[15px] font-bold"}," 本月 ",-1)),Qb=["onClick"],Ob={class:"topic-item-left"},Zb=Be(()=>e("img",{src:fo,alt:"",class:"topic-item-img"},null,-1)),$b={class:"topic-item-title truncate"},Yb={class:"topic-item-time"},jb=["onClick"],Jb=Be(()=>e("button",null,[e("img",{src:_o,alt:""})],-1)),Gb=[Jb],Wb=Be(()=>e("div",{class:"text-lg font-zhongcu mb-[15px] font-bold"}," 今年 ",-1)),Xb=["onClick"],ew={class:"topic-item-left"},tw=Be(()=>e("img",{src:fo,alt:"",class:"topic-item-img"},null,-1)),ow={class:"topic-item-title truncate"},sw={class:"topic-item-time"},nw=["onClick"],aw=Be(()=>e("button",null,[e("img",{src:_o,alt:""})],-1)),lw=[aw],iw=Be(()=>e("div",{class:"text-lg font-zhongcu mb-[15px] font-bold"}," 以前 ",-1)),cw=["onClick"],rw={class:"topic-item-left"},dw=Be(()=>e("img",{src:fo,alt:"",class:"topic-item-img"},null,-1)),uw={class:"topic-item-title truncate"},mw={class:"topic-item-time"},pw=["onClick"],vw=Be(()=>e("button",null,[e("img",{src:_o,alt:""})],-1)),hw=[vw],fw={key:1,class:"w-full h-full absolute top-0 left-0 topic-history"},_w=Be(()=>e("img",{src:Ls,alt:"",class:"w-[26px] relative top-[-2px]"},null,-1)),gw=[_w],Aw={__name:"AppLayout",setup(s){const o=Ce(),l=De(),t=Bt(),d=n(!1),m=n({}),u=n(!1),{hasAnyPermission:r}=C7();Yt(()=>{d.value=!1,x.value=!1,A.value=!1,m.value={}});const v=K=>{d.value=!d.value,m.value=K,u.value=r(["create_aiserver","del_aiserver","manage_aiserver","create_docbase","manage_docbase","manage_siteuser","create_tool","manage_tool"])},k=n(""),h=n(""),g=n(null),x=n(!1),y=()=>{k.value="退出登录",h.value="退出登录后，数据保留。确定要退出吗？",g.value="logout",x.value=!0,d.value=!1},w=()=>{k.value="",h.value="",x.value=!1,g.value=null},T=async()=>{try{if(x.value=!1,g.value=="del"){const K=await S7(b.value);K.data.error=="0"?(E.success("删除成功"),I()):K.data.message?E.error("删除失败，"+K.data.message):E.error("删除失败")}else if(g.value=="logout"){const K=o.currentRoute.value.path,H=K.startsWith("/chat")?"/index":K;localStorage.setItem("redirectAfterLogin",H),t.clearUserInfo(),localStorage.removeItem("console_token"),await pt(),window.location.href="/login"}}catch(K){console.error("操作失败:",K),E.error("操作失败，请重试")}},A=n(!1),C=n(!1),L=n(null),B=n(!1),P=n(l.query.app_id),b=n(""),M=n({today:[],month:[],year:[],earlier:[]}),D=n(""),V=K=>{C.value=K,K&&I()},f=K=>{b.value=K,k.value="永久删除对话",h.value="本条会话数据将被永久删除，不可恢复及撤销。确定要删除吗？",g.value="del",x.value=!0},I=async()=>{L.value=Re.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});const K=await k7(P.value,P.value?"":"default");M.value=K.data,L.value.close()},U=K=>{if(K){let H=K.split(" ")[0],ne=K.split(" ")[1],re=H.split("-")[1],G=H.split("-")[2];return`${re}-${G} ${ne}`}else return""},J=K=>{B.value=K},O=n(!1),Y=async K=>{const H=l.query._t;C.value=!1;const ne={topic_id:K};P.value&&(ne.app_id=P.value),H&&(ne._t=H),o.replace({path:"Chat",query:ne}).then(()=>{window.location.reload()})},R=n(!1),j=K=>{K.key==="Enter"&&ee()},ee=async()=>{L.value=Re.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});try{const K=await x7(P.value,P.value?"":"default",D.value);M.value=K.data}catch(K){console.error("搜索失败:",K),E.error("搜索失败，请重试")}finally{L.value.close()}};return ge(()=>{l.query.show_history==="1"&&(C.value=!0,I())}),(K,H)=>{const ne=_("router-link"),re=_("v2ConfirmsModal"),G=_("el-header"),ve=_("el-aside"),Z=_("router-view"),se=_("el-input"),$=_("el-main"),ae=_("el-container");return a(),c("div",null,[i(dn,{name:"fade-move"},{default:p(()=>{var te,de;return[d.value?(a(),c("div",Xy,[e("div",eb,[e("div",tb,S((te=m.value)!=null&&te.name?oe(He)(m.value.nickname):""),1),e("div",ob,S((de=m.value)!=null&&de.name?m.value.nickname:""),1)]),e("div",sb,[m.value.department?(a(),c(N,{key:0},[m.value.department.name?(a(),c("div",nb,[ab,e("div",lb,S(m.value.department.name),1)])):z("",!0),e("div",ib,[cb,e("div",rb,S(m.value.department.position_name),1)])],64)):(a(),c(N,{key:1},[db,ub],64)),e("div",mb,[pb,m.value.role=="super"?(a(),c("div",vb,[Q(" 超级管理员 "),hb])):(a(),c("div",fb,"普通用户"))]),e("div",_b,[gb,e("div",null,[u.value?(a(),X(ne,{key:0,to:{name:"QueryAppList"},class:"link"},{default:p(()=>[Q("智能体管理")]),_:1})):(a(),c("div",Ab,"暂无权限"))])]),e("div",yb,[bb,e("div",null,[u.value?(a(),X(ne,{key:0,to:{path:"/admin/knowledge"},class:"link"},{default:p(()=>[Q("知识库管理")]),_:1})):(a(),c("div",wb,"暂无权限"))])])]),e("div",Cb,[e("button",{type:"button",class:"logout-btn",onClick:y}," 退出登录 "),e("button",{class:"link-edit font10",style:{border:"none",background:"none"},onClick:H[0]||(H[0]=he=>{d.value=!1,A.value=!0})}," 修改密码 ")])])):z("",!0)]}),_:1}),i(re,{show:x.value,title:k.value,message:h.value,onClose:w,onConfirm:T},null,8,["show","title","message"]),i(w7,{showModifyPasswordModal:A.value,userInfo:m.value,onCancelModifyPasswordModal:H[1]||(H[1]=te=>A.value=te)},null,8,["showModifyPasswordModal","userInfo"]),O.value?(a(),c("div",kb,[e("div",xb,[Sb,e("button",{class:"text-base rounded-[10px] w-[80px] h-[40px] border border-solid border-[#E5E5E5] bg-transparent float-right",onClick:H[2]||(H[2]=te=>O.value=!1)}," 知道了 ")])])):z("",!0),i(ae,{class:"common-layout",onClick:H[7]||(H[7]=te=>d.value=!1)},{default:p(()=>[i(G,{style:{height:"auto",padding:"0"}},{default:p(()=>[i(jn,{showWorkspaceButton:!1})]),_:1}),i(ae,null,{default:p(()=>[i(ve,{class:W({"el-slide-active1":R.value,"el-slide-active2":!R.value})},{default:p(()=>[i(r7,{onUserinfo:v,onIsShowTopicHistory:V,onIsShowFavorite:J,isShowFavorite:B.value,isShowTopicHistory:C.value,isSlide:R.value,"onUpdate:isSlide":H[3]||(H[3]=te=>R.value=te)},null,8,["isShowFavorite","isShowTopicHistory","isSlide"])]),_:1},8,["class"]),i($,{class:"relative"},{default:p(()=>[i(Z,{class:"w-full h-full"}),C.value?(a(),c("div",Ib,[e("div",{class:"topic-history-closed",onClick:H[4]||(H[4]=te=>C.value=!1)},Tb),e("div",Mb,[e("div",Db,[Vb,i(se,{modelValue:D.value,"onUpdate:modelValue":H[5]||(H[5]=te=>D.value=te),placeholder:"搜索历史记录","prefix-icon":oe(xs),class:"h-[60px] mb-[30px]",style:{"border-radius":"15px",overflow:"hidden"},onKeyup:j},null,8,["modelValue","prefix-icon"]),e("div",Eb,[M.value.today&&M.value.today.length>0?(a(),c(N,{key:0},[Lb,(a(!0),c(N,null,le(M.value.today,te=>(a(),c("div",{class:"topic-item font14",key:te.id,onClick:de=>Y(te.id)},[e("div",Fb,[Ub,e("div",Pb,S(te.title),1)]),e("div",Rb,S(U(te.last_request_time)),1),e("div",{class:"topic-item-del",onClick:_e(de=>f(te.id),["stop"])},Kb,8,Nb)],8,zb))),128))],64)):z("",!0),M.value.month&&M.value.month.length>0?(a(),c(N,{key:1},[Hb,(a(!0),c(N,null,le(M.value.month,te=>(a(),c("div",{class:"topic-item font14",key:te.id,onClick:de=>Y(te.id)},[e("div",Ob,[Zb,e("div",$b,S(te.title),1)]),e("div",Yb,S(U(te.last_request_time)),1),e("div",{class:"topic-item-del",onClick:_e(de=>f(te.id),["stop"])},Gb,8,jb)],8,Qb))),128))],64)):z("",!0),M.value.year&&M.value.year.length>0?(a(),c(N,{key:2},[Wb,(a(!0),c(N,null,le(M.value.year,te=>(a(),c("div",{class:"topic-item font14",key:te.id,onClick:de=>Y(te.id)},[e("div",ew,[tw,e("div",ow,S(te.title),1)]),e("div",sw,S(U(te.last_request_time)),1),e("div",{class:"topic-item-del",onClick:_e(de=>f(te.id),["stop"])},lw,8,nw)],8,Xb))),128))],64)):z("",!0),M.value.earlier&&M.value.earlier.length>0?(a(),c(N,{key:3},[iw,(a(!0),c(N,null,le(M.value.earlier,te=>(a(),c("div",{class:"topic-item font14",key:te.id,onClick:de=>Y(te.id)},[e("div",rw,[dw,e("div",uw,S(te.title),1)]),e("div",mw,S(U(te.last_request_time)),1),e("div",{class:"topic-item-del",onClick:_e(de=>f(te.id),["stop"])},hw,8,pw)],8,cw))),128))],64)):z("",!0)])])])])):z("",!0),B.value?(a(),c("div",fw,[e("div",{class:"topic-history-closed",onClick:H[6]||(H[6]=te=>B.value=!1)},gw),i(v9,{isShowTopicHistory:C.value,onIsShowFavorite:J},null,8,["isShowTopicHistory"])])):z("",!0)]),_:1})]),_:1})]),_:1})])}}},yw=ue(Aw,[["__scopeId","data-v-f13fee06"]]),bw=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 14 14"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.04175 1.74992C2.04175 1.10559 2.56408 0.583252 3.20841 0.583252H11.3751C12.0194 0.583252 12.5417 1.10559 12.5417 1.74992V12.2499C12.5417 12.8943 12.0194 13.4166 11.3751 13.4166H3.20841C2.56409 13.4166 2.04175 12.8943 2.04175 12.2499V1.74992ZM11.3751 1.74992H3.20841V12.2499H11.3751V1.74992Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.375 8.75008C4.375 8.42792 4.63617 8.16675 4.95833 8.16675H9.04167C9.36383 8.16675 9.625 8.42792 9.625 8.75008C9.625 9.07225 9.36383 9.33341 9.04167 9.33341H4.95833C4.63617 9.33341 4.375 9.07225 4.375 8.75008Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.375 10.5001C4.375 10.1779 4.63617 9.91675 4.95833 9.91675H7C7.32217 9.91675 7.58333 10.1779 7.58333 10.5001C7.58333 10.8222 7.32217 11.0834 7 11.0834H4.95833C4.63617 11.0834 4.375 10.8222 4.375 10.5001Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.375 3.50008C4.375 3.17792 4.63617 2.91675 4.95833 2.91675H9.04167C9.36383 2.91675 9.625 3.17792 9.625 3.50008V6.41675C9.625 6.73891 9.36383 7.00008 9.04167 7.00008H4.95833C4.63617 7.00008 4.375 6.73891 4.375 6.41675V3.50008ZM5.54167 4.08341V5.83342H8.45833V4.08341H5.54167Z"})],-1),ww={name:"DocumentIcon"},Cw=ye({...ww,props:{color:{default:"#343A3F"},size:{default:14},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[bw]),_:1},8,["color","size","class"]))}}),kw=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 14 14"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M6.99992 1.74992C4.10042 1.74992 1.74992 4.10042 1.74992 6.99992C1.74992 9.89941 4.10042 12.2499 6.99992 12.2499C9.89941 12.2499 12.2499 9.89941 12.2499 6.99992C12.2499 4.10042 9.89941 1.74992 6.99992 1.74992ZM0.583252 6.99992C0.583252 3.45609 3.45609 0.583252 6.99992 0.583252C10.5437 0.583252 13.4166 3.45609 13.4166 6.99992C13.4166 10.5437 10.5437 13.4166 6.99992 13.4166C3.45609 13.4166 0.583252 10.5437 0.583252 6.99992Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7.00008 10.2083C7.32225 10.2083 7.58341 10.4694 7.58341 10.7916V12.8333C7.58341 13.1554 7.32225 13.4166 7.00008 13.4166C6.67792 13.4166 6.41675 13.1554 6.41675 12.8333V10.7916C6.41675 10.4694 6.67792 10.2083 7.00008 10.2083Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M9.91675 7.00008C9.91675 6.67792 10.1779 6.41675 10.5001 6.41675H12.8334C13.1556 6.41675 13.4167 6.67792 13.4167 7.00008C13.4167 7.32225 13.1556 7.58341 12.8334 7.58341H10.5001C10.1779 7.58341 9.91675 7.32225 9.91675 7.00008Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M0.583252 7.00008C0.583252 6.67792 0.844419 6.41675 1.16659 6.41675H3.20825C3.53042 6.41675 3.79159 6.67792 3.79159 7.00008C3.79159 7.32225 3.53042 7.58341 3.20825 7.58341H1.16659C0.844419 7.58341 0.583252 7.32225 0.583252 7.00008Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7.00008 0.583252C7.32225 0.583252 7.58341 0.844419 7.58341 1.16659V3.20825C7.58341 3.53042 7.32225 3.79159 7.00008 3.79159C6.67792 3.79159 6.41675 3.53042 6.41675 3.20825V1.16659C6.41675 0.844419 6.67792 0.583252 7.00008 0.583252Z"})],-1),xw={name:"TestIcon"},Sw=ye({...xw,props:{color:{default:"#343A3F"},size:{default:14},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[kw]),_:1},8,["color","size","class"]))}}),Iw=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 14 14"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M6.58768 0.754106C6.81549 0.5263 7.18484 0.5263 7.41264 0.754106L8.99179 2.33325H11.0835C11.4057 2.33325 11.6668 2.59442 11.6668 2.91659V5.00829L13.246 6.58744C13.4738 6.81525 13.4738 7.18459 13.246 7.4124L11.6668 8.99154V11.0833C11.6668 11.4054 11.4057 11.6666 11.0835 11.6666H8.99179L7.41264 13.2457C7.18484 13.4735 6.81549 13.4735 6.58768 13.2457L5.00854 11.6666H2.91683C2.59466 11.6666 2.3335 11.4054 2.3335 11.0833V8.99154L0.75435 7.4124C0.526545 7.18459 0.526545 6.81525 0.75435 6.58744L2.3335 5.00829V2.91659C2.3335 2.59442 2.59466 2.33325 2.91683 2.33325H5.00854L6.58768 0.754106ZM7.00016 1.99154L5.66264 3.32906C5.55325 3.43846 5.40487 3.49992 5.25016 3.49992H3.50016V5.24992C3.50016 5.40463 3.4387 5.553 3.32931 5.6624L1.99179 6.99992L3.32931 8.33744C3.4387 8.44684 3.50016 8.59521 3.50016 8.74992V10.4999H5.25016C5.40487 10.4999 5.55325 10.5614 5.66264 10.6708L7.00016 12.0083L8.33768 10.6708C8.44708 10.5614 8.59545 10.4999 8.75016 10.4999H10.5002V8.74992C10.5002 8.59521 10.5616 8.44684 10.671 8.33744L12.0085 6.99992L10.671 5.6624C10.5616 5.553 10.5002 5.40463 10.5002 5.24992V3.49992H8.75016C8.59545 3.49992 8.44708 3.43846 8.33768 3.32906L7.00016 1.99154Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M5.34992 5.35017C5.78751 4.91258 6.381 4.66675 6.99984 4.66675C7.61868 4.66675 8.21217 4.91258 8.64975 5.35017C9.08734 5.78775 9.33317 6.38124 9.33317 7.00008C9.33317 7.61892 9.08734 8.21241 8.64975 8.65C8.21217 9.08758 7.61868 9.33342 6.99984 9.33342C6.381 9.33342 5.78751 9.08758 5.34992 8.65C4.91234 8.21241 4.6665 7.61892 4.6665 7.00008C4.6665 6.38124 4.91234 5.78775 5.34992 5.35017ZM6.99984 5.83341C6.69042 5.83341 6.39367 5.95633 6.17488 6.17512C5.95609 6.39392 5.83317 6.69066 5.83317 7.00008C5.83317 7.3095 5.95609 7.60625 6.17488 7.82504C6.39367 8.04383 6.69042 8.16675 6.99984 8.16675C7.30926 8.16675 7.606 8.04383 7.8248 7.82504C8.04359 7.60625 8.1665 7.3095 8.1665 7.00008C8.1665 6.69066 8.04359 6.39392 7.8248 6.17512C7.606 5.95633 7.30926 5.83341 6.99984 5.83341Z"})],-1),Bw={name:"SettingIcon"},Tw=ye({...Bw,props:{color:{default:"#343A3F"},size:{default:14},className:{default:""}},setup(s){return(o,l)=>(a(),X(Qe,{color:o.color,size:o.size,class:W(o.className)},{default:p(()=>[Iw]),_:1},8,["color","size","class"]))}}),Jn=vo("menu",()=>{const s=n([{key:"home",label:"首页",path:"/index"},{key:"knowledge",label:"知识库",path:"/admin/knowledge",children:[{key:"doc_list",label:"文档",path:"/admin/knowledge/detail/:id/docs",icon:at(Cw)},{key:"test",label:"召回测试",path:"/admin/knowledge/detail/:id/test",icon:at(Sw)},{key:"settings",label:"设置",path:"/admin/knowledge/detail/:id/settings",icon:at(Tw)}]},{key:"application",label:"应用",path:"/admin/application",developing:!0},{key:"model",label:"模型管理",path:"/admin/model"}]),o=n("home"),l=n(""),t=n(""),d=n(null),m=et(()=>{var C;const A=s.value.find(L=>L.key===o.value);return!!((C=A==null?void 0:A.children)!=null&&C.length&&l.value)}),u=et(()=>{const A=s.value.find(C=>C.key===o.value);return(A==null?void 0:A.children)||[]}),r=A=>{o.value=A},v=A=>{l.value=A},k=A=>{t.value=A},h=A=>{d.value=A,t.value=(A==null?void 0:A.title)||""},g=()=>{t.value="",d.value=null},x=n(0),y=n(0);return{menuItems:s,activeMenu:o,activeSubMenu:l,currentKnowledgeTitle:t,currentKnowledge:d,showSubMenu:m,currentSubMenus:u,setActiveMenu:r,setActiveSubMenu:v,setCurrentKnowledgeTitle:k,setCurrentKnowledge:h,clearCurrentKnowledgeTitle:g,knowledgeListScrollPosition:x,setKnowledgeListScrollPosition:A=>{x.value=A},applicationListScrollPosition:y,setApplicationListScrollPosition:A=>{y.value=A}}}),Mw={class:"mb-6"},Dw={key:0,class:"flex items-start space-x-4 animate-pulse"},Vw=e("div",{class:"w-[46px] h-[46px] rounded-full bg-gray-200"},null,-1),Ew=e("div",{class:"flex-1 min-w-0 !ml-[14px]"},[e("div",{class:"h-[18px] bg-gray-200 rounded w-1/3 mb-3"}),e("div",{class:"h-[16px] bg-gray-200 rounded w-1/4"})],-1),Lw=[Vw,Ew],zw={key:1,class:"flex items-start space-x-4"},Fw={class:"w-[46px] h-[46px] rounded-full flex items-center justify-center text-white text-xl font-medium",style:{background:"linear-gradient(270deg, #ffcd02 0%, #ffb101 100%)"}},Uw={class:"flex-1 min-w-0 !ml-[14px]"},Pw={class:"text-[14px] mb-3 leading-[18px] font-semibold text-gray-900 break-words"},Rw={class:"text-[12px] leading-[16px] text-[#A2A9B0]"},Nw={name:"KnowledgeSubTitle"},qw=ye({...Nw,setup(s){const o=De(),l=Jn(),t=n(null),d=n(!0),m=async u=>{d.value=!0;try{const r=await jt(u);r.data.error==="0"&&(t.value={id:r.data.id,title:r.data.title,author:r.data.author},l.setCurrentKnowledgeTitle(r.data.title))}catch(r){console.error("获取知识库详情失败:",r)}finally{d.value=!1}};return Pe(()=>o.params.id,u=>{u&&m(u)},{immediate:!0}),ge(()=>{o.params.id&&m(o.params.id)}),(u,r)=>{var v,k;return a(),c("div",Mw,[d.value?(a(),c("div",Dw,Lw)):t.value?(a(),c("div",zw,[e("div",Fw,S((v=t.value.title)==null?void 0:v.charAt(0)),1),e("div",Uw,[e("div",Pw,S(t.value.title),1),e("p",Rw," 创建者："+S(((k=t.value.author)==null?void 0:k.name)||"-"),1)])])):z("",!0)])}}}),Kw={class:"flex flex-col min-h-screen px-[36px] pt-[24px] pb-[24px] bg-[linear-gradient(129deg,#f5fbff_11.03%,#fcfcfc_88.98%)]"},Hw={class:"flex items-center h-12 mb-12 relative z-1 justify-between"},Qw=e("div",null,[e("img",{src:$n,alt:"Logo",class:"w-[102px]"})],-1),Ow={class:"flex mx-auto rounded-lg bg-[#F2F4F8] p-1"},Zw=["onClick"],$w=["title"],Yw={class:"flex-1 relative z-0"},jw={key:0,class:"w-[240px] bg-white rounded-[20px] p-6 pb-0 sticky top-6 flex flex-col h-[calc(100vh-146px)]"},Jw=e("div",{class:"h-[1px] bg-[#E5E5E5] my-4"},null,-1),Gw={class:"flex-1 flex flex-col"},Ww=ye({__name:"AdminLayout",setup(s){const o=De(),l=Ce(),t=Jn(),d=Bt(),{showTip:m}=qo(),u=v=>{if(v.developing){m();return}l.push(v.path)},r=()=>{var v,k;(v=o.meta)!=null&&v.activeMenu&&t.setActiveMenu(o.meta.activeMenu),(k=o.meta)!=null&&k.activeSubMenu&&t.setActiveSubMenu(o.meta.activeSubMenu)};return Pe(()=>o.path,()=>{try{r(),o.path.includes("/knowledge/detail/")||(t.clearCurrentKnowledgeTitle(),t.setActiveSubMenu(""))}catch(v){console.error("Error updating active menu:",v)}},{immediate:!0}),ge(()=>{try{r()}catch(v){console.error("Error in onMounted:",v)}}),(v,k)=>{var x,y;const h=_("router-link"),g=_("router-view");return a(),c("div",Kw,[e("header",Hw,[Qw,e("nav",Ow,[(a(!0),c(N,null,le(oe(t).menuItems,w=>(a(),c("a",{key:w.key,class:W([{"rounded-lg bg-[#129BFE] !text-white hover:text-white":oe(t).activeMenu===w.key},"flex justify-center items-center gap-1 px-6 py-[9px] text-[#343A3F] no-underline text-[14px] hover:text-[#8c9092] whitespace-nowrap cursor-pointer"]),onClick:T=>u(w)},S(w.label),11,Zw))),128))]),e("div",null,[e("div",{title:(x=oe(d).userInfo)==null?void 0:x.name,class:"w-[36px] h-[36px] rounded-[50.143px] bg-gradient-to-br from-[#5F6C90] from-[12.46%] to-[#393D49] to-[85.12%] text-white flex items-center justify-center text-[18px] font-weight-[500] font-['PingFang_SC'] leading-[23.143px]"},S(oe(He)(((y=oe(d).userInfo)==null?void 0:y.name)||"")),9,$w)])]),e("main",Yw,[e("div",{class:W(["mx-auto flex",oe(o).meta.hideSubMenu||oe(t).showSubMenu?"w-full":"w-[calc(100%-360px)]"])},[oe(t).showSubMenu&&!oe(o).meta.hideSubMenu?(a(),c("div",jw,[oe(t).activeMenu==="knowledge"?(a(),X(qw,{key:0})):z("",!0),Jw,e("nav",Gw,[(a(!0),c(N,null,le(oe(t).currentSubMenus,w=>(a(),X(h,{key:w.key,to:w.path.replace(":id",String(v.$route.params.id)),class:W(["flex items-center h-[42px] px-4 rounded-lg text-[14px] font-['PingFang_SC'] font-medium leading-[22px] transition-colors duration-200",{"bg-[#EBF7FF] text-[#129BFE]":oe(t).activeSubMenu===w.key,"text-[#343A3F] hover:bg-[#F5F5F5]":oe(t).activeSubMenu!==w.key}])},{default:p(()=>[w.icon?(a(),X(Ro(w.icon),{key:0,color:oe(t).activeSubMenu===w.key?"#129BFE":"#343A3F",class:"w-4 h-4 mr-2"},null,8,["color"])):z("",!0),Q(" "+S(w.label),1)]),_:2},1032,["to","class"]))),128))])])):z("",!0),e("div",{class:W(["transition-all duration-300 bg-transparent",oe(o).meta.hideSubMenu?"w-full":oe(t).showSubMenu?"w-[calc(100%-264px)] ml-6":"w-full"])},[i(g,null,{default:p(({Component:w})=>[(a(),X(ka,null,[(a(),X(Ro(w),{key:v.$route.fullPath}))],1024))]),_:1})],2)],2)])])}}}),Xw={class:"flex flex-col h-screen v3-gradient-bg relative"},eC={class:"flex flex-1 overflow-hidden"},tC={class:"w-[240px] bg-white h-full flex flex-col"},oC={class:"flex-1 overflow-y-auto pt-[8px] pb-[20px] px-[16px]"},sC={class:"space-y-1"},nC={key:0,class:"text-[12px] leading-4 font-medium text-[#343A3F] py-3 mt-2"},aC=["onClick"],lC={class:"flex-1 border-0 overflow-auto px-6 pt-[16px] pb-0 bg-transparent relative"},iC=ye({__name:"AdminLayout",setup(s){const{t:o}=Ss(),l=De(),t=Ce(),d=Yn(),m=Bt(),{showTip:u}=qo(),r=et(()=>{var x;const h=m.userInfo,g=((x=h==null?void 0:h.roles)==null?void 0:x.map(y=>y.type))||[];return d.menuItems.filter(y=>y.isTitle?!0:!(y.roles&&y.roles.length>0&&!y.roles.some(T=>g.includes(T))))}),v=()=>{var h;(h=l.meta)!=null&&h.activeMenu&&d.setActiveMenu(l.meta.activeMenu)};ge(()=>{try{v()}catch(h){console.error("Error in onMounted:",h)}});const k=h=>{if(h.developing){u();return}t.push(h.path)};return Pe(()=>l.path,()=>{try{v()}catch(h){console.error("Error updating active menu:",h)}},{immediate:!0}),(h,g)=>{const x=_("router-view");return a(),c("div",Xw,[i(jn,{showWorkspaceButton:!0}),e("div",eC,[e("aside",tC,[e("nav",oC,[e("div",sC,[(a(!0),c(N,null,le(r.value,y=>(a(),c(N,{key:y.key},[y.isTitle?(a(),c("div",nC,S(y.label),1)):(a(),c("a",{key:1,class:W(["flex items-center h-[42px] px-6 text-[14px] leading-[22px] font-medium transition-colors duration-200 rounded-md cursor-pointer",oe(d).activeMenu===y.key?"bg-[#EBF7FF] text-[#129BFE]":"text-[#343A3F] hover:bg-[#F5F5F5]"]),onClick:w=>k(y)},[y.icon?(a(),X(Ro(y.icon),{key:0,color:oe(d).activeMenu===y.key?"#129BFE":"#343A3F",class:"w-5 h-5 mr-3"},null,8,["color"])):z("",!0),Q(" "+S(y.label),1)],10,aC))],64))),128))])])]),e("div",lC,[i(x,null,{default:p(({Component:y})=>[(a(),X(Ro(y),{key:h.$route.fullPath}))]),_:1})])])])}}});class cC{constructor(){this.timers=[]}addTimer(o){this.timers.push(o)}clearAllTimers(){this.timers.forEach(o=>clearTimeout(o)),this.timers.forEach(o=>clearInterval(o)),this.timers=[]}}const rC=new cC,Mk=s=>{try{const l=new TextEncoder().encode(s),t=Array.from(l).map(d=>String.fromCharCode(d)).join("");return btoa(t)}catch(o){return console.error("UTF-8转Base64编码失败:",o),""}},Dk=s=>{try{const o=atob(s),l=new Uint8Array(o.length);for(let d=0;d<o.length;d++)l[d]=o.charCodeAt(d);return new TextDecoder().decode(l)}catch(o){return console.error("Base64转UTF-8解码失败:",o),""}},Vk=s=>{const o=btoa(encodeURIComponent(s));return encodeURIComponent(o)},dC=s=>{try{const o=decodeURIComponent(s);return decodeURIComponent(atob(o))}catch(o){return console.error("解码失败:",o),s}},uC=[{path:"/",name:"Login",component:on,meta:{title:"登录-INTELLIDO"}},{path:"/login",name:"LoginQuery",component:on,props:s=>({sso:s.query.sso,ssourl:s.query.ssourl}),meta:{title:"登录-INTELLIDO"}},{path:"/worker",name:"Worker",component:sn,meta:{requiresAuth:!0,title:"数字员工-INTELLIDO"}},{path:"/worker",name:"IndexWithQid",component:sn,meta:{requiresAuth:!0,title:"数字员工-INTELLIDO"},props:s=>({qid:s.query.qid})},{path:"/kbase-list",name:"KBaseList",component:S1,meta:{requiresAuth:!0}},{path:"/doc-list/:kid",name:"DocList",component:Em,meta:{requiresAuth:!0}},{path:"/queryapp-list",name:"QueryAppList",component:lm,meta:{requiresAuth:!0}},{path:"/doc-add-file/:kid/:dcid?",name:"DocAddFile",component:Qp,meta:{requiresAuth:!0}},{path:"/queryapp-detail/:qid",name:"QueryappDetail",component:tf,meta:{requiresAuth:!0}},{path:"/siteuser-list/",name:"SiteuserList",component:Df,meta:{requiresAuth:!0}},{path:"/department-list/",name:"DepartmentList",component:o2,meta:{requiresAuth:!0}},{path:"/organization/",name:"Organization",component:S2,meta:{requiresAuth:!0}},{path:"/siteuser-add/:sid?",name:"SiteuserAdd",component:_0,meta:{requiresAuth:!0}},{path:"/department-add/:did?",name:"DepartmentAdd",component:L0,meta:{requiresAuth:!0}},{path:"/department-position-list/:did",name:"DepartmentPositionList",component:A5,meta:{requiresAuth:!0}},{path:"/siteuser-position-list/:sid",name:"SiteuserPositionList",component:i_,meta:{requiresAuth:!0}},{path:"/docfragment-list/:kid/:dcid",name:"DocFragmentLIst",component:O_,meta:{requiresAuth:!0}},{path:"/tool-list/",name:"ToolList",component:zg,meta:{requiresAuth:!0}},{path:"/tool-edit/:tid?",name:"ToolEdit",component:o3,meta:{requiresAuth:!0}},{path:"/tool-setting/:tid",name:"ToolSetting",component:w3,meta:{requiresAuth:!0}},{path:"/tool-test/:tid",name:"ToolTest",component:q3,meta:{requiresAuth:!0}},{path:"/test",name:"Test",component:K3,meta:{requiresAuth:!0}},{path:"/doc-add-text/:kid",name:"DocAddText",component:VA,meta:{requiresAuth:!0}},{path:"/test-doc",name:"TestDoc",component:f6,meta:{requiresAuth:!0}},{path:"/aios",name:"AIOSIndex",component:F8,meta:{requiresAuth:!1}},{path:"/filetemplate-list",name:"FileTemplateList",component:Z8,meta:{requiresAuth:!0}},{path:"/filetemplate-add/:ftid?",name:"FileTemplateAdd",component:l4,meta:{requiresAuth:!0}},{path:"/fema",name:"FeiMaIndex",component:H4},{path:"/",component:yw,children:[{path:"/index",name:"Index",component:()=>Ie(()=>import("./index-DqLOvDaQ.js"),__vite__mapDeps([0,1,2,3,4,5,6])),meta:{requiresAuth:!0,title:"INTELLIDO"}},{path:"/chat/:token?",name:"Chat",component:()=>Ie(()=>import("./ChatView-BP1WmwpV.js").then(s=>s.C),__vite__mapDeps([7,1,2,8])),meta:{requiresAuth:!0,title:"应用-INTELLIDO"},props:s=>({question:s.query.question})},{path:"/apps",name:"Apps",component:()=>Ie(()=>import("./index-DqLOvDaQ.js"),__vite__mapDeps([0,1,2,3,4,5,6])),meta:{requiresAuth:!0,title:"Apps-INTELLIDO"}}]},{path:"/app-chat/:appId?",name:"AppChat",component:()=>Ie(()=>import("./index-xhSOFnTC.js"),__vite__mapDeps([9,1,2,4,5,3,10])),meta:{requiresAuth:!0,title:"应用-INTELLIDO"}},{path:"/file-preview",name:"FilePreview",component:()=>Ie(()=>import("./FilePreviewPage-cEv_AJah.js"),__vite__mapDeps([11,1,2,12])),meta:{requiresAuth:!0,title:"文件预览-INTELLIDO"}},{path:"/appremodel",name:"AppRemodel",component:()=>Ie(()=>import("./index-dqEKp0WT.js"),__vite__mapDeps([13,1,2,14,15]))},{path:"/admin",component:Ww,children:[{path:"home",name:"AdminHome",component:()=>Ie(()=>import("./index-BWudzzWS.js"),__vite__mapDeps([16,1,2,17,18])),meta:{title:"首页",activeMenu:"home"}},{path:"knowledge",name:"Knowledge",component:()=>Ie(()=>import("./index-BR7-Ob6r.js"),__vite__mapDeps([19,1,2,14,20])),meta:{title:"知识库",activeMenu:"knowledge"}},{path:"knowledge/docfragments/:kbaseId/:docId",name:"KnowledgeDocFragments",component:()=>Ie(()=>import("./Docfragments-CKYN7E1b.js"),__vite__mapDeps([21,1,2,22,3,23,14,24])),meta:{title:"文档切片列表",activeMenu:"knowledge"}},{path:"knowledge/detail/:id",name:"KnowledgeDetail",component:()=>Ie(()=>import("./Detail-DkFugHS6.js"),__vite__mapDeps([25,1,2])),meta:{title:"知识库详情",activeMenu:"knowledge"},redirect:s=>({name:"KnowledgeDetailDocs",params:{id:s.params.id}}),children:[{path:"docs",name:"KnowledgeDetailDocs",component:()=>Ie(()=>import("./Docs-CaCa2q0a.js"),__vite__mapDeps([26,1,2,14,23,3,22,27])),meta:{title:"文档列表",activeMenu:"knowledge",activeSubMenu:"doc_list"}},{path:"docfragments/:docId",name:"KnowledgeDetailDocFragments",component:()=>Ie(()=>import("./Docfragments-CKYN7E1b.js"),__vite__mapDeps([21,1,2,22,3,23,14,24])),meta:{title:"文档切片列表",activeMenu:"knowledge",activeSubMenu:"doc_list"}},{path:"test",name:"KnowledgeDetailTest",component:()=>Ie(()=>import("./Test-MMU_raRr.js"),__vite__mapDeps([28,1,2,22,29])),meta:{title:"知识库测试",activeMenu:"knowledge",activeSubMenu:"test"}},{path:"settings",name:"KnowledgeDetailSettings",component:()=>Ie(()=>import("./Settings-C2MV-lE_.js"),__vite__mapDeps([30,1,2,22,31])),meta:{title:"知识库设置",activeMenu:"knowledge",activeSubMenu:"settings"}}]},{path:"application",name:"Application",component:()=>Ie(()=>import("./index-CqJoS3As.js"),__vite__mapDeps([32,1,2,14,33])),meta:{title:"应用",activeMenu:"application"}},{path:"application/detail/:qid",name:"ApplicationDetail",component:()=>Ie(()=>import("./Detail-xIdcdvcs.js"),__vite__mapDeps([34,1,2])),meta:{title:"应用详情",activeMenu:"application"},redirect:s=>({name:"ApplicationDetailSettings",params:{qid:s.params.qid}}),children:[{path:"settings",name:"ApplicationDetailSettings",component:()=>Ie(()=>import("./Settings-Comqx2Ls.js"),__vite__mapDeps([35,1,2,22,36,37])),meta:{title:"应用设置",activeMenu:"application",activeSubMenu:"settings",hideSubMenu:!0}}]},{path:"model",name:"Model",component:()=>Ie(()=>import("./index-dqEKp0WT.js"),__vite__mapDeps([13,1,2,14,15])),meta:{title:"模型管理",activeMenu:"model"}}]},{path:"/setting",component:iC,children:[{path:"department",name:"SettingDepartment",component:()=>Ie(()=>import("./department-DGC4WEUT.js"),__vite__mapDeps([38,1,2,39,36,14,40,41,42])),meta:{title:"部门管理",activeMenu:"department",requiresAuth:!0}},{path:"user",name:"SettingUser",component:()=>Ie(()=>import("./user-CYURvluk.js"),__vite__mapDeps([43,1,2,39,36,44])),meta:{title:"用户管理",activeMenu:"user",requiresAuth:!0}},{path:"role",name:"SettingRole",component:()=>Ie(()=>import("./role-iO_qqXTm.js"),__vite__mapDeps([45,1,2,46])),meta:{title:"角色管理",activeMenu:"role",requiresAuth:!0}},{path:"model-provider",name:"SettingModelProvider",component:()=>Ie(()=>import("./model-provider-CizF2tHO.js"),__vite__mapDeps([47,1,2])),meta:{title:"模型供应商",activeMenu:"model_provider",requiresAuth:!0}},{path:"data-source",name:"SettingDataSource",component:()=>Ie(()=>import("./data-source-B3SkTLgL.js"),__vite__mapDeps([48,1,2])),meta:{title:"数据来源",activeMenu:"data_source",requiresAuth:!0}},{path:"api-extension",name:"SettingApiExtension",component:()=>Ie(()=>import("./api-extension-BnxwXGaq.js"),__vite__mapDeps([49,1,2])),meta:{title:"API扩展",activeMenu:"api_extension",requiresAuth:!0}},{path:"license",name:"SettingLicense",component:()=>Ie(()=>import("./license-YzC5_D8d.js"),__vite__mapDeps([50,40,1,2,41,51])),meta:{title:"授权管理",activeMenu:"license",requiresAuth:!0}},{path:"system-config",name:"SystemConfig",component:()=>Ie(()=>import("./system-config-BWomNM7h.js"),__vite__mapDeps([52,1,2,53])),meta:{title:"设置",activeMenu:"system-config",requiresAuth:!0}}]},{path:"/privacy",name:"Privacy",component:()=>Ie(()=>import("./privacy-Dz6C79G9.js"),__vite__mapDeps([54,1,2,55])),meta:{title:"隐私政策-INTELLIDO",requiresAuth:!1}},{path:"/favorite",name:"Favorite",component:()=>Ie(()=>import("./favorite-_sJSL1Hs.js"),__vite__mapDeps([56,1,2,57])),meta:{title:"收藏夹-INTELLIDO",requiresAuth:!0}}],Qs=xa({history:Sa("/nq/"),routes:uC});Qs.beforeEach((s,o,l)=>{rC.clearAllTimers();const t=s.query._t?dC(s.query._t):null;document.title=t||s.meta.title||"INTELLIDO",s.matched.some(d=>d.meta.requiresAuth)?localStorage.getItem("console_token")?l():(localStorage.setItem("redirectAfterLogin",s.fullPath),l({name:"Login"})):l()});const mC="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZCAYAAADE6YVjAAAAAXNSR0IArs4c6QAAAa1JREFUSEu9lj0vBkEQx38ThESiUEg0Xj6AQuEplELiJUKvJKH0DUSUNDoKnwEhSjoFgtBKhIJE4jVRSMh45uzJOnfn7uFseTs7v53/zOyckLJUtRboAUaAbqAVqAeegQtgF1gHtkXkJcmVxG2oajUwDswAzWkXcXvXwCywIiKvUftvEFVtAdbKN+zM4DxqcgSMisilv/EFoqolYANoqgAQHrkBhkVkL/zwCXER7P8S4IO6wogCiMuBkSuRKClok65kOQohk8DSLyRKOjolIsviyvQ8YxXlvYdVXbtBBsphbeU9HbG/AuqAxhg/gwYxmUyuStcB0Ac0AGdATcRRINcJ0FEhIQCIyIOqDgGbMX5ODfLobhHdN/g8MAe0xRz2Af3AqpMsavpkEHsGqmKcTIvIoqoaYCcCygowt29pkdwDvSJyGAHlARgkiCQtJ3dO8xC0AEy4HKRJ5AsT5OSn6jKQRWQdHCxVzQow86C6svTJrQMd5wQYJOgTG0xZOt4isgoaS6iiuC746HgXfrFv17+9wg5kE7G4eeJVTbGT0QMVO+M9ULF/K34d/tV/1ztVYM9LMFLdDwAAAABJRU5ErkJggg==",pC="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABkAAAAZCAYAAADE6YVjAAAAAXNSR0IArs4c6QAAAdZJREFUSEu9lkFLFlEUhp+XhAIhpE20SfwBLlzoQlu1iYIwUiR050IhQTfSunXQxgqyReuQkKLSnVCLFrkQ9AeIbqSNqCAYKKc50x2ZprnzDeo0mw++c+Y899z3vfeMqHjM7DJwGxgE+oFOoB04BLaA78BHYEXSr1gplQXMrA0YT2JPgRtVCwmxnZD7VtJxMf8fiJndBD4kK+ypUbyYsgY8kLSdD/wFMbNe4BNw/QyA7JWfwH1Jq9kfp5DQwY9zAvKgvqyjFBI0cMBZtijWtG+dg44zyEQi8Pw5tij26qSkNwo23Yy46H1i4W/AM+BKSSW37RNgABgpibvruhxyN2lrKbKUaUkvQs5iAeSAIUlfzOwx8CpS455DXgOTkYQj4KGk5QLIAcOSPpvZnWD5sk697LxD1oHuCj2KoHfAWE2Al91wyD5wtYXoedA1Sbs1OshKHjjEr4FLNZx1kIr4B9ABuFn8t9VzUreTvAYdkvYiZigDpp200qQosmsyWmKGWEepJlXuirko5royUOquqnMyI2kuInIeNAW8rDonPphiJ34B+Ao8j5x4B80Ct4BH0RMfLshm767/dgsHkE/E5uZJtpeNT8YcqNkZnwM1+7WSt+FFfXf9Bsfc9Eu2b7dcAAAAAElFTkSuQmCC",vC={name:"MyModal",props:{successVisible:{type:Boolean,default:!1},errorVisible:{type:Boolean,default:!1}},setup(s){const o=n(s.successVisible),l=n(s.errorVisible);return Pe(()=>s.successVisible,m=>{o.value=m}),Pe(()=>s.errorVisible,m=>{l.value=m}),{localSuccessVisible:o,localErrorVisible:l,showSuccess:()=>{o.value=!0,l.value=!1},showError:()=>{o.value=!1,l.value=!0}}}},Gn=s=>(be("data-v-f192a928"),s=s(),we(),s),hC={key:0,class:"alert-container-success"},fC=Gn(()=>e("img",{src:mC,alt:""},null,-1)),_C={key:1,class:"alert-container-error"},gC=Gn(()=>e("img",{src:pC,alt:""},null,-1));function AC(s,o,l,t,d,m){return a(),c("div",null,[t.localSuccessVisible?(a(),c("div",hC,[fC,e("span",null,[We(s.$slots,"default",{},void 0,!0)])])):z("",!0),t.localErrorVisible?(a(),c("div",_C,[gC,e("span",null,[We(s.$slots,"default",{},void 0,!0)])])):z("",!0)])}const yC=ue(vC,[["render",AC],["__scopeId","data-v-f192a928"]]),bC={install(s){const o=n(!1),l=n(""),t=n(""),d=(v,k)=>{l.value=v,t.value=k,o.value=!0,setTimeout(()=>{o.value=!1},2e3)},m=()=>{o.value=!1},r=gn({setup(){return{modalVisible:o,modalType:l,modalContent:t,closeModal:m}},render(){return this.modalVisible?Ia(yC,{successVisible:this.modalType==="success",errorVisible:this.modalType==="error"},{default:()=>this.modalContent}):null}}).mount(document.createElement("div"));document.body.appendChild(r.$el),s.config.globalProperties.$modal={showSuccess(v){d("success",v)},showError(v){d("error",v)},close(){m()}}}},wC=ye({name:"GlobalModal",components:{},props:{show:{type:Boolean,required:!0},title:{type:String,required:!0},message:{type:String,required:!0}},emits:["close","confirm"],methods:{handleClose(){this.$emit("close")},handleConfirm(){this.$emit("confirm")}}}),CC={key:0,class:"modal-del"},kC={class:"modal-del-container"},xC={class:"modal-del-container-top font-zhongcu font18"},SC={class:"modal-del-container-center font-zhongcu font14"},IC={class:"modal-del-container-bottom font14"};function BC(s,o,l,t,d,m){return s.show?(a(),c("div",CC,[e("div",kC,[e("div",xC,S(s.title),1),e("div",SC,S(s.message),1),e("div",IC,[e("button",{type:"button",onClick:o[0]||(o[0]=_e((...u)=>s.handleClose&&s.handleClose(...u),["stop"])),class:"common-cancel-btn"},"取消"),e("button",{type:"button",onClick:o[1]||(o[1]=_e((...u)=>s.handleConfirm&&s.handleConfirm(...u),["stop"])),class:"common-confirm-btn"},"确认")])])])):z("",!0)}const TC=ue(wC,[["render",BC],["__scopeId","data-v-6004f886"]]),MC=ye({name:"v2Confirms",components:{},props:{show:{type:Boolean,required:!0},title:{type:String,required:!0},message:{type:String,required:!0}},emits:["close","confirm"],methods:{handleClose(){this.$emit("close")},handleConfirm(){this.$emit("confirm")}}}),DC={key:0,class:"modal-del"},VC={class:"modal-del-container"},EC={class:"modal-del-container-top font-zhongcu font20 leading-7 font-medium"},LC={class:"modal-del-container-center font-zhongcu font16"},zC={class:"modal-del-container-bottom font14"};function FC(s,o,l,t,d,m){return s.show?(a(),c("div",DC,[e("div",VC,[e("div",EC,S(s.title),1),e("div",LC,S(s.message),1),e("div",zC,[e("button",{type:"button",onClick:o[0]||(o[0]=_e((...u)=>s.handleClose&&s.handleClose(...u),["stop"])),class:"common-cancel-btn"},"取消"),e("button",{type:"button",onClick:o[1]||(o[1]=_e((...u)=>s.handleConfirm&&s.handleConfirm(...u),["stop"])),class:"common-confirm-btn"},"确认删除")])])])):z("",!0)}const UC=ue(MC,[["render",FC],["__scopeId","data-v-45ff66ac"]]),PC=Ba(),Dt=gn(Ra);Dt.use(Qs);const rn=Is();let ws=Ma;rn==="en-US"?ws=Da:rn==="zh-Hant"&&(ws=Va);Dt.use(Ta,{locale:ws});Dt.use(bC);Dt.use(ja);console.log("this is new query");Dt.component("newComfirmsModal",TC);Dt.component("v2ConfirmsModal",UC);Dt.use(PC);localStorage.getItem("console_token")&&Bt().fetchUserInfo(Qs.currentRoute.value);Dt.mount("#app");export{z7 as $,OC as A,Ee as B,KC as C,ZC as D,HC as E,QC as F,Ga as G,hk as H,fk as I,_k as J,pk as K,Vk as L,Jn as M,Us as N,ze as O,jt as P,el as Q,ol as R,Ms as S,tl as T,xl as U,gl as V,Al as W,WC as X,XC as Y,ek as Z,Qe as _,ue as a,Me as a0,po as a1,Ko as a2,c_ as a3,r_ as a4,d_ as a5,u_ as a6,_l as a7,GC as a8,yl as a9,il as aA,bn as aB,dl as aC,rl as aD,tk as aE,ok as aF,sk as aG,nk as aH,ak as aI,mk as aJ,ck as aK,ik as aL,lk as aM,rk as aN,dk as aO,uk as aP,ly as aQ,Tk as aR,T7 as aS,I7 as aT,yk as aU,Mk as aV,qo as aW,vk as aX,bl as aa,Jt as ab,xn as ac,Po as ad,En as ae,Mi as af,Bl as ag,Bs as ah,Qm as ai,JC as aj,sl as ak,$C as al,YC as am,jC as an,He as ao,al as ap,ll as aq,wn as ar,Es as as,Tt as at,tn as au,yn as av,cl as aw,Dl as ax,Sl as ay,fl as az,xk as b,Ie as c,Bt as d,zn as e,Sk as f,Ln as g,Ak as h,B7 as i,cn as j,kk as k,Q4 as l,bk as m,Ck as n,wk as o,Ik as p,ho as q,zs as r,gk as s,Ni as t,Bk as u,UC as v,Yn as w,lt as x,Dk as y,Ze as z};
