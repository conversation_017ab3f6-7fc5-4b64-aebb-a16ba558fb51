<!-- 赢率60%以上所有商机列表的组件 -->
<template>
  <div
    class="w-[344px] rounded-[22px] bg-white border border-solid border-[#F1F1F1] py-[15px] pl-[20px] pr-[30px]"
  >
    <div class="flex justify-between">
      <div>
        <div class="text-34 leading-10 font-zhongcu">
          {{ formattedData.value }}{{ formattedData.unit }}
        </div>
        <div class="text-xs text-[#3C3C43]">{{ props.data.title }}</div>
      </div>
      <div ref="chartRef" class="w-[70px] h-[70px]"></div>
    </div>
    <div class="mt-[8px] text-xs">
      <div
        v-for="(item, index) in props.data.items || []"
        :key="index"
        :class="[
          'flex justify-between item-center py-[5px] text-[#00080E]',
          {
            'border-0 border-b border-[#ECECF2] border-solid':
              index !== (props.data.items?.length || 0) - 1,
          },
        ]"
      >
        <span class="overflow-one w-[85%]">{{ item.name }}</span>
        <span>{{ phaseToPercentage(item.phase) }}%</span>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, nextTick, watch } from "vue";
import { formatAmount } from "../../utils";
import * as echarts from "echarts";
import type { EChartsType } from "echarts";

export interface Opportunity {
  name: string;
  phase: string;
}

export interface ModelData {
  title: string;
  total_count: number;
  percentage: number;
  items?: Opportunity[];
}

const props = defineProps<{
  data: ModelData;
}>();

// 添加调试日志
// console.log("CenterOpportunityListComponent props:", props.data);

// 使用 computed 来确保 formatAmount 函数的返回值是响应式的
const formattedData = computed(() => {
  if (!props.data.total_count) {
    return { value: 0, unit: "" };
  }
  const { value, unit } = formatAmount(props.data.total_count, false);
  return { value, unit };
});

// 将 phase 转换为百分比
const phaseToPercentage = (phase: string): string => {
  const phases: Record<string, string> = {
    initial: "20",
    requirement: "40",
    quotation: "60",
    negotiation: "80",
    win: "100",
    lost: "0",
  };
  return phases[phase] || "0";
};

const chartRef = ref<HTMLElement | null>(null);
const chart = ref<EChartsType | null>(null);

const optionData = computed(() => ({
  value1: props.data.percentage || 0,
  value2: 100 - (props.data.percentage || 0),
}));

const getChartOption = () => ({
  series: [
    {
      type: "pie",
      center: ["50%", "50%"],
      radius: ["0%", "100%"],
      data: [
        { value: optionData.value.value1, itemStyle: { color: "#35DC99" } },
        {
          value: optionData.value.value2,
          itemStyle: { color: "rgba(53, 220, 153, 0.1)" },
        },
      ],
      labelLine: {
        show: false,
      },
      label: {
        show: true,
        position: "center",
        rich: {
          value: {
            fontSize: 12,
            color: "#000",
            align: "center",
            verticalAlign: "middle",
            padding: [0, 0, 0, 0],
          },
        },
        formatter: function () {
          return "{value|" + optionData.value.value1 + "%}";
        },
      },
      emphasis: {
        scale: false,
      },
    },
  ],
});

const initChart = () => {
  if (chartRef.value) {
    nextTick(() => {
      try {
        if (chart.value) {
          chart.value.dispose();
        }
        chart.value = echarts.init(chartRef.value);
        chart.value.setOption(getChartOption());
        console.log("Chart initialized successfully");
      } catch (error) {
        console.error("Failed to initialize chart:", error);
      }
    });
  }
};

// 监听数据变化，更新图表
watch(
  () => props.data,
  () => {
    nextTick(() => {
      if (chart.value) {
        try {
          chart.value.setOption(getChartOption());
          console.log("Chart updated successfully");
        } catch (error) {
          console.error("Failed to update chart:", error);
        }
      }
    });
  },
  { deep: true }
);

onMounted(() => {
  console.log("Component mounted");
  initChart();
});

onUnmounted(() => {
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }
});
</script>

<style scoped>
.text-34 {
  font-size: 34px;
}
.overflow-one {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
