<!-- 项目回款情况组件 -->
<template>
   <div class="w-[330px] h-[110px] rounded-[22px] bg-white border border-solid border-[#F1F1F1] flex justify-between p-[20px]">
        <div>
            <div class="text-22 leading-7 font-zhongcu">
                {{ formattedReceivedAmount.value }}{{ formattedReceivedAmount.unit }}/{{ formattedTotalAmount.value }}{{ formattedTotalAmount.unit }}
            </div>
            <div class="text-xs font-zhongcu">{{ data.title }}</div>
        </div>
        <div ref="chartRef" class="w-[70px] h-[70px]"></div>
    </div>
</template>
<script setup lang="ts">
import { computed, ref, onMounted } from "vue";
import { formatAmount } from "../../utils"
import * as echarts from 'echarts';



interface ModelData {
    title:string;
    total_received_amount: number;   //已回款金额
    total_contract_amount: number;   //全部回款金额
    percentage?:number, //当前回款对应的百分比
}

const props = defineProps<{
    data: ModelData;  //必填项
}>();


function useFormattedData(amount) {
    if(amount == 0){
        return { value: 0, unit: '' };
    }
  const { value, unit } = formatAmount(amount, false);
  return { value, unit };
}


const formattedReceivedAmount = computed(() => useFormattedData(props.data.total_received_amount));
const formattedTotalAmount = computed(() => useFormattedData(props.data.total_contract_amount));

const chartRef = ref(null);
const chart = ref(null);
const option = {
    series: [
        {
            type: 'gauge',
            startAngle: 180,  //仪表盘起始角度
            endAngle: -180,  //仪表盘结束角度。
            center: ['50%', '50%'], // 确保仪表盘居中显示
            radius: '100%', // 设置仪表盘的半径为画布的50%
            pointer: {
                show: false
            },
            progress: {
                show: true,
                overlap: false,
                roundCap: true,
                itemStyle:{
                    color:'#129BFF'
                }
            },
            axisLine: {
                lineStyle: {
                    width: 10
                },
                shadowOffsetX:0,
                shadowOffsetY:0,
            },
            splitLine: {
                show: false,
                distance: 0,
                length: 0
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                show: false,
                distance: 50
            },
            data: [
                {
                    value: 50.5,
                    // value: props.data.percentage? props.data.percentage : 0,
                    detail: {
                        valueAnimation: true,
                        offsetCenter: ['0%', '0%']
                    }
                }
            ],
            title: {
                fontSize: 14
            },
            detail: {
                fontSize: 14,
                color: '#000',
                formatter: '{value}%'
            }
        }
    ]
};


onMounted(() => {
    if (chart.value) {
        chart.value.dispose();
    }
    chart.value = echarts.init(chartRef.value);
    chart.value.setOption(option);
});


</script>
<style scoped>

</style>