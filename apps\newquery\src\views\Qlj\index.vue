<template>
  <div class="w-[920px] mx-auto text-[#343A3F]">
    <div class="text-base font-medium text-[#121619]">上传数据包</div>
    <div
      class="w-full p-4 h-[300px] rounded-[20px] border border-dashed border-[rgba(0,0,0,0.2)] bg-[#F7F9FC] relative"
    >
      <el-upload
        class="w-full h-full relative z-10 opacity-0"
        ref="upload"
        drag
        :auto-upload="false"
        :limit="1"
        multiple
        :file-list="fileList"
        :show-file-list="false"
        :on-change="handlChange"
        :on-exceed="handleExceed"
        :on-error="handleUploadError"
        accept=".zip"
      />
      <template v-if="isUploading || isUploadSuccess">
        <div class="absolute top-0 left-0 w-full h-full flex flex-col justify-center items-center">
          <img src="@/assets/images/qlj/uploading.png" alt="" class="w-[130px]" />
          <div class="text-sm font-medium mt-2">
            <template v-if="isUploadSuccess">
              <img src="@/assets/images/qlj/success.png" class="w-4 mr-1" />
            </template>
            {{ uploadingText }}
          </div>
          <div class="flex items-end mt-3">
            <div class="w-9 h-9 flex items-center justify-center rounded-md bg-[#CFEAFE]">
              <img src="@/assets/images/qlj/folder.png" alt="" class="w-5 h-5" />
            </div>
            <div class="ml-3 mr-6 text-sm font-medium">
              <img src="@/assets/images/qlj/loading.gif" alt="" class="w-[244px]" />
              <div class="text-sm">{{ file.name || '' }}</div>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="absolute top-0 left-0 w-full h-full flex flex-col justify-center items-center">
          <img src="@/assets/images/qlj/upload.png" alt="" class="w-[130px]" />
          <div class="text-sm font-medium mt-2">
            将叶片设计数据包拖拽到此区域，或 <span class="text-[#129BFE]">点击上传</span>
          </div>
          <div class="text-sm text-[#697077] mt-3">Ctrl + V  或  Command + V 以粘贴数据包</div>
        </div>
      </template>
    </div>
  </div>
  <!-- 数据包详情，可编辑 -->
  <div class="w-[920px] mx-auto mt-10 text-[#343A3F] text-sm">
    <!-- 数据包基本信息 -->
    <div class="pt-3 pb-2 border-0 border-solid border-b border-[rgba(0,0,0,0.2)]">
      <div class="w-full flex">
        <div class="flex items-center mr-5 flex-shrink-0">
          <span class="flex-shrink-0 mr-1">机组型号<span class="text-red-500">*</span></span>

          <div class="relative">
            <el-input v-model="dataPackage.model_number" disabled class="!max-w-[196px]" />
            <el-button
              type=""
              size="small"
              text
              class="w-[22px] h-[22px] ml-1"
              @click="showEditData('model_number')"
            >
              <img src="@/assets/images/qlj/edit.png" alt="" class="w-[22px]" />
            </el-button>
            <div
              v-if="selectedEditKey == 'model_number'"
              class="absolute top-0 left-0 w-full h-full flex items-center justify-between bg-white border border-solid border-[#129BFE] rounded-lg p-2"
            >
              <el-input v-model="tempEditValues.model_number" size="small" />
              <div class="flex-shrink-0 flex items-center">
                <el-button
                  type=""
                  size="small"
                  text
                  class="!w-3 !h-3 !ml-1 !p-0"
                  @click="handleEditTitle()"
                >
                  <el-icon color="#52C668" size="12"><Check /></el-icon>
                </el-button>
                <el-button
                  type=""
                  size="small"
                  text
                  class="!w-3 !h-3 !ml-1 !p-0"
                  @click="cancelEditData()"
                >
                  <el-icon color="#CD1525" size="12"><Close /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="flex items-center mr-5 flex-shrink-0">
          <span class="flex-shrink-0 mr-1">气缸编号<span class="text-red-500">*</span></span>
          <div class="relative">
            <el-input v-model="dataPackage.cylinder_number" disabled class="!w-[50px]" />
            <el-button
              type=""
              size="small"
              text
              class="w-[22px] h-[22px] ml-1"
              @click="showEditData('cylinder_number')"
            >
              <img src="@/assets/images/qlj/edit.png" alt="" class="w-[22px]" />
            </el-button>
            <div
              v-if="selectedEditKey == 'cylinder_number'"
              class="absolute top-0 left-0 w-full h-full flex items-center justify-between bg-white border border-solid border-[#129BFE] rounded-lg p-2"
            >
              <el-input v-model="tempEditValues.cylinder_number" size="small" />
              <div class="flex-shrink-0 flex items-center">
                <el-button
                  type=""
                  size="small"
                  text
                  class="!w-3 !h-3 !ml-1 !p-0"
                  @click="handleEditTitle()"
                >
                  <el-icon color="#52C668" size="12"><Check /></el-icon>
                </el-button>
                <el-button
                  type=""
                  size="small"
                  text
                  class="!w-3 !h-3 !ml-1 !p-0"
                  @click="cancelEditData()"
                >
                  <el-icon color="#CD1525" size="12"><Close /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="flex items-center mr-5 flex-shrink-0">
          <span class="flex-shrink-0 mr-1">气缸名称<span class="text-red-500">*</span></span>
          <div class="relative">
            <el-input v-model="dataPackage.cylinder_name" disabled class="!max-w-[196px]" />
            <el-button
              type=""
              size="small"
              text
              class="w-[22px] h-[22px] ml-1"
              @click="showEditData('cylinder_name')"
            >
              <img src="@/assets/images/qlj/edit.png" alt="" class="w-[22px]" />
            </el-button>
            <div
              v-if="selectedEditKey == 'cylinder_name'"
              class="absolute top-0 left-0 w-full h-full flex items-center justify-between bg-white border border-solid border-[#129BFE] rounded-lg p-2"
            >
              <el-input v-model="tempEditValues.cylinder_name" size="small" />
              <div class="flex-shrink-0 flex items-center">
                <el-button
                  type=""
                  size="small"
                  text
                  class="!w-3 !h-3 !ml-1 !p-0"
                  @click="handleEditTitle()"
                >
                  <el-icon color="#52C668" size="12"><Check /></el-icon>
                </el-button>
                <el-button
                  type=""
                  size="small"
                  text
                  class="!w-3 !h-3 !ml-1 !p-0"
                  @click="cancelEditData()"
                >
                  <el-icon color="#CD1525" size="12"><Close /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="flex items-center flex-shrink-0">
          <span class="flex-shrink-0 mr-1">起始级号<span class="text-red-500">*</span></span>
          <div class="relative">
            <el-input v-model="dataPackage.start_stage" disabled class="!w-[50px]" />
            <el-button
              type=""
              size="small"
              text
              class="w-[22px] h-[22px] ml-1"
              @click="showEditData('start_stage')"
            >
              <img src="@/assets/images/qlj/edit.png" alt="" class="w-[22px]" />
            </el-button>
            <div
              v-if="selectedEditKey == 'start_stage'"
              class="absolute top-0 left-0 w-full h-full flex items-center justify-between bg-white border border-solid border-[#129BFE] rounded-lg p-2"
            >
              <el-input v-model="tempEditValues.start_stage" size="small" />
              <div class="flex-shrink-0 flex items-center">
                <el-button
                  type=""
                  size="small"
                  text
                  class="!w-3 !h-3 !ml-1 !p-0"
                  @click="handleEditTitle()"
                >
                  <el-icon color="#52C668" size="12"><Check /></el-icon>
                </el-button>
                <el-button
                  type=""
                  size="small"
                  text
                  class="!w-3 !h-3 !ml-1 !p-0"
                  @click="cancelEditData()"
                >
                  <el-icon color="#CD1525" size="12"><Close /></el-icon>
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="mt-4 flex justify-between items-center">
        <div class="text-[15px] text-[#121619] w-[80%] truncate">
          数据包 {{ dataPackage.number }}
        </div>
        <a
          :href="dataPackage.file_zip"
          :download="`${dataPackage.number}.zip`"
          class="text-xs flex items-center pl-2 pr-[10px] h-[22px] rounded-md bg-[#CFEAFE] text-[#129BFE] cursor-pointer"
        >
          <img src="@/assets/images/qlj/download.png" alt="" class="w-4" />
          <span class="relative -top-[1px]">下载</span>
        </a>
      </div>
    </div>
    <!-- 数据包叶片信息 -->
    <div class="p-3">
      <template v-if="dataPackage.blade_sets.length > 0">
        <!-- 部套tab切换 -->
        <ChangeBlade v-model="selectedBlade" :blade-sets="dataPackage.blade_sets" />
        <!-- 叶片信息 -->
        <BladeStageDetail :blades-stage-list="bladesStageList" />
      </template>
    </div>
  </div>
</template>

<script setup name="QljView">
import { debounce } from '@/utils/utils';
import { ref, onMounted, watch } from 'vue';
import { dataPackage as _datapackage } from './mock/index';
import { ElIcon, ElMessage, ElLoading } from 'element-plus';
import { Check, Close, ArrowRight, ArrowLeft } from '@element-plus/icons-vue';
import ChangeBlade from './components/ChangeBlade.vue';
import BladeStageDetail from './components/BladeStageDetail.vue';

const file = ref({}); //上传的文件
const fileList = ref([]); //上传的文件列表
const isUploading = ref(false); //是否解析中
const isUploadSuccess = ref(false); //是否解析成功
const uploadingText = ref('正在解析数据中……');
// 是否展示数据包信息
const showDetail = ref(false);
const dataPackage = ref({});
const selectedEditKey = ref(''); //选中的编辑字段key
const tempEditValues = ref({}); // 存储所有字段的临时编辑值
dataPackage.value = _datapackage;

// 添加mock数据开关
const USE_MOCK_DATA = ref(true); // 控制是否使用mock数据

// 上产数据包
const handlChange = debounce(async (_file, _fileList) => {
  isUploading.value = true;
  isUploadSuccess.value = false;
  file.value = _file;
  // 调用接口
  try {
    if (USE_MOCK_DATA.value) {
      setTimeout(() => {
        isUploading.value = false;
        isUploadSuccess.value = true;
        uploadingText.value = '解析完成';
        dataPackage.value = _datapackage;
      }, 1000);

      setTimeout(() => {
        showDetail.value = true;
      }, 2000);
    } else {
      // const res = await test(_file.raw)
      // if(res.data.error == '0'){
      //     isUploading.value = false;
      //     isUploadSuccess.value = true;
      //     uploadingText.value = '解析完成'
      //     dataPackage.value = res.data
      //     setTimeout(() => {
      //         showDetail.value = true
      //     },1000)
      // }
    }
  } catch (e) {
    isUploading.value = false;
    isUploadSuccess.value = false;
    ElMessage('上传失败');
  }
});

// 修改字段信息
const showEditData = _selectedEditKey => {
  selectedEditKey.value = _selectedEditKey;
  tempEditValues.value[_selectedEditKey] = dataPackage.value[_selectedEditKey]; // 初始化临时值为当前值
};

// 保存编辑信息
const handleEditTitle = () => {
  if (selectedEditKey.value) {
    dataPackage.value[selectedEditKey.value] = tempEditValues.value[selectedEditKey.value]; // 确认时才更新实际值
  }
  selectedEditKey.value = '';
};

// 取消编辑
const cancelEditData = () => {
  selectedEditKey.value = '';
};

const handleExceed = (files, fileList) => {
  console.log(files, fileList);
};
const handleUploadError = (err, file, fileList) => {
  console.log(err, file, fileList);
};

// 叶片信息
const selectedBlade = ref(''); // 选中的叶片
const bladesData = ref({}); //指定type的部套信息
const bladesStageList = ref('[]'); // 叶片级号列表

// 监听 selectedBlade 的变化
watch(selectedBlade, newValue => {
  // 这里可以根据 newValue 来更新展示的内容
  // 例如：更新数据、调用API等
  console.log('当前选中的刀片类型：', newValue);
  // TODO: 在这里添加您的业务逻辑
  bladesData.value = dataPackage.value.blade_sets.find(item => item.type === newValue);
  bladesStageList.value = bladesData.value.blades;
});

// 在组件挂载时设置默认值
onMounted(() => {
  console.log;
  if (dataPackage.value.blade_sets.length > 0) {
    selectedBlade.value = dataPackage.value.blade_sets[0].type;
  }
});
</script>
<style scoped>
:deep(.el-upload.is-drag),
:deep(.el-upload-dragger) {
  height: 100%;
}
:deep(.el-input__wrapper:hover),
:deep(.el-input.is-disabled .el-input__wrapper) {
  box-shadow: none;
}
:deep(.el-input.is-disabled .el-input__wrapper),
:deep(.el-input.is-disabled .el-input__inner) {
  cursor: default !important;
}
:deep(.el-input__wrapper) {
  background: transparent;
  box-shadow: none;
  border-radius: 8px;
  overflow: hidden;
  display: inline-block;
}
:deep(.el-tabs__item.is-active) {
  color: #129bfe;
}
:deep(.el-tabs__nav-wrap::after) {
  display: none;
}
:deep(.el-tabs__nav) {
  border: none;
}
:deep(.el-tabs__active-bar) {
  /* width: 24px !important; */
  background-color: #129bfe;
}
:deep(.el-tabs__item) {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: auto;
  padding: 0 20px;
}
</style>
