.DS_Store
node_modules

# 如果需要将一下两个文件忽略，那么需要再第一次commit 之前，现将这两个文件删除，在commit,删除文件的缓存（从 Git 的索引中删除文件，但保留在工作目录中）
# git rm --cached dist/apiConfig.json
# git rm --cached public/apiConfig.json

dist/apiConfig.json
public/apiConfig.json
public/static-config/

# local env files
.env.local
.env.*.local
.env.development

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# dist

# static_aios
.vscode
.prettierrc