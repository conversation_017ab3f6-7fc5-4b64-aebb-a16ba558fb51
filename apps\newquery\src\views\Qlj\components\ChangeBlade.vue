<template>
  <div
    class="bg-[#F2F4F8] h-9 p-[3px] w-full rounded-[39px] overflow-hidden flex text-sm text-[#343A3F]"
  >
    <template v-for="item in bladeSets" :key="item.id">
      <div
        :class="[
          'flex-1 cursor-pointer flex items-center justify-center rounded-[14px]',
          modelValue === item.type ? 'bg-[#129BFE] text-white' : '',
        ]"
        @click="handleChangeBlade(item.type)"
      >
        {{ item.name }} {{ item.number }}
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
interface BladeSet {
  id: number | string;
  type: string;
  name: string;
  number?: number;
}

const props = defineProps<{
  bladeSets: BladeSet[];
  modelValue: string;
}>();

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void;
}>();

const handleChangeBlade = (type: string) => {
  emit('update:modelValue', type);
};
</script>
