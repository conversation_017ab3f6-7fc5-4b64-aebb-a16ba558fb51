import{d as ee,A as K,o as c,n as $,m as S,b as e,c as v,r as l,s as ce,P as xe,M as ut,J as ge,K as ye,e as H,p as G,x as ue,y as pe,f as _,t as F,S as Ft,Y as Tt,z as qe,a as E,k as Ee,v as it,E as x,Q as fe,u as Bt,N as de,ar as Ue,T as Ke,an as is,am as cs,L as ds,R as ct,q as Et,aN as St,aP as us,aQ as ps,w as Te,aa as ae,aK as vs,aD as _s}from"./pnpm-pnpm-B4aX-tnA.js";import{_ as he,a as te,B as pt,a0 as Ht,a2 as vt,z as _t,ab as me,a1 as Pt,P as We,ac as Rt,ad as gs,ae as nt,af as rt,ag as dt,ah as fs,ai as hs,aj as Be,d as ms,M as Cs,v as ws,ak as bs,al as ys,am as xs,an as ks}from"./index-oCQpNzhc.js";import{_ as Mt}from"./DeleteIcon.vue_vue_type_script_setup_true_lang-TAzPfaAy.js";import{a as $s,_ as Ls}from"./UploadThinIcon.vue_vue_type_script_setup_true_lang-D5ZNlFf9.js";import{_ as zs}from"./SearchIcon.vue_vue_type_script_setup_true_lang-4BGMlqW3.js";import{_ as Is}from"./BackArrowIcon.vue_vue_type_script_setup_true_lang-BntbR4_Q.js";const Ds=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 18 17"},[e("g",{"clip-path":"url(#clip0_1175_474)"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M9.00031 2.20002C5.52091 2.20002 2.70029 5.02062 2.70029 8.50002V14.8H9.00031C12.4797 14.8 15.3003 11.9794 15.3003 8.50002C15.3003 5.02062 12.4797 2.20002 9.00031 2.20002ZM2.00029 15.5C1.30029 15.5 1.30029 15.5 1.30029 15.5V8.50002C1.30029 4.24742 4.74771 0.800018 9.00031 0.800018C13.2529 0.800018 16.7003 4.24742 16.7003 8.50002C16.7003 12.7526 13.2529 16.2 9.00031 16.2H2.00035C2.00031 16.2 2.00029 16.2 2.00029 15.5ZM2.00029 15.5L2.00035 16.2C1.61375 16.2 1.30029 15.8866 1.30029 15.5L2.00029 15.5Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.7998 6.39998C4.7998 6.01338 5.11321 5.69998 5.4998 5.69998H11.7998C12.1864 5.69998 12.4998 6.01338 12.4998 6.39998C12.4998 6.78658 12.1864 7.09998 11.7998 7.09998H5.4998C5.11321 7.09998 4.7998 6.78658 4.7998 6.39998Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.7998 9.2C4.7998 8.8134 5.11321 8.5 5.4998 8.5H11.7998C12.1864 8.5 12.4998 8.8134 12.4998 9.2C12.4998 9.5866 12.1864 9.9 11.7998 9.9H5.4998C5.11321 9.9 4.7998 9.5866 4.7998 9.2Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.7998 12C4.7998 11.6134 5.11321 11.3 5.4998 11.3H8.99981C9.38641 11.3 9.69981 11.6134 9.69981 12C9.69981 12.3866 9.38641 12.7 8.99981 12.7H5.4998C5.11321 12.7 4.7998 12.3866 4.7998 12Z"})]),e("defs",null,[e("clipPath",{id:"clip0_1175_474"},[e("rect",{width:"16.8",height:"16.8",fill:"white",transform:"translate(0.600098 0.100006)"})])])],-1),Ss={name:"DescriptionIcon"},Ps=ee({...Ss,props:{color:{default:"#129BFE"},size:{default:18},className:{default:""}},setup(i){return(s,t)=>(c(),K(he,{color:s.color,size:s.size,class:$(s.className)},{default:S(()=>[Ds]),_:1},8,["color","size","class"]))}}),Ms={name:"ExpandIcon",props:{size:{type:[Number,String],default:20},color:{type:String,default:"currentColor"},className:{type:String,default:""}}},Fs=["width","height"],Ts=["fill"];function Bs(i,s,t,a,I,u){return c(),v("svg",{width:t.size,height:t.size,viewBox:"0 0 20 20",class:$(t.className),xmlns:"http://www.w3.org/2000/svg"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.5013 1.66663C2.04106 1.66663 1.66797 2.03972 1.66797 2.49996V7.49996C1.66797 7.9602 2.04106 8.33329 2.5013 8.33329C2.96154 8.33329 3.33464 7.9602 3.33464 7.49996V3.33329H7.5013C7.96154 3.33329 8.33464 2.9602 8.33464 2.49996C8.33464 2.03972 7.96154 1.66663 7.5013 1.66663H2.5013ZM18.3346 12.5C18.3346 12.0397 17.9615 11.6666 17.5013 11.6666C17.0411 11.6666 16.668 12.0397 16.668 12.5V16.6666H12.5013C12.0411 16.6666 11.668 17.0397 11.668 17.5C11.668 17.9602 12.0411 18.3333 12.5013 18.3333H17.5013C17.9615 18.3333 18.3346 17.9602 18.3346 17.5V12.5ZM18.0906 3.08922C18.416 2.76378 18.416 2.23614 18.0906 1.9107C17.7651 1.58527 17.2375 1.58527 16.912 1.9107L12.918 5.90478V2.91661C12.918 2.45637 12.5449 2.08327 12.0846 2.08327C11.6244 2.08327 11.2513 2.45637 11.2513 2.91661V7.91648V7.91661L11.2513 7.91913C11.2516 8.02761 11.2727 8.13122 11.3107 8.2262C11.3514 8.32802 11.4129 8.42344 11.4954 8.50588C11.5813 8.59178 11.6813 8.655 11.7879 8.69555C11.8632 8.72427 11.9438 8.74237 12.0277 8.74803C12.0486 8.74945 12.0695 8.75008 12.0904 8.74994C12.3018 8.74847 12.5127 8.66712 12.6739 8.50588L14.0965 7.08327L18.0906 3.08922ZM2.08529 12.0833C2.08529 11.6231 2.45838 11.25 2.91862 11.25H7.91862C8.37886 11.25 8.75195 11.6231 8.75195 12.0833V17.0833C8.75195 17.5435 8.37886 17.9166 7.91862 17.9166C7.45838 17.9166 7.08529 17.5435 7.08529 17.0833V14.0951L3.09121 18.0892C2.76577 18.4146 2.23813 18.4146 1.9127 18.0892C1.58726 17.7638 1.58726 17.2361 1.9127 16.9107L5.90677 12.9166H2.91862C2.45838 12.9166 2.08529 12.5435 2.08529 12.0833Z",fill:t.color},null,8,Ts)],10,Fs)}const je=te(Ms,[["render",Bs]]),Es={class:"process-level-slider"},Hs={class:"slider-track"},Rs={name:"ProcessLevelComponent"},Vs=ee({...Rs,props:{level:{},status:{}},emits:["update:hover","update:rect"],setup(i,{emit:s}){const t=i,a=s,I=l(null),u=d=>t.status==="fail"?"failed":t.level===0?"waiting":t.level===1&&d===1?"in-progress":t.level===2&&d===1?"completed":t.level===2&&d===2?"in-progress":t.level===3&&(d===1||d===2)?"completed":t.level===3&&d===3?"in-progress":t.level===4?"completed":"waiting",h=ce(()=>{if(t.status==="fail")return 0;switch(t.level){case 0:return 0;case 1:return 0;case 2:return 50;case 3:case 4:return 100;default:return 0}});let p=null;const m=()=>{p&&clearTimeout(p),p=setTimeout(()=>{a("update:hover",!0),I.value&&a("update:rect",I.value.getBoundingClientRect())},50)},w=()=>{p&&clearTimeout(p),p=setTimeout(()=>{a("update:hover",!1)},150)};return xe(()=>{p&&clearTimeout(p)}),(d,f)=>(c(),v("div",{class:"process-level-wrapper",onMouseenter:m,onMouseleave:w,ref_key:"wrapperRef",ref:I},[e("div",Es,[e("div",Hs,[e("div",{class:"slider-progress",style:ut({width:`${h.value}%`})},null,4)]),(c(),v(ge,null,ye(3,r=>e("div",{key:r,class:"slider-dot-wrapper"},[e("div",{class:$(["slider-dot",u(r)])},null,2)])),64))])],544))}}),Ns=te(Vs,[["__scopeId","data-v-69125e2a"]]),Zs=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M4.22183 4.22182C6.21136 2.23232 8.96273 1 12 1C15.0373 1 17.7886 2.23231 19.7782 4.22182L19.0711 4.92893L19.7782 4.22183C21.7677 6.21136 23 8.96273 23 12C23 15.0373 21.7677 17.7886 19.7782 19.7782C17.7886 21.7677 15.0373 23 12 23C8.96273 23 6.21136 21.7677 4.22183 19.7782L4.92893 19.0711L4.22182 19.7782C2.23231 17.7886 1 15.0373 1 12C1 8.96273 2.23232 6.21136 4.22182 4.22183L4.22183 4.22182ZM17.5303 9.53033C17.8232 9.23744 17.8232 8.76256 17.5303 8.46967C17.2374 8.17678 16.7626 8.17678 16.4697 8.46967L11 13.9393L8.53033 11.4697C8.23744 11.1768 7.76256 11.1768 7.46967 11.4697C7.17678 11.7626 7.17678 12.2374 7.46967 12.5303L10.4697 15.5303C10.7626 15.8232 11.2374 15.8232 11.5303 15.5303L17.5303 9.53033Z"})],-1),As={name:"CheckCircleSolidIcon"},Vt=ee({...As,props:{color:{default:"#129BFE"},size:{default:24},className:{default:""}},setup(i){return(s,t)=>(c(),K(he,{color:s.color,size:s.size,class:$(s.className)},{default:S(()=>[Zs]),_:1},8,["color","size","class"]))}}),Os={components:{CheckCircleSolidIcon:Vt},props:{step:{type:Number,required:!0},typestr:{type:String,required:!0}}},Nt=i=>(ue("data-v-4e085760"),i=i(),pe(),i),Us={class:"progress-div font16"},Ks={class:"progress-item"},Ws={key:0},js={key:1},qs={class:"progress-item"},Gs=Nt(()=>e("div",null,"数据学习",-1)),Qs={class:"progress-item"},Js=Nt(()=>e("div",null,"学习完成",-1));function Xs(i,s,t,a,I,u){const h=H("CheckCircleSolidIcon");return c(),v("div",Us,[e("div",Ks,[e("div",{class:$(["progredd-item-shuzi",[t.step>=1?"progredd-item-shuzi-active":"progredd-item-shuzi-inactive"]])}," 1 ",2),t.typestr=="text"?(c(),v("div",Ws,"添加文本")):(c(),v("div",js,"选择文件"))]),e("div",{class:$(["progress-fenge",t.step>=2?"progredd-item-shuzi-active":""])},null,2),e("div",qs,[e("div",{class:$(["progredd-item-shuzi",[t.step>=2?"progredd-item-shuzi-active":"progredd-item-shuzi-inactive"]])}," 2 ",2),Gs]),e("div",{class:$(["progress-fenge",t.step>=3?"progredd-item-shuzi-active":""])},null,2),e("div",Qs,[e("div",{class:$(["progredd-item-shuzi",[t.step>=3?"progredd-item-shuzi-inactive !border-0":"progredd-item-shuzi-inactive"]])},[t.step>=3?(c(),K(h,{key:0,size:24})):(c(),v(ge,{key:1},[G("3")],64))],2),Js])])}const Ys=te(Os,[["render",Xs],["__scopeId","data-v-4e085760"]]),eo=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 72 72"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M36 3C26.8882 3 18.6341 6.69695 12.6655 12.6655L12.6655 12.6655C6.69695 18.6341 3 26.8882 3 36C3 45.1118 6.69694 53.3659 12.6655 59.3345L14.7868 57.2132L12.6655 59.3345C18.6341 65.303 26.8882 69 36 69C45.1118 69 53.3659 65.303 59.3345 59.3345C65.303 53.3659 69 45.1118 69 36C69 26.8882 65.303 18.6341 59.3345 12.6655L57.2132 14.7868L59.3345 12.6655C53.3659 6.69694 45.1118 3 36 3ZM16.9081 16.9081C21.7975 12.0188 28.5434 9 36 9C43.4566 9 50.2025 12.0188 55.0918 16.9081C59.9812 21.7975 63 28.5434 63 36C63 43.4566 59.9812 50.2025 55.0918 55.0918C50.2025 59.9812 43.4566 63 36 63C28.5434 63 21.7975 59.9812 16.9081 55.0918C12.0188 50.2025 9 43.4566 9 36C9 28.5434 12.0188 21.7975 16.9081 16.9081ZM53.1213 29.1213C54.2929 27.9497 54.2929 26.0503 53.1213 24.8787C51.9497 23.7071 50.0503 23.7071 48.8787 24.8787L33 40.7574L26.1213 33.8787C24.9497 32.7071 23.0503 32.7071 21.8787 33.8787C20.7071 35.0503 20.7071 36.9497 21.8787 38.1213L30.8787 47.1213C32.0503 48.2929 33.9497 48.2929 35.1213 47.1213L53.1213 29.1213Z"})],-1),to={name:"SuccessIcon"},so=ee({...to,props:{color:{default:"#52C668"},size:{default:72},className:{default:""}},setup(i){return(s,t)=>(c(),K(he,{color:s.color,size:s.size,class:$(s.className)},{default:S(()=>[eo]),_:1},8,["color","size","class"]))}}),oo={name:"CompleteStep",components:{SuccessIcon:so},props:{text:{type:String,default:"该文本已学习完成"}},emits:["cancel","goTest"]},ao={class:"upload-step3 h-full"},lo={class:"complete-step-content"},no={class:"text-[#697077] text-center text-base font-medium leading-6 font-['PingFang_SC']"},ro={class:"upload-btn-wrapper"};function io(i,s,t,a,I,u){const h=H("SuccessIcon");return c(),v("div",ao,[e("div",lo,[_(h,{class:"mb-6"}),e("p",no,F(t.text),1)]),e("div",ro,[e("button",{onClick:s[0]||(s[0]=p=>i.$emit("cancel")),class:"btn-base btn-cancel"},"取消"),e("button",{onClick:s[1]||(s[1]=p=>i.$emit("goTest")),class:"btn-base upload-btn active"}," 去测试 ")])])}const Zt=te(oo,[["render",io],["__scopeId","data-v-76ac6cb4"]]),co={name:"DocAddTextView",components:{BaseNavComponent:Ht,DocProgressComponent:Ys,BreadCrumbComponent:pt,ElIcon:Tt,ArrowLeftBold:Ft,CompleteStep:Zt},props:{kbaseId:{type:[String,Number],required:!0},visible:{type:Boolean,default:!1},editDocId:{type:[String,Number],default:null}},emits:["update:visible","close","success","goTest"],setup(i,{emit:s}){const t=l("kbase"),a=l("知识库"),I=l("doc"),u=l(1),h=l("advance"),p=l(i.editDocId),m=l([]),w=l(null),d=l(""),f=l(""),r=l([]),D=l([]),R=l(!1),W=l(""),O=l([]),A=l(null),X=g=>{h.value=g},J=async()=>{try{const g=await Pt(i.kbaseId,p.value);g.data.message?x.error(g.data.message):(d.value=g.data.title,f.value=g.data.content,h.value=g.data.learn_type||"advance",p.value=g.data.id)}catch{p.value=null,u.value=1}},y=async()=>{const g=await We(i.kbaseId);return g.data.error=="0"?`知识库（${g.data.title}）`:"知识库"};qe(async()=>{i.editDocId&&J();let g=await y();D.value=[{path:"/kbase-list",name:g},{path:`/doc-list/${i.kbaseId}`,name:"文档"},{path:"",name:"自定义文本"}]}),xe(()=>{w.value&&clearInterval(w.value)});const b=()=>{s("update:visible",!1),s("close")},z=_t(async()=>{if(!d.value||!f.value){x("请将内容填写完成");return}A.value=fe.service({lock:!0,text:"提交中",background:"rgba(0, 0, 0, 0.5)"});try{const g=await Rt(i.kbaseId,h.value,"text",d.value,f.value,"");g.data.error=="0"?(p.value=g.data.id,A.value.close(),u.value=2,L(),w.value&&clearInterval(w.value),w.value=setInterval(L,2e3)):g.data.message&&x.error(g.data.message)}catch{x.error("学习失败")}}),L=async()=>{try{const g=await Pt(i.kbaseId,p.value);if(g.data.error==="0"){const V=m.value.findIndex(j=>j.id===g.data.id);V!==-1?m.value[V].status!==g.data.status&&(m.value[V].status=g.data.status,m.value[V].status_display=g.data.status_display):m.value.push({title:`${g.data.title}.${g.data.type}`,status:g.data.status,status_display:g.data.status_display||"学习中",id:g.data.id,percentage:g.data.percentage||{percentage_a:0,percentage_b:0,percentage_c:0}}),m.value.forEach(j=>{j.id===g.data.id&&(j.percentage=g.data.percentage||{percentage_a:0,percentage_b:0,percentage_c:0})}),m.value.every(j=>j.status==="success")&&setTimeout(()=>{clearInterval(w.value),u.value=3,s("success")},1e3)}}catch{}};return{selectedBar:t,kbaseName:a,selectedKBaseNav:I,step:u,selectedType:h,chooseType:X,docName:d,docContent:f,fileList:r,items:m,AddDocText:z,breadcrumbs:D,content:W,searchTest:async()=>{if(!R.value){if(R.value=!0,!W.value){x.error("请输入要测试的文本"),R.value=!1;return}A.value=fe.service({lock:!0,text:"搜索中",background:"rgba(0, 0, 0, 0.5)"}),O.value=[];try{const g=await searchTestAction(i.kbaseId,W.value);g.data.message?x.error(g.data.message):O.value=g.data}catch{A.value.close(),x.error("搜索失败")}finally{R.value=!1,A.value.close()}}},results:O,getFileIcon:vt,handleClose:b}}},ke=i=>(ue("data-v-d1bb638d"),i=i(),pe(),i),uo={class:"el-main-right-bottom overflow-y"},po={key:0,class:"upload-step1"},vo={class:"upload-step1-left !w-full"},_o={class:"upload-step1-left-item"},go=ke(()=>e("div",{class:"font16 font-zhongcu"},"数据集名称",-1)),fo={class:"upload-step1-left-item upload-step1-left-item-txt"},ho=ke(()=>e("div",{class:"font16 font-zhongcu"},"数据集内容",-1)),mo={class:"upload-btn-wrapper"},Co={key:1,class:"upload-step3 !pt-0"},wo={class:"info-box w-full"},bo=ke(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},[e("g",{"clip-path":"url(#clip0_1940_6756)"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.81585 2.81487C4.14221 1.48854 5.97646 0.666992 8.0013 0.666992C10.0261 0.666992 11.8604 1.48854 13.1867 2.81487L12.7153 3.28628L13.1867 2.81488C14.5131 4.14123 15.3346 5.97548 15.3346 8.00033C15.3346 10.0252 14.5131 11.8594 13.1867 13.1858C11.8604 14.5121 10.0261 15.3337 8.0013 15.3337C5.97645 15.3337 4.14221 14.5121 2.81585 13.1858L3.28726 12.7144L2.81585 13.1858C1.48951 11.8594 0.667969 10.0252 0.667969 8.00033C0.667969 5.97548 1.48951 4.14123 2.81585 2.81488L2.81585 2.81487ZM8.83398 4.50016C8.83398 4.03993 8.46088 3.66683 8.00065 3.66683C7.54042 3.66683 7.16732 4.03993 7.16732 4.50016C7.16732 4.9604 7.54042 5.3335 8.00065 5.3335C8.46088 5.3335 8.83398 4.9604 8.83398 4.50016ZM7.50065 6.16683C7.22451 6.16683 7.00065 6.39069 7.00065 6.66683C7.00065 6.94297 7.22451 7.16683 7.50065 7.16683H7.66732V10.8335H7.00065C6.72451 10.8335 6.50065 11.0574 6.50065 11.3335C6.50065 11.6096 6.72451 11.8335 7.00065 11.8335H8.16633L8.16732 11.8335L8.1683 11.8335H9.33398C9.61013 11.8335 9.83398 11.6096 9.83398 11.3335C9.83398 11.0574 9.61013 10.8335 9.33398 10.8335H8.66732V6.66683C8.66732 6.39069 8.44346 6.16683 8.16732 6.16683H7.50065Z",fill:"#4CB4FF"})]),e("defs",null,[e("clipPath",{id:"clip0_1940_6756"},[e("rect",{width:"16",height:"16",fill:"white"})])])],-1)),yo=ke(()=>e("span",null,"如可以关闭弹窗就此处理，知识人工过程可后台继续进行，文件学习成功后即可进行知识问答。",-1)),xo={class:"upload-step3-file-list overflow-y"},ko={class:"font-zhongcu overflow-one"},$o=ke(()=>e("img",{src:gs,alt:"file icon",class:"file-icon"},null,-1)),Lo={class:"font-zhongcu el-col-item-status mr-4"},zo={class:"flex items-center gap-4"},Io={class:"progress-wrapper"},Do={class:"status-wrapper"},So={key:0,src:me,alt:"",class:"w-3.5 h-3.5"},Po={class:"upload-btn-wrapper"},Mo=ke(()=>e("button",{class:"btn-base upload-btn"},[G(" 学习中 "),e("img",{src:me,alt:"",class:"loading-icon"})],-1)),Fo={key:2,class:"h-full"};function To(i,s,t,a,I,u){const h=H("DocProgressComponent"),p=H("el-icon"),m=H("el-progress"),w=H("CompleteStep");return c(),v("div",uo,[_(h,{step:a.step,typestr:"text"},null,8,["step"]),a.step==1?(c(),v("div",po,[e("div",vo,[e("div",_o,[go,Ee(e("input",{type:"text",class:"el-main-item-ipt font14 font-zhongcu !bg-[#F5F5F5]","onUpdate:modelValue":s[0]||(s[0]=d=>a.docName=d),placeholder:"输入名称",maxlength:"50"},null,512),[[it,a.docName]])]),e("div",fo,[ho,Ee(e("textarea",{"onUpdate:modelValue":s[1]||(s[1]=d=>a.docContent=d),maxlength:"20000",placeholder:"输入内容",class:"font14 font-zhongcu !w-full !bg-[#F5F5F5]"},null,512),[[it,a.docContent]])]),e("div",mo,[e("button",{class:"btn-base btn-cancel",onClick:s[2]||(s[2]=(...d)=>a.handleClose&&a.handleClose(...d))},"取消"),e("button",{class:$(["btn-base upload-btn",{active:a.docName&&a.docContent}]),onClick:s[3]||(s[3]=(...d)=>a.AddDocText&&a.AddDocText(...d))}," 开始学习 ",2)])])])):E("",!0),a.step==2?(c(),v("div",Co,[e("div",wo,[_(p,{class:"info-icon",size:"16"},{default:S(()=>[bo]),_:1}),yo]),e("div",xo,[(c(!0),v(ge,null,ye(a.items,(d,f)=>{var r,D;return c(),v("div",{class:"el-col-item font14",key:f},[e("div",ko,[$o,G(" "+F(d.title),1)]),e("div",Lo,[e("div",zo,[e("div",Io,[_(m,{percentage:((r=d.percentage)==null?void 0:r.percentage_a)||0,color:((D=d.percentage)==null?void 0:D.percentage_a)===100?"#67C23A":"#129BFE","show-text":!0,"stroke-width":4,class:"flex-1"},null,8,["percentage","color"])]),e("div",Do,[["wait","split","doing"].includes(d.status)?(c(),v("img",So)):E("",!0),G(" "+F(d.status_display),1)])])])])}),128))]),e("div",Po,[e("button",{class:"btn-base btn-cancel",onClick:s[4]||(s[4]=(...d)=>a.handleClose&&a.handleClose(...d))},"取消"),Mo])])):E("",!0),a.step==3?(c(),v("div",Fo,[_(w,{onCancel:a.handleClose,onGoTest:s[5]||(s[5]=d=>i.$emit("goTest"))},null,8,["onCancel"])])):E("",!0)])}const Bo=te(co,[["render",To],["__scopeId","data-v-d1bb638d"]]),Eo={name:"DocProgressV2Four",components:{CheckCircleSolidIcon:Vt},props:{step:{type:Number,default:1}}},Ce=i=>(ue("data-v-38d0484a"),i=i(),pe(),i),Ho={class:"progress-div font16"},Ro={class:"progress-item"},Vo=Ce(()=>e("span",{class:"number"},"1",-1)),No=[Vo],Zo=Ce(()=>e("div",null,"选择文件",-1)),Ao={class:"progress-item"},Oo=Ce(()=>e("span",{class:"number"},"2",-1)),Uo=[Oo],Ko=Ce(()=>e("div",null,"上传文件",-1)),Wo={class:"progress-item"},jo=Ce(()=>e("span",{class:"number"},"3",-1)),qo=[jo],Go=Ce(()=>e("div",null,"文件学习",-1)),Qo={class:"progress-item"},Jo={key:1,class:"number"},Xo=Ce(()=>e("div",null,"学习完成",-1));function Yo(i,s,t,a,I,u){const h=H("CheckCircleSolidIcon");return c(),v("div",Ho,[e("div",Ro,[e("div",{class:$(["progredd-item-shuzi",[t.step>=1?"progredd-item-shuzi-active":"progredd-item-shuzi-inactive"]])},No,2),Zo]),e("div",{class:$(["progress-fenge",t.step>=2?"progredd-item-shuzi-active":""])},null,2),e("div",Ao,[e("div",{class:$(["progredd-item-shuzi",[t.step>=2?"progredd-item-shuzi-active":"progredd-item-shuzi-inactive"]])},Uo,2),Ko]),e("div",{class:$(["progress-fenge",t.step>=3?"progredd-item-shuzi-active":""])},null,2),e("div",Wo,[e("div",{class:$(["progredd-item-shuzi",[t.step>=3?"progredd-item-shuzi-active":"progredd-item-shuzi-inactive"]])},qo,2),Go]),e("div",{class:$(["progress-fenge",t.step>=4?"progredd-item-shuzi-active":""])},null,2),e("div",Qo,[e("div",{class:$(["progredd-item-shuzi",[t.step>=4?"progredd-item-shuzi-inactive !border-0":"progredd-item-shuzi-inactive"]])},[t.step>=4?(c(),K(h,{key:0,class:"check-icon"})):(c(),v("span",Jo,"4"))],2),Xo])])}const ea=te(Eo,[["render",Yo],["__scopeId","data-v-38d0484a"]]),ta={name:"DocAddFile",components:{BaseNavComponent:Ht,ElIcon:Tt,ArrowLeftBold:Ft,DocProgressV2Four:ea,BreadCrumbComponent:pt,CompleteStep:Zt,ExpandIcon:je},props:{kbaseId:{type:[String,Number],required:!0},visible:{type:Boolean,default:!1}},emits:["update:visible","close","success"],setup(i,{emit:s}){const t=l("kbase"),a=l("知识库"),I=l("doc"),u=l(i.kbaseId),h=l([]),p=l(1),m=l("advance"),w=l([]);Bt();const d=l(10),f=l(["txt","docx","xlsx","pptx","pdf","csv","md"]),r=l(500),D=l(""),R=l(""),W=l([]),O=l(0),A=l(0),X=l(null),J=l([]),y=l(!1),b=l(""),z=l([]),L=l(null),P=l({}),g=l(!1),V=C=>{if(f.value){let B="";if(C.name.lastIndexOf(".")>-1&&(B=C.name.slice(C.name.lastIndexOf(".")+1).toLowerCase()),!f.value.some(Y=>(Y=Y.toLowerCase(),C.raw.type.toLowerCase().includes(Y)||B.includes(Y))))return x(`文件格式不正确, 上传失败！请上传${f.value.join("/")}格式文件!`),!1}return r.value&&!(C.size/1024/1024<r.value)?(x(`上传文件大小不能超过 ${r.value} MB!`),!1):!0},le=(C,B)=>{V(C)&&(W.value=B,A.value+=1)},j=()=>{x(`一次最多上传${d.value}个文件`)},ve=()=>{x.error("上传失败, 请重试")},$e=()=>{p.value=2,He()},He=_t(async()=>{try{for(const C of W.value){P.value[C.uid]=0;const B=await Rt(u.value,m.value,"","","",C.raw,U=>{const Y=Math.round(U.loaded*100/U.total);P.value[C.uid]=Y});B.data.error=="0"?(R.value+=B.data.id+"|",O.value+=1,P.value[C.uid]=100):x.error("上传中断，部分文件上传失败")}O.value===W.value.length&&(p.value=3,Le(),X.value&&clearInterval(X.value),X.value=setInterval(Le,2e3))}catch{x.error("上传失败")}}),Je=C=>{m.value=C},Q=C=>{W.value=W.value.filter(B=>B.uid!==C),A.value-=1},Xe=()=>{if(p.value===2){x.warning("请等待文件上传完成后再关闭");return}if(p.value===3){x.warning("请等待文件学习完成后再关闭");return}s("update:visible",!1),s("close")},Re=C=>({wait:"待学习",split:"分片完成",doing:"学习中",success:"学习成功",fail:"学习失败"})[C]||C,Le=async()=>{const C=R.value.split("|").filter(Boolean);if(console.log("开始查询文档状态，文档ID列表:",C),C.length===0){console.log("没有需要查询的文档ID");return}try{console.log("调用批量查询接口...");let B;if(g.value?B={data:W.value.map((U,Y)=>{if(Y===0)return{id:C[0],title:rt(U.name),type:nt(U.name),status:"fail",percentage:{percentage_a:30,percentage_b:0,percentage_c:0}};const N=Math.min(100,Math.floor(Math.random()*100)+30);return{id:Y+1,title:rt(U.name),type:nt(U.name),status:N===100?"success":"doing",percentage:{percentage_a:N,percentage_b:N>50?N-50:0,percentage_c:N>80?N-80:0}}})}:B=await Be(u.value,C),B.data){const U=B.data||[];console.log("获取到文档状态数据:",U),w.value.length===0&&(w.value=W.value.map(N=>({id:null,title:N.name,status:"wait",status_display:Re("wait"),percentage:{percentage_a:0,percentage_b:0,percentage_c:0}}))),U.forEach(N=>{const De=w.value.findIndex(Ve=>Ve.id===N.id||Ve.title===N.title+"."+N.type);De!==-1&&(w.value[De]={...w.value[De],id:N.id,title:N.title+"."+N.type,status:N.status,status_display:Re(N.status),percentage:N.percentage||{percentage_a:0,percentage_b:0,percentage_c:0}})}),w.value=[...w.value],console.log("更新后的文档列表:",w.value);const Y=w.value.every(N=>N.status==="success");console.log("是否所有文档都已成功完成:",Y),Y&&(console.log("所有文档处理成功，清除定时器"),clearInterval(X.value),p.value=4,D.value=`${w.value.length}个文件学习成功并入库完成`,s("success"))}}catch(B){console.error("获取文档状态失败",B)}},ze=async()=>{const C=await We(u.value);return C.data.error=="0"?`知识库（${C.data.title}）`:"知识库"},Ie=async()=>{if(!y.value){if(y.value=!0,b.value==""||!b.value){x.error("请输入要测试的文本"),y.value=!1;return}L.value=fe.service({lock:!0,text:"搜索中",background:"rgba(0, 0, 0, 0.5)"}),z.value=[];try{const C=await fs(u.value,b.value);C.data.message?x.error(C.data.message):z.value=C.data}catch{L.value.close(),x.error("搜索失败")}finally{y.value=!1,L.value.close()}}};qe(async()=>{u.value=i.kbaseId;let C=await ze();J.value=[{path:"/kbase-list",name:C},{path:`/doc-list/${u.value}`,name:"文档"},{path:"",name:"本地文件上传"}]}),de(()=>i.kbaseId,C=>{u.value=C}),xe(()=>{X.value&&clearInterval(X.value)});const Ye=ce(()=>w.value.some(C=>C.status==="fail"));return{selectedBar:t,kbaseName:a,selectedKBaseNav:I,selectedKid:u,selectedFiles:h,fileList:W,fileNum:d,successfulUploads:O,totalUploads:A,step:p,selectedType:m,chooseType:Je,getFileIcon:vt,getFileBaseName:rt,getFileExtension:nt,items:w,breadcrumbs:J,content:b,searchTest:Ie,results:z,handlChange:le,handleExceed:j,handleUploadError:ve,uploadFiles:He,delDocment:Q,handleClose:Xe,completeText:D,uploadProgress:P,startUpload:$e,hasFailedDocuments:Ye,handleRetryFailed:async()=>{L.value=fe.service({lock:!0,text:"正在提交",background:"rgba(0, 0, 0, 0.5)"});try{const C=w.value.filter(B=>B.status==="fail").map(B=>B.id);await Promise.all(C.map(B=>dt(u.value,B)))}catch(C){console.error("重新学习失败:",C),x.error("重新学习失败")}finally{L.value.close(),Le()}},getProgressBarColor:C=>{var B;return C.status==="fail"?"#FF4D4F":((B=C.percentage)==null?void 0:B.percentage_a)===100?"#67C23A":"#129BFE"}}}},we=i=>(ue("data-v-63826713"),i=i(),pe(),i),sa={key:0,class:"overflow-y",style:{"max-height":"90%"}},oa=we(()=>e("img",{src:hs,alt:""},null,-1)),aa={class:"el-upload__text"},la=we(()=>e("div",{class:"font14 font-zhongcu",style:{color:"#000"}}," 点击此处选择文件上传 ",-1)),na=we(()=>e("div",{class:"font12",style:{color:"#666","line-height":"17px","margin-top":"5px"}}," 支持 .txt, .docx, .csv, .xlsx, .md, .pptx, .pdf类型文件 ",-1)),ra={class:"font12",style:{color:"#666","line-height":"17px"}},ia={key:0,class:"el-main-list max-h-[200px] overflow-y-auto",style:{"margin-top":"15px"}},ca={class:"el-col-body"},da={class:"font-zhongcu overflow-one"},ua=["src"],pa={class:"el-col-item-title overflow-one"},va={style:{width:"10%","text-align":"right"}},_a={class:"fixed-bottom-btns"},ga={key:1,class:"btn-base upload-btn",style:{opacity:"0.5",cursor:"not-allowed"}},fa={key:1,class:"upload-step2 !pt-0"},ha={class:"el-main-list"},ma={class:"el-col-body overflow-y"},Ca={class:"font-zhongcu overflow-one"},wa=["src"],ba={class:"font-zhongcu",style:{width:"15%","text-align":"right"}},ya={key:0,src:me,alt:"",class:"loading-icon",style:{"margin-left":"4px"}},xa=we(()=>e("div",{class:"fixed-bottom-btns"},[e("button",{class:"btn-base upload-btn"},[G(" 上传中 "),e("img",{src:me,alt:"",class:"loading-icon"})])],-1)),ka={key:2,class:"upload-step3 !pt-0"},$a={class:"info-box w-full"},La=we(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},[e("g",{"clip-path":"url(#clip0_1940_6756)"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.81585 2.81487C4.14221 1.48854 5.97646 0.666992 8.0013 0.666992C10.0261 0.666992 11.8604 1.48854 13.1867 2.81487L12.7153 3.28628L13.1867 2.81488C14.5131 4.14123 15.3346 5.97548 15.3346 8.00033C15.3346 10.0252 14.5131 11.8594 13.1867 13.1858C11.8604 14.5121 10.0261 15.3337 8.0013 15.3337C5.97645 15.3337 4.14221 14.5121 2.81585 13.1858L3.28726 12.7144L2.81585 13.1858C1.48951 11.8594 0.667969 10.0252 0.667969 8.00033C0.667969 5.97548 1.48951 4.14123 2.81585 2.81488L2.81585 2.81487ZM8.83398 4.50016C8.83398 4.03993 8.46088 3.66683 8.00065 3.66683C7.54042 3.66683 7.16732 4.03993 7.16732 4.50016C7.16732 4.9604 7.54042 5.3335 8.00065 5.3335C8.46088 5.3335 8.83398 4.9604 8.83398 4.50016ZM7.50065 6.16683C7.22451 6.16683 7.00065 6.39069 7.00065 6.66683C7.00065 6.94297 7.22451 7.16683 7.50065 7.16683H7.66732V10.8335H7.00065C6.72451 10.8335 6.50065 11.0574 6.50065 11.3335C6.50065 11.6096 6.72451 11.8335 7.00065 11.8335H8.16633L8.16732 11.8335L8.1683 11.8335H9.33398C9.61013 11.8335 9.83398 11.6096 9.83398 11.3335C9.83398 11.0574 9.61013 10.8335 9.33398 10.8335H8.66732V6.66683C8.66732 6.39069 8.44346 6.16683 8.16732 6.16683H7.50065Z",fill:"#4CB4FF"})]),e("defs",null,[e("clipPath",{id:"clip0_1940_6756"},[e("rect",{width:"16",height:"16",fill:"white"})])])],-1)),za=we(()=>e("span",null,"如可以关闭弹窗就此处理，知识人工过程可后台继续进行，文件学习成功后即可进行知识问答。",-1)),Ia={class:"upload-step3-file-list overflow-y"},Da={class:"font-zhongcu overflow-one"},Sa=["src"],Pa={class:"font-zhongcu el-col-item-status mr-4"},Ma={class:"flex items-center gap-4"},Fa={class:"progress-wrapper"},Ta={class:"status-wrapper"},Ba={key:0,src:me,alt:"",class:"w-3.5 h-3.5"},Ea={class:"fixed-bottom-btns"},Ha={key:1,class:"btn-base upload-btn"},Ra=we(()=>e("img",{src:me,alt:"",class:"loading-icon"},null,-1)),Va={key:3,class:"h-full"};function Na(i,s,t,a,I,u){const h=H("DocProgressV2Four"),p=H("el-upload"),m=H("el-button"),w=H("el-icon"),d=H("el-progress"),f=H("CompleteStep");return c(),v("div",{class:$(["el-main-bottom",{"!pb-0":a.step===4}])},[_(h,{step:a.step},null,8,["step"]),a.step==1?(c(),v("div",sa,[_(p,{class:"upload-demo upload-file-uploader",ref:"upload",drag:"","auto-upload":!1,limit:a.fileNum,multiple:"","file-list":a.fileList,"show-file-list":!1,accept:".txt, .docx, .pdf, .csv, .xlsx, .md, .pptx","on-change":a.handlChange,"on-exceed":a.handleExceed,"on-error":a.handleUploadError},{default:S(()=>[oa,e("div",aa,[la,na,e("div",ra," 最多支持 "+F(a.fileNum)+" 个文件。单个文件最大 500MB ",1)])]),_:1},8,["limit","file-list","on-change","on-exceed","on-error"]),a.fileList.length>0?(c(),v("div",ia,[e("div",ca,[(c(!0),v(ge,null,ye(a.fileList,(r,D)=>(c(),v("div",{class:"el-col-item font14",key:D},[e("div",da,[e("img",{src:a.getFileIcon(r.name),alt:"file icon",class:"file-icon"},null,8,ua),e("span",pa,F(r.name),1)]),e("div",va,[_(m,{link:"",type:"primary",size:"default",class:"el-col-item-btn",onClick:R=>a.delDocment(r.uid)},{default:S(()=>[G("删除")]),_:2},1032,["onClick"])])]))),128))])])):E("",!0),e("div",_a,[e("button",{class:"btn-base btn-cancel",onClick:s[0]||(s[0]=(...r)=>a.handleClose&&a.handleClose(...r))},"取消"),a.fileList.length>0?(c(),v("button",{key:0,class:"btn-base upload-btn active",onClick:s[1]||(s[1]=(...r)=>a.startUpload&&a.startUpload(...r))}," 上传 ")):(c(),v("button",ga," 上传 "))])])):E("",!0),a.step==2?(c(),v("div",fa,[e("div",ha,[e("div",ma,[(c(!0),v(ge,null,ye(a.fileList,(r,D)=>(c(),v("div",{key:D,class:"el-col-item font14"},[e("div",Ca,[e("img",{src:a.getFileIcon(r.name),alt:"file icon",class:"file-icon"},null,8,wa),e("span",null,F(r.name),1)]),e("div",ba,[e("span",{class:$(["status-text",{uploading:a.uploadProgress[r.uid]>0&&a.uploadProgress[r.uid]<100,success:a.uploadProgress[r.uid]===100}])},[G(F(a.uploadProgress[r.uid]===0?"上传中":a.uploadProgress[r.uid]===100?"上传成功":"待上传")+" ",1),a.uploadProgress[r.uid]===0?(c(),v("img",ya)):E("",!0)],2)])]))),128))])]),xa])):E("",!0),a.step==3?(c(),v("div",ka,[e("div",$a,[_(w,{class:"info-icon",size:"16"},{default:S(()=>[La]),_:1}),za]),e("div",Ia,[(c(!0),v(ge,null,ye(a.items,(r,D)=>{var R;return c(),v("div",{class:"el-col-item font14",key:D},[e("div",Da,[e("img",{src:a.getFileIcon(r.title),alt:"file icon",class:"file-icon"},null,8,Sa),G(" "+F(r.title),1)]),e("div",Pa,[e("div",Ma,[e("div",Fa,[_(d,{percentage:((R=r.percentage)==null?void 0:R.percentage_a)||0,color:a.getProgressBarColor(r),"show-text":!0,"stroke-width":4,class:"flex-1"},null,8,["percentage","color"])]),e("div",Ta,[["wait","split","doing"].includes(r.status)?(c(),v("img",Ba)):E("",!0),G(" "+F(r.status_display),1)])])])])}),128))]),e("div",Ea,[a.hasFailedDocuments?(c(),v("button",{key:0,class:"btn-base upload-btn active",onClick:s[2]||(s[2]=(...r)=>a.handleRetryFailed&&a.handleRetryFailed(...r))}," 重新学习 ")):(c(),v("button",Ha,[G(" 学习中 "),Ra]))])])):E("",!0),a.step==4?(c(),v("div",Va,[_(f,{text:a.completeText,onCancel:a.handleClose,onGoTest:s[3]||(s[3]=r=>i.$emit("goTest"))},null,8,["text","onCancel"])])):E("",!0)],2)}const Za=te(ta,[["render",Na],["__scopeId","data-v-63826713"]]),Ge=i=>(ue("data-v-e3ae718b"),i=i(),pe(),i),Aa={class:"status-card"},Oa={class:"flex items-center justify-between mb-3"},Ua=Ge(()=>e("div",{class:"card-title"},"知识处理状态",-1)),Ka=Ge(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 18 18",fill:"none"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M9 1.5C6.88125 1.5 4.965 2.38125 3.60375 3.79125C3.315 4.0875 3.32625 4.56375 3.6225 4.8525C3.91875 5.14125 4.395 5.13 4.68375 4.83375C5.775 3.70125 7.305 3 9 3C12.315 3 15 5.685 15 9C15 9.4125 15.3375 9.75 15.75 9.75C16.1625 9.75 16.5 9.4125 16.5 9V3C16.5 2.5875 16.1625 2.25 15.75 2.25C15.3375 2.25 15 2.5875 15 3V4.5C13.6312 2.6775 11.4525 1.5 9 1.5ZM3 9C3 8.5875 2.6625 8.25 2.25 8.25C1.8375 8.25 1.5 8.5875 1.5 9V15C1.5 15.4125 1.8375 15.75 2.25 15.75C2.6625 15.75 3 15.4125 3 15V13.5C4.36875 15.3225 6.5475 16.5 9 16.5C11.0212 16.5 12.8587 15.6975 14.2087 14.3962C14.505 14.1075 14.5162 13.635 14.2275 13.335C13.9387 13.0387 13.4663 13.0275 13.1663 13.3163C12.0863 14.3588 10.62 15 9 15C5.685 15 3 12.315 3 9Z",fill:"#697077"})],-1)),Wa={class:"info-box"},ja=Ge(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},[e("g",{"clip-path":"url(#clip0_1940_6756)"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.81585 2.81487C4.14221 1.48854 5.97646 0.666992 8.0013 0.666992C10.0261 0.666992 11.8604 1.48854 13.1867 2.81487L12.7153 3.28628L13.1867 2.81488C14.5131 4.14123 15.3346 5.97548 15.3346 8.00033C15.3346 10.0252 14.5131 11.8594 13.1867 13.1858C11.8604 14.5121 10.0261 15.3337 8.0013 15.3337C5.97645 15.3337 4.14221 14.5121 2.81585 13.1858L3.28726 12.7144L2.81585 13.1858C1.48951 11.8594 0.667969 10.0252 0.667969 8.00033C0.667969 5.97548 1.48951 4.14123 2.81585 2.81488L2.81585 2.81487ZM8.83398 4.50016C8.83398 4.03993 8.46088 3.66683 8.00065 3.66683C7.54042 3.66683 7.16732 4.03993 7.16732 4.50016C7.16732 4.9604 7.54042 5.3335 8.00065 5.3335C8.46088 5.3335 8.83398 4.9604 8.83398 4.50016ZM7.50065 6.16683C7.22451 6.16683 7.00065 6.39069 7.00065 6.66683C7.00065 6.94297 7.22451 7.16683 7.50065 7.16683H7.66732V10.8335H7.00065C6.72451 10.8335 6.50065 11.0574 6.50065 11.3335C6.50065 11.6096 6.72451 11.8335 7.00065 11.8335H8.16633L8.16732 11.8335L8.1683 11.8335H9.33398C9.61013 11.8335 9.83398 11.6096 9.83398 11.3335C9.83398 11.0574 9.61013 10.8335 9.33398 10.8335H8.66732V6.66683C8.66732 6.39069 8.44346 6.16683 8.16732 6.16683H7.50065Z",fill:"#4CB4FF"})]),e("defs",null,[e("clipPath",{id:"clip0_1940_6756"},[e("rect",{width:"16",height:"16",fill:"white"})])])],-1)),qa=Ge(()=>e("span",null,"文件学习完成后，即可支持知识问答，且不影响后台继续进行知识精炼和深度优化。",-1)),Ga={class:"card-content"},Qa={class:"flex items-center"},Ja={class:"status-texts mr-4"},Xa={class:"status-text-item"},Ya={class:"status-text-item"},el={class:"status-text-item"},tl={class:"status-nodes"},sl={class:"status-node-item"},ol={key:1},al={class:"status-node-item"},ll={key:1},nl={class:"status-node-item"},rl={key:1},il={class:"status-info w-full relative top-[9px]"},cl={class:"status-info-item"},dl={class:"status-title"},ul={class:"status-desc"},pl={class:"status-info-item"},vl={class:"status-title"},_l={class:"status-desc"},gl={class:"status-info-item"},fl={class:"status-title"},hl={class:"status-desc"},ml={name:"ProcessStatusCard"},Cl=ee({...ml,props:{visible:{type:Boolean,default:!1},level:{default:0},status:{default:""},triggerRect:{},progressData:{default:()=>({fileLearnProgress:100,knowledgeRefineProgress:20,deepOptimizeProgress:0})},row:{default:null}},emits:["update:hover","relearn"],setup(i,{emit:s}){const t=i,a=s,I=l(!1),u=l({}),h=()=>{a("update:hover",!0)},p=()=>{a("update:hover",!1)},m=ce(()=>{var y,b,z;return[{name:"文件学习",desc:"知识入库，构建基础索引能力",progress:w(1),progressValue:((y=t.progressData)==null?void 0:y.fileLearnProgress)||0},{name:"知识精炼",desc:"提炼知识要点，提升检索精度",progress:w(2),progressValue:((b=t.progressData)==null?void 0:b.knowledgeRefineProgress)||0},{name:"深度优化",desc:"智能标注数据，值得花点时间",progress:w(3),progressValue:((z=t.progressData)==null?void 0:z.deepOptimizeProgress)||0}]}),w=y=>{var P,g,V;const b=t.level;if(t.status==="fail")return"已失败";const L=y===1?(P=t.progressData)==null?void 0:P.fileLearnProgress:y===2?(g=t.progressData)==null?void 0:g.knowledgeRefineProgress:(V=t.progressData)==null?void 0:V.deepOptimizeProgress;return L===100?"已完成":L>0?"进行中":L===0||b===0?"等待中":b===1?y===1?"进行中":"等待中":b===2?y===1?"已完成":y===2?"进行中":"等待中":b===3?y<=2?"已完成":"进行中":b===4?"已完成":"等待中"},d=y=>{var z,L,P;const b=y===1?(z=t.progressData)==null?void 0:z.fileLearnProgress:y===2?(L=t.progressData)==null?void 0:L.knowledgeRefineProgress:(P=t.progressData)==null?void 0:P.deepOptimizeProgress;return t.status==="fail"?"已失败":b===100?"已完成":b>0?"进行中":"等待中"},f=y=>{var z,L,P;const b=y===1?(z=t.progressData)==null?void 0:z.fileLearnProgress:y===2?(L=t.progressData)==null?void 0:L.knowledgeRefineProgress:(P=t.progressData)==null?void 0:P.deepOptimizeProgress;return t.status==="fail"?"text-red-500":b===100?"text-green-500":b>0?"text-blue-500":"text-gray-400"},r=y=>{var z,L,P;const b=y===1?(z=t.progressData)==null?void 0:z.fileLearnProgress:y===2?(L=t.progressData)==null?void 0:L.knowledgeRefineProgress:(P=t.progressData)==null?void 0:P.deepOptimizeProgress;return t.status==="fail"?is:b===100?cs:(b>0||b===0,"text")},D=y=>{var z,L,P;const b=y===1?(z=t.progressData)==null?void 0:z.fileLearnProgress:y===2?(L=t.progressData)==null?void 0:L.knowledgeRefineProgress:(P=t.progressData)==null?void 0:P.deepOptimizeProgress;return t.status==="fail"?"bg-red-500 text-white":b===100?"bg-green-500 text-white":b>0?"bg-transparent border border-blue-500 text-blue-500":"bg-transparent border border-gray-400 text-gray-400"},R=(y,b)=>{var P,g,V,le,j,ve;const z=y===1?(P=t.progressData)==null?void 0:P.fileLearnProgress:y===2?(g=t.progressData)==null?void 0:g.knowledgeRefineProgress:(V=t.progressData)==null?void 0:V.deepOptimizeProgress,L=b===1?(le=t.progressData)==null?void 0:le.fileLearnProgress:b===2?(j=t.progressData)==null?void 0:j.knowledgeRefineProgress:b===3?(ve=t.progressData)==null?void 0:ve.deepOptimizeProgress:0;return t.status==="fail"?"bg-red-200":z===100&&L>0?"bg-blue-500":z===100?"bg-green-500":z>0?"bg-blue-500":"bg-gray-200"},W=()=>{if(!t.triggerRect)return;const y=360,b=310,z=window.innerHeight,L=window.innerWidth;let P=t.triggerRect.right-y/2+100,g=t.triggerRect.bottom+20;z-t.triggerRect.bottom<b+40?(g=t.triggerRect.top-b-30,I.value=!0):I.value=!1,P<8&&(P=8),P+y>L-8&&(P=L-y-8),u.value={left:`${Math.round(P)}px`,top:`${Math.round(g)}px`,position:"fixed",zIndex:"10000"}};let O=null;const A=()=>{O&&clearTimeout(O),O=setTimeout(()=>{t.visible&&t.triggerRect&&W()},50)};de(()=>t.triggerRect,()=>{A()},{deep:!0}),de(()=>t.visible,y=>{y&&A()}),qe(()=>{window.addEventListener("resize",A),window.addEventListener("scroll",A)}),xe(()=>{window.removeEventListener("resize",A),window.removeEventListener("scroll",A),O&&clearTimeout(O)});const X=ce(()=>{var b;return["a_fail","b_fail","c_fail"].includes((b=t.row)==null?void 0:b.stage_status)}),J=()=>{a("relearn",t.row)};return(y,b)=>{const z=H("el-icon");return c(),K(Ke,{name:"fade"},{default:S(()=>[y.visible?(c(),v("div",{key:0,class:"fixed z-[10000]",style:ut(u.value),onMouseenter:h,onMouseleave:p},[e("div",Aa,[e("div",Oa,[Ua,X.value?(c(),v("div",{key:0,title:"重新学习",class:"relearn-btn p-1 relative rounded top-[-6px] hover:bg-[#eaeaea] transition-colors duration-200",onClick:J},[_(z,null,{default:S(()=>[Ka]),_:1})])):E("",!0)]),e("div",Wa,[_(z,{class:"info-icon",size:"16"},{default:S(()=>[ja]),_:1}),qa]),e("div",Ga,[e("div",Qa,[e("div",Ja,[e("div",Xa,[e("span",{class:$(["status-text",f(1)])},F(d(1)),3)]),e("div",Ya,[e("span",{class:$(["status-text",f(2)])},F(d(2)),3)]),e("div",el,[e("span",{class:$(["status-text",f(3)])},F(d(3)),3)])]),e("div",tl,[e("div",sl,[e("div",{class:$(["status-node",D(1)])},[r(1)!=="text"?(c(),K(Ue(r(1)),{key:0,class:"status-icon"})):(c(),v("span",ol,"1"))],2)]),e("div",{class:$(["status-line",R(1,2)])},null,2),e("div",al,[e("div",{class:$(["status-node",D(2)])},[r(2)!=="text"?(c(),K(Ue(r(2)),{key:0,class:"status-icon"})):(c(),v("span",ll,"2"))],2)]),e("div",{class:$(["status-line",R(2,3)])},null,2),e("div",nl,[e("div",{class:$(["status-node",D(3)])},[r(3)!=="text"?(c(),K(Ue(r(3)),{key:0,class:"status-icon"})):(c(),v("span",rl,"3"))],2)])]),e("div",il,[e("div",cl,[e("div",dl,F(m.value[0].name)+" ("+F(m.value[0].progressValue)+"%) ",1),e("div",ul,F(m.value[0].desc),1)]),e("div",pl,[e("div",vl,F(m.value[1].name)+" ("+F(m.value[1].progressValue)+"%) ",1),e("div",_l,F(m.value[1].desc),1)]),e("div",gl,[e("div",fl,F(m.value[2].name)+" ("+F(m.value[2].progressValue)+"%) ",1),e("div",hl,F(m.value[2].desc),1)])])])])])],36)):E("",!0)]),_:1})}}}),wl=te(Cl,[["__scopeId","data-v-e3ae718b"]]),bl=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M16.0007 1.33331C15.2673 1.33331 14.6673 1.93331 14.6673 2.66665C14.6673 3.39998 15.2673 3.99998 16.0007 3.99998C21.854 3.99998 26.7273 8.19331 27.7873 13.7333L25.4873 12.58C24.8273 12.2533 24.0273 12.52 23.7006 13.1733C23.374 13.8333 23.6406 14.6333 24.294 14.96L28.7407 17.18C29.154 17.3866 29.6473 17.3666 30.0406 17.12C30.434 16.88 30.674 16.4466 30.674 15.9866C30.6673 7.89998 24.1006 1.33331 16.0007 1.33331ZM5.90732 9.13998C5.90732 7.35331 7.35398 5.89998 9.14732 5.89998C10.9407 5.89998 12.3873 7.34665 12.3873 9.13998C12.3873 10.9333 10.9407 12.38 9.14732 12.38C7.35398 12.38 5.90732 10.9333 5.90732 9.13998ZM9.14065 3.23998C5.88065 3.23998 3.24065 5.87998 3.24065 9.13998C3.24065 12.4 5.88732 15.0466 9.14732 15.0466C12.4073 15.0466 15.054 12.4 15.054 9.13998C15.054 5.87998 12.4073 3.23998 9.14065 3.23998ZM19.6206 22.86C19.6206 21.0733 21.0673 19.62 22.8607 19.62C24.654 19.62 26.1007 21.0666 26.1007 22.86C26.1007 24.6533 24.654 26.1 22.8607 26.1C21.0673 26.1 19.6206 24.6533 19.6206 22.86ZM22.854 16.9533C19.594 16.9533 16.9473 19.6 16.9473 22.86C16.9473 26.12 19.594 28.7666 22.854 28.7666C26.114 28.7666 28.7606 26.12 28.7606 22.86C28.7606 19.6 26.114 16.9533 22.854 16.9533ZM1.96732 14.8666C2.36065 14.6266 2.85398 14.6 3.26732 14.8066L7.71398 17.0266C8.37398 17.3533 8.64065 18.16 8.30732 18.8133C7.98065 19.4733 7.17398 19.74 6.52065 19.4066L4.22065 18.2533C5.28065 23.8 10.154 27.9866 16.0073 27.9866C16.7407 27.9866 17.3407 28.5866 17.3407 29.32C17.3407 30.0533 16.7407 30.6533 16.0073 30.6533C7.90065 30.6666 1.33398 24.1 1.33398 16C1.33398 15.54 1.57398 15.1066 1.96732 14.8666Z"})],-1),yl={name:"SyncIcon"},xl=ee({...yl,props:{color:{default:"#129BFE"},size:{default:32},className:{default:""}},setup(i){return(s,t)=>(c(),K(he,{color:s.color,size:s.size,class:$(s.className)},{default:S(()=>[bl]),_:1},8,["color","size","class"]))}}),kl={class:"relative inline-block overflow-visible"},$l={key:0,class:"absolute top-[4px] right-[12px] flex min-w-[16px] min-h-[16px] items-center justify-center rounded-full bg-[#FF4D4F] text-[10px] text-white px-1 py-0.5 font-medium"},Ll={name:"NotificationIcon"},zl=ee({...Ll,props:{count:{default:0},iconSize:{default:32},iconColor:{default:"#129BFE"},maxCount:{default:99}},emits:["click"],setup(i,{emit:s}){const t=i,a=s,I=ce(()=>t.count<=t.maxCount?t.count:`${t.maxCount}+`),u=()=>{a("click")};return(h,p)=>(c(),v("div",kl,[e("div",{class:"cursor-pointer transition-all duration-200 flex items-center justify-center w-[64px] h-[56px] bg-[#F7F9FC] rounded-[12px] border border-solid border-[#E5E5E5] hover:border-[#129BFE] shadow-[0px_3px_8px_0px_rgba(0,8,14,0.10)]",onClick:u},[_(xl,{size:h.iconSize,color:h.iconColor},null,8,["size","color"]),h.count>0?(c(),v("div",$l,F(I.value),1)):E("",!0)])]))}}),Il=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 17"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.81439 3.31453C4.14075 1.9882 5.97499 1.16666 7.99984 1.16666C10.0247 1.16666 11.8589 1.9882 13.1853 3.31454L12.7139 3.78594L13.1853 3.31454C14.5116 4.6409 15.3332 6.47514 15.3332 8.49999C15.3332 10.5248 14.5116 12.3591 13.1853 13.6854C11.8589 15.0118 10.0247 15.8333 7.99984 15.8333C5.97499 15.8333 4.14074 15.0118 2.81439 13.6854L3.28579 13.214L2.81438 13.6854C1.48805 12.3591 0.666504 10.5248 0.666504 8.49999C0.666504 6.47514 1.48805 4.6409 2.81438 3.31454L2.81439 3.31453ZM7.99984 2.49999C6.34282 2.49999 4.84373 3.17083 3.75719 4.25735C2.67067 5.34389 1.99984 6.84297 1.99984 8.49999C1.99984 10.157 2.67068 11.6561 3.7572 12.7426C4.84373 13.8291 6.34282 14.5 7.99984 14.5C9.65686 14.5 11.1559 13.8291 12.2425 12.7426C13.329 11.6561 13.9998 10.157 13.9998 8.49999C13.9998 6.84297 13.329 5.34388 12.2425 4.25735C11.1559 3.17083 9.65686 2.49999 7.99984 2.49999Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M11.8046 6.02861C12.0649 6.28896 12.0649 6.71107 11.8046 6.97141L7.80458 10.9714C7.54423 11.2318 7.12212 11.2318 6.86177 10.9714L4.86177 8.97142C4.60142 8.71107 4.60142 8.28896 4.86177 8.02861C5.12212 7.76826 5.54423 7.76826 5.80458 8.02861L7.33317 9.5572L10.8618 6.02861C11.1221 5.76826 11.5442 5.76826 11.8046 6.02861Z"})],-1),Dl={name:"CheckIcon"},Sl=ee({...Dl,props:{color:{default:"#52C668"},size:{default:26},className:{default:""}},setup(i){return(s,t)=>(c(),K(he,{color:s.color,size:s.size,class:$(s.className)},{default:S(()=>[Il]),_:1},8,["color","size","class"]))}}),Pl=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 17"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M0.666504 8.50002C0.666504 4.44993 3.94975 1.16669 7.99984 1.16669C12.0499 1.16669 15.3332 4.44993 15.3332 8.50002C15.3332 12.5501 12.0499 15.8334 7.99984 15.8334C3.94975 15.8334 0.666504 12.5501 0.666504 8.50002ZM7.99984 2.50002C4.68613 2.50002 1.99984 5.18631 1.99984 8.50002C1.99984 11.8137 4.68613 14.5 7.99984 14.5C11.3135 14.5 13.9998 11.8137 13.9998 8.50002C13.9998 5.18631 11.3135 2.50002 7.99984 2.50002Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M10.3571 6.14302C10.6174 6.40337 10.6174 6.82548 10.3571 7.08583L6.58583 10.8571C6.32548 11.1174 5.90337 11.1174 5.64302 10.8571C5.38267 10.5967 5.38267 10.1746 5.64302 9.91425L9.41425 6.14302C9.6746 5.88267 10.0967 5.88267 10.3571 6.14302Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M5.64302 6.14302C5.90337 5.88267 6.32548 5.88267 6.58583 6.14302L10.3571 9.91425C10.6174 10.1746 10.6174 10.5967 10.3571 10.8571C10.0967 11.1174 9.6746 11.1174 9.41425 10.8571L5.64302 7.08583C5.38267 6.82548 5.38267 6.40337 5.64302 6.14302Z"})],-1),Ml={name:"CircleDeleteIcon"},Fl=ee({...Ml,props:{color:{default:"#FB5454"},size:{default:26},className:{default:""}},setup(i){return(s,t)=>(c(),K(he,{color:s.color,size:s.size,class:$(s.className)},{default:S(()=>[Pl]),_:1},8,["color","size","class"]))}}),Tl=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 16 15"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M7.99984 0.166656C3.94975 0.166656 0.666504 3.4499 0.666504 7.49999C0.666504 11.5501 3.94975 14.8333 7.99984 14.8333C12.0499 14.8333 15.3332 11.5501 15.3332 7.49999C15.3332 3.4499 12.0499 0.166656 7.99984 0.166656ZM1.99984 7.49999C1.99984 4.18628 4.68613 1.49999 7.99984 1.49999C11.3135 1.49999 13.9998 4.18628 13.9998 7.49999C13.9998 10.8137 11.3135 13.5 7.99984 13.5C4.68613 13.5 1.99984 10.8137 1.99984 7.49999ZM8.66935 3.50007C8.66938 3.13188 8.37094 2.83337 8.00275 2.83333C7.63456 2.8333 7.33605 3.13174 7.33601 3.49993L7.33561 7.50287C7.33559 7.6797 7.40583 7.8493 7.53087 7.97434L10.3573 10.8008C10.6177 11.0612 11.0398 11.0612 11.3001 10.8008C11.5605 10.5405 11.5605 10.1183 11.3001 9.858L8.66897 7.22682L8.66935 3.50007Z"})],-1),Bl={name:"TimeIcon"},Oe=ee({...Bl,props:{color:{default:"#A2A9B0"},size:{default:26},className:{default:""}},setup(i){return(s,t)=>(c(),K(he,{color:s.color,size:s.size,class:$(s.className)},{default:S(()=>[Tl]),_:1},8,["color","size","class"]))}}),At=()=>({getStatusIcon:u=>({success:Sl,fail:Fl,doing:Oe,wait:Oe,split:Oe})[u]||Oe,getStatusIconClass:u=>({success:"text-[#52C668]",fail:"text-[#F56C6C]",doing:"text-[#409EFF]",wait:"text-[#E6A23C]",split:"text-[#909399]"})[u]||"",getStatusLabel:u=>({wait:"待学习",split:"分片完成",doing:"学习中",success:"学习成功",fail:"学习失败"})[u]||u,getProgressColor:(u,h)=>u==="fail"?"#F56C6C":h===100?"#67C23A":"#129BFE",getProcessLevel:u=>{if(u._processLevel!==void 0&&u._processStatus!==void 0)return{level:u._processLevel,status:u._processStatus};let h=0,p="success";if(u.percentage&&(u.percentage.percentage_c>0?h=3:u.percentage.percentage_b>0?h=2:u.percentage.percentage_a>0&&(h=1),u.percentage.percentage_a===100&&(h=Math.max(h,2)),u.percentage.percentage_b===100&&(h=Math.max(h,3)),u.percentage.percentage_c===100&&(h=4)),u.stage_status){const m=[u.stage_status.stage_status_a,u.stage_status.stage_status_b,u.stage_status.stage_status_c];m.some(w=>w==="失败")?p="fail":m.some(w=>w==="进行中")?p="doing":m.every(w=>w==="等待中")&&(p="wait")}return u._processLevel=h,u._processStatus=p,{level:h,status:p}}}),El={name:"LearningProgressList",props:{items:{type:Array,default:()=>[]}},emits:["close","retry-failed"],setup(i){debugger;const{getStatusLabel:s,getProgressColor:t,getProcessLevel:a}=At(),I=l(new Map),u=d=>{d.forEach(f=>{const r=I.value.get(f.id);r?(r.isFailed=f.status==="fail",r.status=f.status):I.value.set(f.id,{shouldFadeOut:!1,isComplete:!1,isFailed:f.status==="fail",status:f.status})})},h=ce(()=>i.items.filter(d=>{const f=I.value.get(d.id);return f?f.isRemoved?!1:d.status==="success"&&!f.shouldFadeOut?!0:d.status!=="success":!0}).map(d=>{var f;return{...d,shouldFadeOut:((f=I.value.get(d.id))==null?void 0:f.shouldFadeOut)||!1}})),p=ce(()=>i.items.some(d=>d.status==="fail")),m=async d=>{if(d.status==="success"){const f=I.value.get(d.id);f&&!f.isComplete&&(f.isComplete=!0,await ct(),setTimeout(()=>{f.shouldFadeOut=!0,setTimeout(()=>{f.isRemoved=!0},500)},500))}},w=d=>{var r;if(d.status==="fail")return"#FF4D4F";const{status:f}=a(d);return t(f,((r=d.percentage)==null?void 0:r.percentage_a)||0)};return de(()=>i.items,d=>{ct(()=>{u(d)})},{immediate:!0,deep:!0}),de(()=>i.items.map(d=>({id:d.id,status:d.status,percentage:d.percentage})),d=>{d.forEach(f=>{var r;f.status==="success"&&((r=f.percentage)==null?void 0:r.percentage_a)===100&&m(f)})},{deep:!0}),{getFileIcon:vt,hasFailedDocuments:p,getStatusLabel:s,getProgressBarColor:w,filteredItems:h,handleProgressComplete:m}}},Qe=i=>(ue("data-v-b9d8907a"),i=i(),pe(),i),Hl={class:"el-main-bottom"},Rl={class:"info-box w-full mb-4"},Vl=Qe(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},[e("g",{"clip-path":"url(#clip0_1940_6756)"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.81585 2.81487C4.14221 1.48854 5.97646 0.666992 8.0013 0.666992C10.0261 0.666992 11.8604 1.48854 13.1867 2.81487L12.7153 3.28628L13.1867 2.81488C14.5131 4.14123 15.3346 5.97548 15.3346 8.00033C15.3346 10.0252 14.5131 11.8594 13.1867 13.1858C11.8604 14.5121 10.0261 15.3337 8.0013 15.3337C5.97645 15.3337 4.14221 14.5121 2.81585 13.1858L3.28726 12.7144L2.81585 13.1858C1.48951 11.8594 0.667969 10.0252 0.667969 8.00033C0.667969 5.97548 1.48951 4.14123 2.81585 2.81488L2.81585 2.81487ZM8.83398 4.50016C8.83398 4.03993 8.46088 3.66683 8.00065 3.66683C7.54042 3.66683 7.16732 4.03993 7.16732 4.50016C7.16732 4.9604 7.54042 5.3335 8.00065 5.3335C8.46088 5.3335 8.83398 4.9604 8.83398 4.50016ZM7.50065 6.16683C7.22451 6.16683 7.00065 6.39069 7.00065 6.66683C7.00065 6.94297 7.22451 7.16683 7.50065 7.16683H7.66732V10.8335H7.00065C6.72451 10.8335 6.50065 11.0574 6.50065 11.3335C6.50065 11.6096 6.72451 11.8335 7.00065 11.8335H8.16633L8.16732 11.8335L8.1683 11.8335H9.33398C9.61013 11.8335 9.83398 11.6096 9.83398 11.3335C9.83398 11.0574 9.61013 10.8335 9.33398 10.8335H8.66732V6.66683C8.66732 6.39069 8.44346 6.16683 8.16732 6.16683H7.50065Z",fill:"#4CB4FF"})]),e("defs",null,[e("clipPath",{id:"clip0_1940_6756"},[e("rect",{width:"16",height:"16",fill:"white"})])])],-1)),Nl=Qe(()=>e("span",null,"您可以关闭弹窗做其他事，知识入库过程将在后台处理，完成后会有状态提示。文件学习成功后即可进行知识问答。",-1)),Zl={class:"content-wrapper"},Al={key:0,class:"empty-state"},Ol=Qe(()=>e("svg",{xmlns:"http://www.w3.org/2000/svg",width:"82",height:"82",viewBox:"0 0 82 82",fill:"none"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M44.7065 5.12492C44.7065 3.24575 43.169 1.70825 41.2898 1.70825C39.4107 1.70825 37.8732 3.24575 37.8732 5.12492V17.0833C37.8732 18.9624 39.4107 20.4999 41.2898 20.4999C43.169 20.4999 44.7065 18.9624 44.7065 17.0833V5.12492ZM12.3165 33.4662C12.6411 31.8774 14.0419 30.7499 15.6648 30.7499H66.9148C68.5378 30.7499 69.9215 31.8774 70.2632 33.4662L78.1898 50.8912C78.5657 51.3695 78.8048 51.9674 78.8732 52.5995C78.8732 52.7362 78.8903 52.8728 78.8903 53.0266V75.1666C78.8903 77.0458 77.3528 78.5833 75.4736 78.5833H7.12318C5.24401 78.5833 3.70651 77.0458 3.70651 75.1666V53.0266C3.70651 52.8899 3.70651 52.7533 3.72359 52.5995C3.79193 51.9674 4.03109 51.3695 4.40693 50.8912L12.3336 33.4662H12.3165ZM64.1473 37.5833L70.0753 49.5416H56.8186C55.3153 49.5416 53.9828 50.5324 53.5557 51.9674L51.1811 59.7916H31.3986L29.024 51.9674C28.5798 50.5324 27.2644 49.5416 25.7611 49.5416H12.5044L18.4323 37.5833H64.1303H64.1473ZM10.5398 56.3749V71.7499H72.0398V56.3749H59.3469L56.9723 64.1991C56.5282 65.6341 55.2128 66.6249 53.7094 66.6249H28.8532C27.3498 66.6249 26.0173 65.6341 25.5903 64.1991L23.2157 56.3749H10.5228H10.5398ZM79.1978 10.9503C80.5303 12.2828 80.5303 14.4524 79.1978 15.7849L70.7415 24.2412C69.409 25.5737 67.2394 25.5737 65.9069 24.2412C64.5744 22.9087 64.5744 20.7391 65.9069 19.4066L74.3632 10.9503C75.6957 9.61784 77.8653 9.61784 79.1978 10.9503ZM7.53318 11.2578C6.20068 9.92534 4.03109 9.92534 2.69859 11.2578C1.36609 12.5903 1.36609 14.7599 2.69859 16.0924L11.1548 24.5487C12.4873 25.8812 14.6569 25.8812 15.9894 24.5487C17.3219 23.2162 17.3219 21.0466 15.9894 19.7141L7.53318 11.2578Z",fill:"#C1C7CD"})],-1)),Ul=Qe(()=>e("p",{class:"empty-text"},"暂无文件",-1)),Kl={key:1,class:"upload-step3-file-list overflow-y"},Wl={class:"font-zhongcu overflow-one"},jl=["src"],ql={class:"font-zhongcu el-col-item-status mr-4"},Gl={class:"flex items-center gap-4"},Ql={class:"progress-wrapper"},Jl={class:"status-wrapper"},Xl={key:0,src:me,alt:"",class:"w-3.5 h-3.5"},Yl={class:"fixed-bottom-btns"};function en(i,s,t,a,I,u){const h=H("el-icon"),p=H("el-progress");return c(),v("div",Hl,[e("div",Rl,[_(h,{class:"info-icon",size:"16"},{default:S(()=>[Vl]),_:1}),Nl]),e("div",Zl,[a.filteredItems.length===0?(c(),v("div",Al,[_(h,{class:"empty-icon",size:"82"},{default:S(()=>[Ol]),_:1}),Ul])):(c(),v("div",Kl,[(c(!0),v(ge,null,ye(a.filteredItems,(m,w)=>{var d;return c(),v("div",{class:$(["el-col-item font14",{"fade-out":m.shouldFadeOut}]),key:m.id||w},[e("div",Wl,[e("img",{src:a.getFileIcon(m.title),alt:"file icon",class:"file-icon"},null,8,jl),G(" "+F(m.title),1)]),e("div",ql,[e("div",Gl,[e("div",Ql,[_(p,{percentage:((d=m.percentage)==null?void 0:d.percentage_a)||0,color:a.getProgressBarColor(m),"show-text":!0,"stroke-width":4,class:"flex-1",onProgressComplete:f=>a.handleProgressComplete(m)},null,8,["percentage","color","onProgressComplete"])]),e("div",Jl,[["wait","split","doing"].includes(m.status)?(c(),v("img",Xl)):E("",!0),G(" "+F(a.getStatusLabel(m.status)),1)])])])],2)}),128))]))]),Ee(e("div",Yl,[e("button",{class:"btn-base upload-btn active",onClick:s[0]||(s[0]=m=>i.$emit("retry-failed"))}," 重新学习 ")],512),[[ds,a.hasFailedDocuments]])])}const tn=te(El,[["render",en],["__scopeId","data-v-b9d8907a"]]),sn={name:"LearningProgressModal",components:{LearningProgressList:tn,ExpandIcon:je},props:{visible:{type:Boolean,default:!1},items:{type:Array,default:()=>[]}},emits:["close","retry-failed"],setup(i,{emit:s}){console.log("LearningProgressModal setup 被调用"),console.log("初始 props:",i);const t=Et(),a=l([]),I=l(null),u=l([]);de(()=>i.items,r=>{console.log("items 变化，新数据:",r),r&&r.length>0&&(u.value=r.map(D=>D.id),a.value=[...r],console.log("更新 documentIds:",u.value),console.log("更新 localItems:",a.value))},{immediate:!0,deep:!0});const h=async()=>{if(console.log("getDocumentsStatus 被调用, documentIds:",u.value),!u.value.length){console.log("documentIds 为空，跳过请求");return}try{console.log("调用 apiGetBatchDocumentsStatus, 参数:",{knowledgeId:t.params.id,documentIds:u.value});const r=await Be(t.params.id,u.value);if(console.log("API 响应:",r),r.data&&r.data.length>0){const D=r.data;a.value=D.map(R=>({...R,title:R.title+"."+R.type,status_display:p(R.status)})),console.log("更新后的 localItems:",a.value)}}catch(r){console.error("获取学习文档状态失败:",r)}},p=r=>({wait:"待学习",split:"分片完成",doing:"学习中",success:"学习成功",fail:"学习失败"})[r]||r,m=()=>{console.log("开始轮询"),h(),I.value=setInterval(h,2e3)},w=()=>{console.log("停止轮询"),I.value&&(clearInterval(I.value),I.value=null)},d=()=>{w(),s("close")},f=()=>{const r=a.value.filter(D=>D.status==="fail").map(D=>D.id);s("retry-failed",r)};return de(()=>i.visible,r=>{console.log("visible 变化:",r),console.log("当前 props.items:",i.items),r?m():w()}),xe(()=>{w()}),{localItems:a,handleClose:d,handleRetryFailed:f}}},on=i=>(ue("data-v-ac1c496c"),i=i(),pe(),i),an={class:"new-modal-overlay"},ln={class:"new-modal-container !w-[70vw]"},nn={class:"new-modal-top"},rn=on(()=>e("div",{class:"font18 font-semibold"},"文档学习进度",-1));function cn(i,s,t,a,I,u){const h=H("ExpandIcon"),p=H("LearningProgressList");return c(),v("div",an,[e("div",ln,[e("div",nn,[rn,_(h,{class:"cursor-pointer text-[#697077] hover:text-[#129BFE] transition-colors",onClick:a.handleClose},null,8,["onClick"])]),_(p,{items:a.localItems,onClose:a.handleClose,onRetryFailed:a.handleRetryFailed},null,8,["items","onClose","onRetryFailed"])])])}const dn=te(sn,[["render",cn],["__scopeId","data-v-ac1c496c"]]),gt=i=>(ue("data-v-4317a5e7"),i=i(),pe(),i),un={class:"h-full relative"},pn={key:0,class:"absolute top-[-30px] right-[0px] z-10"},vn={class:"flex items-center mb-6 pb-[12px] border-0 border-solid border-b border-[#E5E5E5]"},_n=gt(()=>e("span",{class:"ml-2 text-[18px] font-semibold text-[#121619] leading-[26px] font-[PingFang SC]"},"文档",-1)),gn={class:"flex items-center justify-between mb-[18px]"},fn=["disabled"],hn={key:0,class:"absolute top-[34px] left-0 w-full bg-white rounded-[6px] z-10 text-[#343A3F] overflow-hidden shadow-[0px_10px_44px_1px_rgba(0,_0,_0,_0.10)]"},mn={class:"flex items-center gap-3"},Cn={key:0,class:"absolute top-[36px] left-0 w-[112px] border border-solid border-[#E5E5E5] bg-white rounded-[6px] z-20 text-[14px] text-[#343A3F] overflow-hidden shadow-[0px_10px_44px_1px_rgba(0,_0,_0,_0.10)]"},wn={class:"rounded-[12px] border border-solid border-[#E5E5E5] bg-[#FFF] overflow-hidden h-[calc(100vh-340px)]"},bn={class:"flex items-center"},yn=["onClick"],xn={class:"flex items-center"},kn={class:"ml-1"},$n={class:"relative"},Ln=["title","onClick"],zn={class:"flex items-center space-x-2"},In=["onClick"],Dn=["onClick"],Sn={class:"flex items-center justify-end my-3 px-5 py-3"},Pn={key:1,class:"new-modal-overlay"},Mn={class:"new-modal-container"},Fn={class:"new-modal-top"},Tn={class:"font18 font-zhongcu"},Bn={class:"new-modal-center"},En={class:"new-modal-center-item"},Hn={class:"text-right text-[#999] text-[12px] mt-1"},Rn={class:"new-modal-bottom"},Vn={key:0,class:"new-modal-overlay"},Nn={class:"new-modal-top"},Zn=gt(()=>e("div",{class:"font18 font-semibold"},"自定义文本",-1)),An={key:0,class:"new-modal-overlay"},On={class:"new-modal-top"},Un=gt(()=>e("div",{class:"font18 font-semibold"},"上传本地文件",-1)),Kn={class:"mt-6"},Wn={key:0,class:"new-modal-overlay"},jn=!1,qn=ee({__name:"Docs",setup(i){const s=Et(),t=Bt(),a=ms(),I=Cs(),u=l([{name:"知识库（加载中...）",path:"/admin/knowledge"},{name:"文档"}]),h=o=>{u.value=[{name:`知识库（${o}）`,path:"/admin/knowledge"},{name:"文档"}]},p=l(!1);l(!0);const m=ce(()=>J.value.length===0),w=()=>{m.value||(U.value=!U.value)},d=async()=>{if(!s.params.id){console.log("No route id parameter found, skipping API call"),p.value=!1;return}try{const o=await We(s.params.id);if(o.data.error==="0"){I.setCurrentKnowledge(o.data),p.value=o.data.is_edit;const n=o.data.title;h(n)}else o.data.error==="1"?(x.error("知识库不存在"),p.value=!1):o.data.error==="2"||o.data.error==="403"?(x.error("无权限查看"),p.value=!1):(x.error(o.data.message||"获取知识库信息失败"),p.value=!1)}catch(o){console.error("获取知识库信息失败:",o),x.error("获取知识库信息失败"),p.value=!1}};de([()=>a.userInfo,()=>s.path],([o,n])=>{console.log("=== Watch triggered in Docs ==="),console.log("New user info:",o),console.log("New path:",n),n!=null&&n.includes("/docs")&&o&&d()},{immediate:!0}),St(()=>{console.log("=== Docs Component activated ==="),console.log("Current route:",s),s.path.includes("/docs")&&Q()});const f=l([]),r=l(1),D=l(15),R=l(0),W=l(!0),O=l(!1),A=l("永久删除文档"),X=l("该文档数据将被永久删除，不可恢复及撤销。确定要删除吗？"),J=l([]),y=l(null),b=l(null),z=l(!1),L=l(""),P=l(null),g=l(null),V=l(null),le=l(null),j=l(""),ve=l(500),$e=()=>{const o=window.innerHeight,n=422;ve.value=o-n},He=o=>{const n=["base","primary","advance"],M=["success","fail","doing","wait","split"],Z=[];for(let T=0;T<o;T++)Z.push({id:T+1,title:`测试文档 ${T+1}`,file_total_chars:Math.floor(Math.random()*1e4),fragment_count:Math.floor(Math.random()*100),learn_type:n[Math.floor(Math.random()*n.length)],status:M[Math.floor(Math.random()*M.length)],is_enabled:Math.random()>.5,create_time:new Date(Date.now()-Math.floor(Math.random()*1e10)).toISOString().split("T")[0],update_time:new Date(Date.now()-Math.floor(Math.random()*1e9)).toISOString().split("T")[0],description:Math.random()>.5?`这是第 ${T+1} 个文档的描述信息`:""});return Z},Je=l(!1),Q=async()=>{var o;W.value=!0;try{if(Je.value){const M=(r.value-1)*D.value,Z=M+D.value;let T=He(1e3);j.value&&(T=T.filter(q=>{var ne;return q.title.toLowerCase().includes(j.value.toLowerCase())||((ne=q.description)==null?void 0:ne.toLowerCase().includes(j.value.toLowerCase()))})),f.value=T.slice(M,Z),R.value=T.length,await new Promise(q=>setTimeout(q,300))}else{const n=await bs(s.params.id,r.value,D.value,j.value);n.data.error==="1"?x.error("知识库不存在"):n.data.error==="2"||n.data.error==="403"?x.error("无权限查看"):(f.value=n.data.data,R.value=((o=n.data.total_num)==null?void 0:o[0])||0)}}catch(n){console.error("获取文档列表失败:",n),x.error("获取文档列表失败")}finally{W.value=!1,It()}},Xe=o=>({base:"基础版",primary:"普通版",advance:"高级版"})[o]||o,{getStatusIcon:Re,getStatusIconClass:Le,getStatusLabel:ze,getProcessLevel:Ie}=At(),Ye=o=>{D.value=o,Q()},ft=o=>{r.value=o,Q()},ht=o=>{g.value=o,P.value=o.id,L.value=o.description||"",z.value=!0},C=async()=>{try{const o=await ys(s.params.id,P.value,L.value);o.data.error==="0"?(x.success("描述更新成功"),z.value=!1,g.value&&(g.value.description=L.value),Q()):x.error(o.data.message||"更新描述失败")}catch(o){console.error("更新描述失败:",o),x.error("更新描述失败")}},B=o=>{J.value=o.map(n=>n.id)},U=l(!1),Y=async()=>{U.value=!1,J.value.length>0?(A.value="批量删除文档",X.value="选中的文档将被永久删除，不可恢复及撤销。确定要删除吗？",b.value="batch",O.value=!0):x.error("请选择文档")},N=o=>{y.value=o.id,A.value="永久删除文档",X.value="该文档将被永久删除，不可恢复及撤销。确定要删除吗？",b.value="single",O.value=!0},De=()=>{O.value=!1,b.value=null},Ve=async()=>{if(b.value)try{const o=b.value==="single"?[y.value]:J.value,n=await ks({document_ids:o,kbase_id:s.params.id});n.data.error==="0"?(x.success("删除成功"),Q()):x.error(n.data.message||"删除失败")}catch(o){console.error("删除失败:",o),x.error("删除失败")}finally{O.value=!1,J.value=[],y.value=null,b.value=null}},Ot=async o=>{try{const n=await xs({kbase_id:s.params.id,document_ids:[o.id],is_enabled:o.is_enabled});n.data.error==="0"?(x.success(o.is_enabled?"启用成功":"禁用成功"),Q()):(o.is_enabled=!o.is_enabled,x.error(n.data.message||"操作失败"))}catch(n){console.error("状态修改失败:",n),o.is_enabled=!o.is_enabled,x.error("状态修改失败")}},mt=async o=>{V.value=fe.service({lock:!0,text:"正在提交",background:"rgba(0, 0, 0, 0.5)"});try{const n=await dt(s.params.id,o.id);n.data.error==="0"?(x.success("学习中，请稍后查看"),Q()):x.error(n.data.message||"重新学习失败")}catch(n){console.error("重新学习失败:",n),x.error("重新学习失败")}finally{V.value.close(),_e.value=!1}},Ct=o=>{const n=o.target;le.value&&!le.value.contains(n)&&et.value&&!et.value.contains(n)&&(U.value=!1,be.value=!1)},Ut=_t(()=>{r.value=1,Q()},300),be=l(!1),et=l(null),Se=l(!1),Pe=l(!1),Kt=()=>{be.value=!1,Se.value=!0},Wt=()=>{be.value=!1,Pe.value=!0},jt=()=>{t.push("/admin/knowledge")},wt=()=>{Se.value=!1,Pe.value=!1,t.push(`/admin/knowledge/detail/${s.params.id}/test`)},qt=o=>{t.push(`/admin/knowledge/detail/${s.params.id}/docfragments/${o.id}`)},_e=l(!1),bt=l(0),yt=l(""),xt=l(null),Me=l(null),kt=l(!1);let re=null;const Gt=o=>{var n,M,Z;return o?{fileLearnProgress:((n=o.percentage)==null?void 0:n.percentage_a)||0,knowledgeRefineProgress:((M=o.percentage)==null?void 0:M.percentage_b)||0,deepOptimizeProgress:((Z=o.percentage)==null?void 0:Z.percentage_c)||0}:{fileLearnProgress:0,knowledgeRefineProgress:0,deepOptimizeProgress:0}},Qt=(o,n)=>{if(re&&(clearTimeout(re),re=null),o){const M=Ie(n);bt.value=M.level,yt.value=M.status,Me.value=n,_e.value=!0}else re=setTimeout(()=>{kt.value?_e.value=!0:(_e.value=!1,Me.value=null)},1200)},Jt=o=>{kt.value=o,o?(clearTimeout(re),_e.value=!0):re=setTimeout(()=>{_e.value=!1,Me.value=null},200)},Xt=(o,n)=>{xt.value=o},$t=l(null),Yt=l(null),tt=o=>{const n=o.querySelector(".new-modal-container");if(!n)return;const M=document.querySelector(".absolute.top-\\[-30px\\].right-\\[0px\\]");if(!M)return;const Z=M.getBoundingClientRect(),T=n.getBoundingClientRect(),ne=(window.innerWidth-Z.right)/T.width*100;n.style.setProperty("--modal-end-x",`${ne}%`)},Lt=()=>{var o;if(((o=$t.value)==null?void 0:o.step)===2){x.warning("请等待文件上传完成后再操作");return}Pe.value=!1,Se.value=!1,Q()},Fe=l(0),es=async()=>{if(console.log("点击通知图标"),V.value){console.log("已有加载中的请求，跳过");return}try{V.value=fe.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"}),console.log("开始获取学习文档，知识库ID:",s.params.id);const o=await Be(s.params.id);if(console.log("API 响应完整数据:",o),o.data&&o.data.length>0){const n=o.data;console.log("处理前的文档数据:",n),ie.value=n.map(M=>({...M,title:M.title+"."+M.type,status_display:ze(M.status)})),console.log("最终设置的 learningItems:",ie.value),await ct(),Ne.value=!0,console.log("弹窗显示状态已设置为 true")}else console.log("API 返回空数据，response:",o),ie.value=[],x.info("暂无学习中的文档")}catch(o){console.error("获取学习文档状态失败，错误详情:",o),x.error("获取学习文档状态失败")}finally{V.value&&(V.value.close(),V.value=null)}},ie=l([]),Ne=l(!1),se=l(null),Ze=async()=>{try{if(console.log("开始获取学习文档状态, 当前路径:",s.path),s.path==="/admin/knowledge"){console.log("在知识库列表页面，停止轮询"),se.value&&clearInterval(se.value);return}console.log("调用 apiGetBatchDocumentsStatus, 知识库ID:",s.params.id);const o=await Be(s.params.id);if(console.log("API 响应数据:",o),o.data&&o.data.length>0){const n=o.data;ie.value=n.map(M=>({...M,title:M.title+"."+M.type,status_display:ze(M.status)})),console.log("处理后的 learningItems:",ie.value),Fe.value=n.length}else console.log("API 返回空数据"),ie.value=[],Fe.value=0}catch(o){console.error("获取学习文档状态失败:",o)}},ts=async o=>{V.value=fe.service({lock:!0,text:"正在提交",background:"rgba(0, 0, 0, 0.5)"});try{await Promise.all(o.map(n=>dt(s.params.id,n))),x.success("重新学习已开始，请稍后查看"),Q()}catch(n){console.error("重新学习失败:",n),x.error("重新学习失败")}finally{V.value.close()}},Ae=l(null),st=l([]),ot=l(!1),at=l(0),zt=async()=>{const o=Date.now();if(o-at.value<9e3){console.log(`轮询间隔过短，跳过本次轮询，距上次轮询: ${(o-at.value)/1e3}秒`);return}if(!(ot.value||!f.value.length||!s.path.includes("/docs"))){console.log("开始执行深度处理状态轮询",new Date().toLocaleTimeString()),ot.value=!0,at.value=o;try{const n=f.value.filter(Z=>{if(!Z.percentage)return!1;const{percentage_a:T=0,percentage_b:q=0,percentage_c:ne=0}=Z.percentage;return T<100||q<100||ne<100});if(!n.length){st.value=[];return}st.value=n,console.log("需要更新状态的文档数量:",n.length);const M=await Be(s.params.id,n.map(Z=>Z.id));M.data&&M.data.length>0&&(console.log("获取到状态更新文档数量:",M.data.length),M.data.forEach(Z=>{const T=f.value.findIndex(q=>q.id===Z.id);if(T!==-1){const q=f.value[T];q.status=Z.status,q.percentage=Z.percentage,q.stage_status=Z.stage_status,delete q._processLevel,delete q._processStatus}}))}catch(n){console.error("获取文档处理状态失败:",n)}finally{ot.value=!1,console.log("深度处理状态轮询结束",new Date().toLocaleTimeString())}}},It=()=>{lt(),setTimeout(()=>{zt(),Ae.value=setInterval(zt,1e4),console.log("深度处理状态轮询已启动",new Date().toLocaleTimeString())},1e3)},lt=()=>{Ae.value&&(clearInterval(Ae.value),Ae.value=null,console.log("深度处理状态轮询已停止",new Date().toLocaleTimeString()))},ss=l(null),os=l(null);qe(()=>{as(),d(),Q(),$e(),window.addEventListener("resize",$e),document.addEventListener("click",Ct),Ze(),se.value=setInterval(Ze,1e4)}),St(()=>{Ze(),se.value&&clearInterval(se.value),se.value=setInterval(Ze,1e4),It()}),us(()=>{se.value&&clearInterval(se.value),lt()}),xe(()=>{document.removeEventListener("click",Ct),window.removeEventListener("resize",$e),re&&clearTimeout(re),se.value&&clearInterval(se.value),lt(),ie.value=[],Fe.value=0,st.value=[]});const as=async()=>{try{const o=await We(s.params.id);o.data.error==="0"&&h(o.data.title)}catch(o){console.error("获取知识库名称失败:",o)}};return(o,n)=>{var Dt;const M=H("el-icon"),Z=H("el-input"),T=H("el-table-column"),q=H("el-switch"),ne=H("el-tooltip"),ls=H("el-table"),ns=H("el-pagination"),rs=ps("loading");return c(),v("div",un,[Fe.value>0?(c(),v("div",pn,[_(zl,{count:Fe.value,onClick:es},null,8,["count"])])):E("",!0),e("div",vn,[e("div",{class:"flex items-center cursor-pointer",onClick:jt},[_(Is,{size:24,color:"#121619"})]),_n]),e("div",gn,[e("div",{ref_key:"batchOperateRef",ref:le,class:$(["rounded-[6px] pt-[4px] pb-[4px] pl-3 pr-4 text-sm flex items-center leading-[22px] font-medium relative caozuo-div",{"caozuo-active":U.value,"cursor-not-allowed caozuo-disabled":J.value.length===0,"cursor-pointer":J.value.length>0}]),onClick:Te(w,["stop"]),disabled:J.value.length===0&&!p.value},[G(" 批量操作 "),_(M,{class:"ml-2 caozuo-up-icon",color:"#343A3F",size:"18"},{default:S(()=>[U.value?(c(),K(ae(vs),{key:0})):(c(),K(ae(_s),{key:1}))]),_:1}),U.value?(c(),v("div",hn,[E("",!0),e("button",{class:"w-full h-full bg-none border-0 py-1.5 px-3 bg-white flex items-center hover:bg-gray-100 transition-colors duration-200",style:ut({"border-top":"none"}),onClick:Te(Y,["stop"])},[_(Mt,{size:"16",class:"mr-2"}),G("删除 ")],4)])):E("",!0)],10,fn),e("div",mn,[_(Z,{modelValue:j.value,"onUpdate:modelValue":[n[2]||(n[2]=k=>j.value=k),ae(Ut)],placeholder:"请输入内容",class:"w-[280px] !h-[32px] rounded-[6px]"},{prefix:S(()=>[_(zs,{size:"16",color:"#999"})]),_:1},8,["modelValue","onUpdate:modelValue"]),e("div",{ref_key:"uploadBtnRef",ref:et,class:"relative"},[e("button",{class:$(["h-[32px] min-w-[110px] bg-[#129BFE] rounded-[6px] px-[14px] text-white text-sm border-0 flex items-center gap-[6px] font-medium hover:bg-[#118AE3] transition-colors duration-200",{"opacity-50 cursor-not-allowed":!p.value,"hover:bg-[#118AE3]":p.value}]),onClick:n[3]||(n[3]=Te(k=>p.value&&(be.value=!be.value),["stop"]))},[_($s,{size:"16",color:"#fff"}),G(" 上传文档 ")],2),be.value&&p.value?(c(),v("div",Cn,[e("button",{class:"w-full h-full bg-none border-0 py-3 px-3 bg-white flex items-center hover:bg-gray-100 transition-colors duration-200",onClick:Te(Kt,["stop"])}," 自定义文本 "),e("button",{class:"w-full h-full bg-none border-0 py-3 px-3 bg-white flex items-center hover:bg-gray-100 transition-colors duration-200",onClick:Te(Wt,["stop"])}," 本地文件上传 ")])):E("",!0)],512)])]),e("div",wn,[Ee((c(),K(ls,{data:f.value,style:{width:"100%"},class:"rounded-lg","max-height":ve.value,"element-loading-text":"加载中...","element-loading-background":"rgba(255, 255, 255, 0.9)","header-cell-style":{background:"#f5f7fa",color:"#121619",fontWeight:"bold"},onSelectionChange:B},{default:S(()=>[_(T,{type:"selection",width:"40",fixed:!0,selectable:k=>p.value},null,8,["selectable"]),_(T,{label:"文件名称","min-width":"200",prop:"title"},{default:S(k=>[e("div",bn,[e("span",{class:"text-[14px] cursor-pointer hover:text-[#129BFE] hover:underline font-['PingFang_SC'] leading-[22px]",onClick:oe=>qt(k.row)},F(k.row.title)+"."+F(k.row.type),9,yn)])]),_:1}),_(T,{prop:"file_total_chars",label:"字符数",width:"100"}),_(T,{prop:"fragment_count",label:"分段",width:"100"}),_(T,{label:"学习方式",width:"120",prop:"learn_type"},{default:S(k=>[e("span",null,F(Xe(k.row.learn_type)),1)]),_:1}),_(T,{label:"文件状态",width:"120",prop:"status"},{default:S(k=>[e("div",xn,[_(M,{class:$([ae(Le)(k.row.status),"mr-1"])},{default:S(()=>[(c(),K(Ue(ae(Re)(k.row.status)),{size:16}))]),_:2},1032,["class"]),e("span",kn,F(ae(ze)(k.row.status)),1)])]),_:1}),_(T,{label:"深度处理",width:"100",prop:"process_level"},{default:S(k=>[e("div",$n,[_(Ns,{level:ae(Ie)(k.row).level,status:ae(Ie)(k.row).status,"onUpdate:hover":oe=>Qt(oe,k.row),"onUpdate:rect":oe=>Xt(oe,k.row)},null,8,["level","status","onUpdate:hover","onUpdate:rect"])])]),_:1}),jn?(c(),K(T,{key:0,label:"启用状态",width:"120",prop:"is_enabled"},{default:S(k=>[_(q,{modelValue:k.row.is_enabled,"onUpdate:modelValue":oe=>k.row.is_enabled=oe,onChange:oe=>Ot(k.row),disabled:!p.value},null,8,["modelValue","onUpdate:modelValue","onChange","disabled"])]),_:1})):E("",!0),_(T,{label:"创建时间",width:"180",prop:"create_time"},{default:S(k=>[G(F(k.row.create_time),1)]),_:1}),_(T,{label:"更新时间",width:"180",prop:"update_time"},{default:S(k=>[G(F(k.row.update_time),1)]),_:1}),_(T,{label:"描述",width:"80",align:"center",prop:"description"},{default:S(k=>[e("div",{title:k.row.description||"暂无描述",class:"cursor-pointer",onClick:oe=>ht(k.row)},[_(Ps)],8,Ln)]),_:1}),_(T,{label:"操作",width:"80",fixed:"right",prop:"operation"},{default:S(k=>[e("div",zn,[["a_fail","b_fail","c_fail"].includes(k.row.stage_status)?(c(),K(ne,{key:0,content:"重新学习",placement:"top"},{default:S(()=>[e("div",{class:$(["cursor-pointer text-[#A2A9B0] hover:text-[#343A3F]",{"opacity-50 cursor-not-allowed":!p.value}]),onClick:oe=>p.value&&mt(k.row)},[_(Ls,{size:16,color:"#333333"})],10,In)]),_:2},1024)):E("",!0),_(ne,{content:"删除",placement:"top"},{default:S(()=>[e("div",{class:$(["cursor-pointer text-[#A2A9B0] hover:text-[#343A3F]",{"opacity-50 cursor-not-allowed":!p.value}]),onClick:oe=>p.value&&N(k.row)},[_(Mt,{size:16,color:"#333333"})],10,Dn)]),_:2},1024)])]),_:1})]),_:1},8,["data","max-height"])),[[rs,W.value]]),e("div",Sn,[_(ns,{"current-page":r.value,"onUpdate:currentPage":n[4]||(n[4]=k=>r.value=k),"page-size":D.value,"onUpdate:pageSize":n[5]||(n[5]=k=>D.value=k),"page-sizes":[15,30,50,100],total:R.value,onSizeChange:Ye,onCurrentChange:ft,layout:"total, sizes, prev, pager, next, jumper",background:""},null,8,["current-page","page-size","total"])])]),_(ws,{show:O.value,title:A.value,message:X.value,onClose:De,onConfirm:Ve},null,8,["show","title","message"]),z.value?(c(),v("div",Pn,[e("div",Mn,[e("div",Fn,[e("div",Tn,F((Dt=g.value)!=null&&Dt.description?"修改文档描述":"添加文档描述"),1)]),e("div",Bn,[e("div",En,[Ee(e("textarea",{class:"field-ipt field-txt font14 font-zhongcu","onUpdate:modelValue":n[6]||(n[6]=k=>L.value=k),placeholder:"请输入文档描述（支持markdown格式）",maxlength:"400"},null,512),[[it,L.value]]),e("div",Hn,F(L.value.length)+"/400 ",1)])]),e("div",Rn,[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:n[7]||(n[7]=k=>z.value=!1)}," 取消 "),e("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu common-confirm-btn",onClick:C}," 确认 ")])])])):E("",!0),_(Ke,{name:"modal-zoom",onBeforeLeave:tt},{default:S(()=>[Se.value?(c(),v("div",Vn,[e("div",{class:"new-modal-container !w-[70vw]",ref_key:"customTextModalRef",ref:ss},[e("div",Nn,[Zn,_(je,{class:"cursor-pointer text-[#697077] hover:text-[#129BFE] transition-colors",onClick:Lt})]),_(Bo,{"kbase-id":String(ae(s).params.id),onClose:n[8]||(n[8]=k=>Se.value=!1),onSuccess:Q,onGoTest:wt},null,8,["kbase-id"])],512)])):E("",!0)]),_:1}),_(Ke,{name:"modal-zoom",onBeforeLeave:tt},{default:S(()=>[Pe.value?(c(),v("div",An,[e("div",{class:"new-modal-container !w-[70vw]",ref_key:"modalContainerRef",ref:Yt},[e("div",On,[Un,_(je,{class:"cursor-pointer text-[#697077] hover:text-[#129BFE] transition-colors",onClick:Lt})]),_(Za,{ref_key:"localFileUploadRef",ref:$t,"kbase-id":String(ae(s).params.id),onClose:n[9]||(n[9]=k=>Pe.value=!1),onSuccess:Q,onGoTest:wt},null,8,["kbase-id"])],512)])):E("",!0)]),_:1}),e("div",Kn,[_(pt,{breadcrumbs:u.value},null,8,["breadcrumbs"])]),_(wl,{visible:_e.value,level:bt.value,status:yt.value,"trigger-rect":xt.value,"progress-data":Gt(Me.value),row:Me.value,"onUpdate:hover":Jt,onRelearn:mt},null,8,["visible","level","status","trigger-rect","progress-data","row"]),_(Ke,{name:"modal-zoom",onBeforeLeave:tt},{default:S(()=>[Ne.value?(c(),v("div",Wn,[e("div",{class:"new-modal-container",ref_key:"learningProgressModalRef",ref:os},[_(dn,{visible:Ne.value,items:ie.value,onClose:n[10]||(n[10]=()=>{Ne.value=!1,Q()}),onRetryFailed:ts},null,8,["visible","items"])],512)])):E("",!0)]),_:1})])}}}),t1=te(qn,[["__scopeId","data-v-4317a5e7"]]);export{t1 as default};
