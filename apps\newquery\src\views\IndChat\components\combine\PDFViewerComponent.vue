<template>
  <div class="relative">
    <div
      v-if="isLoading2"
      class="absolute inset-0 bg-white/90 z-50 flex items-center justify-center"
    >
      <div class="flex flex-col items-center gap-2">
        <div
          class="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"
        ></div>
        <span class="text-gray-600">加载中...</span>
      </div>
    </div>
    <div v-if="data.title" class="text-base font-medium mt-4 mb-2 text-black">
      {{ getFormattedTitle() }}
    </div>
    <div
      class="w-full h-full flex flex-col bg-gray-100 rounded-lg max-h-[calc(100vh-130px)]"
    >
      <!-- 头部控制栏 -->
      <div
        class="flex justify-between items-center px-4 py-3 header-gradient text-gray-700 rounded-t-lg"
      >
        <template v-if="isLoading">
          <div class="flex items-center">
            <el-icon class="animate-spin mr-2"><Loading /></el-icon>
            加载中...
          </div>
        </template>

        <template v-else-if="loadError">
          <div class="text-red-500">文件加载失败</div>
        </template>

        <template v-else>
          <div class="flex justify-between items-center w-full">
            <div class="flex items-center gap-4">
              <template v-if="!showAllPages">
                <div class="flex items-center gap-2">
                  <el-button
                    :disabled="currentPage <= 1"
                    @click="currentPage--"
                    size="small"
                    class="!text-gray-600"
                  >
                    ❮
                  </el-button>
                  <span>{{ currentPage }} / {{ totalPages }}</span>
                  <el-button
                    :disabled="currentPage >= totalPages"
                    @click="currentPage++"
                    size="small"
                    class="!text-gray-600"
                  >
                    ❯
                  </el-button>
                </div>
              </template>
              <template v-else>
                <span>共 {{ totalPages }} 页</span>
              </template>
            </div>

            <div class="flex items-center">
              <el-checkbox v-model="showAllPages" class="!text-gray-200">
                显示全部页面
              </el-checkbox>
              <el-button
                v-if="props.data.hideFullscreenButton !== true"
                @click="isFullscreen = true"
                size="small"
                class="ml-2 !text-gray-600 !px-1.5"
              >
                <FullscreenIcon />
              </el-button>
            </div>
          </div>
        </template>
      </div>

      <!-- PDF 内容区域 -->
      <div class="flex-1 p-4 overflow-auto" ref="pdfContainer">
        <template v-if="loadError">
          <div class="flex items-center justify-center h-full">
            <div class="text-red-500 text-lg">
              <template v-if="!isFileTypeSupported">
                不支持预览该文件格式，目前仅支持 PDF、PPT、Word 文档格式
              </template>
              <template v-else>
                文件加载失败，请检查文件格式或网络连接
              </template>
            </div>
          </div>
        </template>
        <template v-else-if="isLoading || isConverting">
          <div class="flex items-center justify-center h-full">
            <div class="flex items-center text-gray-600">
              <el-icon class="animate-spin mr-2"><Loading /></el-icon>
              文档加载中...
            </div>
          </div>
        </template>
        <template v-else-if="!isConverting && pdfUrl">
          <div class="max-w-4xl mx-auto max-h-[calc(100vh-200px)]">
            <vue-pdf-embed
              :key="pdfKey"
              :source="pdfUrl"
              :height="normalPdfHeight"
              :page="showAllPages ? null : currentPage"
              :image-resources-path="imageResourcesPath"
              @password-requested="handlePasswordRequest"
              @loaded="handleDocumentLoad"
              @rendered="handleDocumentRender"
              @internal-link-clicked="handleInternalLinkClicked"
              class="pdf-container"
            />
          </div>
        </template>
        <!-- Markdown预览区域 -->
        <template v-else-if="isMarkdownFile && markdownContent">
          <div
            class="max-w-4xl mx-auto bg-white rounded-lg p-6 shadow-lg overflow-auto markdown-content"
          >
            <pre
              class="whitespace-pre-wrap text-gray-800 font-mono text-sm leading-relaxed"
              >{{ markdownContent }}</pre
            >
          </div>
        </template>
      </div>
    </div>

    <!-- 全屏弹窗 -->
    <el-dialog
      v-model="isFullscreen"
      :title="getFormattedTitle() || '文档预览'"
      fullscreen
      :show-close="true"
      :close-on-click-modal="false"
      :close-on-press-escape="true"
      class="pdf-fullscreen-dialog"
    >
      <div class="h-full flex flex-col bg-gray-100">
        <!-- 复用相同的组件，但传入不同的样式和配置 -->
        <div
          class="flex justify-between items-center px-4 py-3 header-gradient text-gray-700"
        >
          <template v-if="isLoading">
            <div class="flex items-center">
              <el-icon class="animate-spin mr-2"><Loading /></el-icon>
              加载中...
            </div>
          </template>

          <template v-else-if="loadError">
            <div class="text-red-500">文件加载失败</div>
          </template>

          <template v-else>
            <div class="flex justify-between items-center w-full">
              <div class="flex items-center gap-4">
                <template v-if="!showAllPages">
                  <div class="flex items-center gap-2">
                    <el-button
                      :disabled="currentPage <= 1"
                      @click="currentPage--"
                      size="small"
                      class="!text-gray-600"
                    >
                      ❮
                    </el-button>
                    <span>{{ currentPage }} / {{ totalPages }}</span>
                    <el-button
                      :disabled="currentPage >= totalPages"
                      @click="currentPage++"
                      size="small"
                      class="!text-gray-600"
                    >
                      ❯
                    </el-button>
                  </div>
                </template>
                <template v-else>
                  <span>共 {{ totalPages }} 页</span>
                </template>
              </div>

              <div class="flex items-center">
                <el-checkbox v-model="showAllPages" class="!text-gray-200">
                  显示全部页面
                </el-checkbox>
                <!-- <el-button
                  @click="isFullscreen = false"
                  size="small"
                  class="ml-2 !text-gray-600"
                >
                  <el-icon><Close /></el-icon>
                </el-button> -->
              </div>
            </div>
          </template>
        </div>

        <!-- PDF 内容区域 -->
        <div
          class="flex-1 p-4 overflow-auto flex justify-center"
          ref="fullscreenPdfContainer"
        >
          <template v-if="loadError">
            <div class="flex items-center justify-center h-full">
              <div class="text-red-500 text-lg">
                <template v-if="!isFileTypeSupported">
                  不支持预览该文件格式，目前仅支持 PDF、PPT、Word 文档格式
                </template>
                <template v-else>
                  文件加载失败，请检查文件格式或网络连接
                </template>
              </div>
            </div>
          </template>
          <template v-else-if="isLoading || isConverting">
            <div class="flex items-center justify-center h-full">
              <div class="flex items-center text-gray-600">
                <el-icon class="animate-spin mr-2"><Loading /></el-icon>
                文档加载中...
              </div>
            </div>
          </template>
          <template v-else-if="!isConverting && pdfUrl">
            <div class="pdf-content-wrapper mx-auto max-h-[calc(100vh-156px)]">
              <vue-pdf-embed
                :key="fullscreenPdfKey"
                :source="pdfUrl"
                :page="showAllPages ? null : currentPage"
                :image-resources-path="imageResourcesPath"
                :height="pdfHeight"
                @password-requested="handlePasswordRequest"
                @loaded="handleDocumentLoad"
                @rendered="handleDocumentRender"
                @internal-link-clicked="handleInternalLinkClicked"
                class="fullscreen-pdf"
              />
            </div>
          </template>
          <!-- 全屏Markdown预览 -->
          <template v-else-if="isMarkdownFile && markdownContent">
            <div
              class="max-w-4xl mx-auto bg-white rounded-lg p-6 shadow-lg overflow-auto markdown-content max-h-[calc(100vh-156px)]"
            >
              <pre
                class="whitespace-pre-wrap text-gray-800 font-mono text-sm leading-relaxed"
                >{{ markdownContent }}</pre
              >
            </div>
          </template>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick, computed, onUnmounted } from "vue";
import VuePdfEmbed from "vue-pdf-embed";
import { Loading, Close } from "@element-plus/icons-vue";
import FullscreenIcon from "@/components/icons/FullscreenIcon.vue";
import type { PropType } from "vue";
import { useConfig } from "@/hooks/useConfig";
// 导入编码工具类
import { utf8ToBase64 } from "@/utils/encoding";
// 导入必要的样式
import "vue-pdf-embed/dist/styles/annotationLayer.css";
import "vue-pdf-embed/dist/styles/textLayer.css";
import { convertFile } from "@/services/intellido";

// 定义支持的文件格式列表，方便后续扩展
const SUPPORTED_FILE_TYPES = ["pdf", "ppt", "pptx", "doc", "docx"];

// 添加 UTF-8 转 base64 的函数
function utf8_to_b64(str: string) {
  // 使用工具类中的方法
  return utf8ToBase64(str);
}

// 获取格式化的文件标题，如果没有后缀则添加文件类型作为后缀
const getFormattedTitle = (): string => {
  if (!props.data.title) return "";

  const { title, fileType } = props.data;

  // 如果没有文件类型或标题已经包含后缀，则直接返回
  if (!fileType || title.toLowerCase().endsWith(`.${fileType.toLowerCase()}`)) {
    return title;
  }

  // 否则添加后缀
  return `${title}.${fileType}`;
};

interface PDFViewerProps {
  fileUrl: string;
  fileType?: string;
  initialPage?: number;
  title?: string;
  highlights?: {
    page: number;
    texts: string[];
  }[];
  hideFullscreenButton?: boolean; // 是否隐藏全屏按钮
  defaultShowAllPages?: boolean; // 是否默认显示全部页面
}

const props = defineProps({
  data: {
    type: Object as PropType<PDFViewerProps>,
    required: true,
  },
});

const isLoading = ref(true);
const isLoading2 = ref(true);
const isConverting = ref(true);
const currentPage = ref(props.data.initialPage || 1);
const totalPages = ref(0);
const showAllPages = ref(props.data.defaultShowAllPages || false);
const pdfContainer = ref<HTMLElement | null>(null);
const pdfUrl = ref("");
const loadError = ref(false);
const isInitialLoad = ref(true); // 添加初始加载标志
const markdownContent = ref(""); // 存储Markdown内容

// 设置图片资源路径
const imageResourcesPath = ""; //"https://unpkg.com/pdfjs-dist/web/images/";

// 添加全屏状态控制
const isFullscreen = ref(false);
const fullscreenPdfContainer = ref<HTMLElement | null>(null);

// 添加 PDF 组件的 key 控制
const pdfKey = ref(`pdf-${Date.now()}`);
const fullscreenPdfKey = ref(`pdf-fullscreen-${Date.now()}`);

// 添加动态高度计算
const screenHeight = ref(window.innerHeight);
const pdfHeight = computed(() => Math.floor(screenHeight.value * 0.78));

// 添加 PPT 文件类型判断
const pptExtensions = ["ppt", "pptx"];
const isPptFile = computed(() => {
  const fileExt =
    props.data.fileType || props.data.fileUrl.toLowerCase().split(".").pop();
  return pptExtensions.includes(fileExt);
});

// 判断文件类型是否支持
const isFileTypeSupported = computed(() => {
  const fileExt = (
    props.data.fileType ||
    props.data.fileUrl.toLowerCase().split(".").pop() ||
    ""
  ).toLowerCase();

  return SUPPORTED_FILE_TYPES.includes(fileExt);
});

// 判断是否为Markdown文件
const isMarkdownFile = computed(() => {
  const fileExt = (
    props.data.fileType ||
    props.data.fileUrl.toLowerCase().split(".").pop() ||
    ""
  ).toLowerCase();

  return fileExt === "md";
});

// 添加非全屏状态下的高度计算
const normalPdfHeight = computed(() => {
  if (isPptFile.value) {
    return 0; // PPT 文件保持原有高度
  }

  return 0;
  // return Math.floor(screenHeight.value * 0.72);
});

// 监听窗口大小变化
const handleResize = () => {
  screenHeight.value = window.innerHeight;
};

onMounted(() => {
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
});

// 添加全屏状态监听
watch(isFullscreen, async (newValue) => {
  if (newValue) {
    // 进入全屏时，等待 DOM 更新
    await nextTick();
    // 只更新全屏PDF的key
    fullscreenPdfKey.value = `pdf-fullscreen-${Date.now()}`;
    // 确保在全屏模式下保持当前页码
    await nextTick();
    setTimeout(highlightTextsOnCurrentPage, 100);
  }
});

// 监听页码变化，处理高亮
watch(currentPage, async (newPage, oldPage) => {
  // 如果是从 0 切换回来的，不处理
  if (oldPage === 0) return;

  await nextTick();
  highlightTextsOnCurrentPage();
});

// 检查文件类型并在需要时进行转换
const checkAndConvertFile = async () => {
  // 获取文件类型
  const fileExt = (
    props.data.fileType ||
    props.data.fileUrl.toLowerCase().split(".").pop() ||
    ""
  ).toLowerCase();

  // 检查文件类型是否支持
  if (!SUPPORTED_FILE_TYPES.includes(fileExt)) {
    console.error("[PDF预览] 不支持的文件格式:", fileExt);
    loadError.value = true;
    isConverting.value = false;
    isLoading.value = false;
    isLoading2.value = false;
    return;
  }

  // 如果是Markdown文件，直接获取内容
  if (fileExt === "md") {
    try {
      isLoading.value = true;
      isLoading2.value = true;
      loadError.value = false;
      console.log("[Markdown预览] 开始加载Markdown文件:", props.data.fileUrl);

      try {
        // 使用fetch获取Markdown内容
        const response = await fetch(props.data.fileUrl);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        markdownContent.value = await response.text();

        // 完成加载
        isConverting.value = false;
        isLoading.value = false;
        isLoading2.value = false;

        // 设置总页数为1（Markdown没有分页概念）
        totalPages.value = 1;
        currentPage.value = 1;
      } catch (error) {
        console.error("[Markdown预览] 加载错误:", error);
        loadError.value = true;
        isConverting.value = false;
        isLoading2.value = false;
        isLoading.value = false;
      }
      return;
    } catch (error) {
      console.error("[Markdown预览] 发生错误:", error);
      loadError.value = true;
      isConverting.value = false;
      isLoading2.value = false;
      isLoading.value = false;
      return;
    }
  }

  if (fileExt !== "pdf") {
    try {
      isLoading.value = true;
      isLoading2.value = true;
      loadError.value = false;
      console.log("[PDF转换] 开始转换文件:", props.data.fileUrl);

      // 先进行 base64 编码，再进行 URL 编码
      const base64Url = utf8_to_b64(props.data.fileUrl);
      const encodedUrl = encodeURIComponent(base64Url);

      try {
        pdfUrl.value = convertFile({ url: encodedUrl });
        isConverting.value = false;

        // debugger;
        // const result = await convertFile({ url: encodedUrl });
        // console.log("[PDF转换] 解析响应数据", result);

        // if (result.data && result.data.converted_url) {
        //   console.log("[PDF转换] 转换成功");
        //   pdfUrl.value = result.data.converted_url;
        //   isConverting.value = false;
        // } else {
        //   console.error("[PDF转换] 转换失败:", result.data.message);
        //   throw new Error(result.data.message || "文件转换失败");
        // }
      } catch (error) {
        console.error("[PDF转换] 发生错误:", error);
        loadError.value = true;
        isConverting.value = false;
        isLoading2.value = false;
        throw error;
      }
    } catch (error) {
      console.error("[PDF转换] 发生错误:", error);
      loadError.value = true;
      isConverting.value = false;
      isLoading2.value = false;
    } finally {
      isLoading.value = false;
    }
  } else {
    console.log("[PDF加载] 直接加载PDF文件");
    pdfUrl.value = props.data.fileUrl;
    isConverting.value = false;
    isLoading.value = false;
    isLoading2.value = false;
  }
};

// 监听显示模式变化
watch(showAllPages, async (newValue) => {
  if (!newValue) {
    currentPage.value = 1;
  }
  // 等待页面渲染完成后再处理
  await nextTick();
  setTimeout(highlightTextsOnCurrentPage, 500);
});

// 处理文档内部链接点击
const handleInternalLinkClicked = (page: number) => {
  currentPage.value = page;
};

// 当前页面的文字
const highlightTextsOnCurrentPage = async () => {
  if (!props.data.highlights) return;

  // 确保等待 DOM 更新完成
  await nextTick();

  // 获取当前激活的容器
  const activeContainer = isFullscreen.value
    ? fullscreenPdfContainer.value
    : pdfContainer.value;
  if (!activeContainer) return;

  const textLayers = showAllPages.value
    ? Array.from(activeContainer.querySelectorAll(".textLayer") || [])
    : [activeContainer.querySelector(".textLayer")].filter(Boolean);

  // 清除所有页面的高亮
  textLayers.forEach((textLayer) => {
    const oldHighlights = textLayer.querySelectorAll(".highlight-text");
    oldHighlights.forEach((el) => {
      el.classList.remove("highlight-text");
    });
  });

  // 处理每个页面的高亮
  textLayers.forEach((textLayer, index) => {
    const pageNumber = showAllPages.value ? index + 1 : currentPage.value;
    const currentHighlights = props.data.highlights?.find(
      (h) => h.page === pageNumber
    );
    if (!currentHighlights || !textLayer) return;

    const textElements = Array.from(textLayer.querySelectorAll("span"));

    // 对每个要高亮的文本进行处理
    currentHighlights.texts.forEach((highlightText) => {
      // 将文本转换为小写以进行不区分大小写的匹配
      const lowercaseHighlight = highlightText.toLowerCase().trim();

      // 用于存储连续匹配的文本节点
      let consecutiveNodes: Element[] = [];
      let currentText = "";

      // 遍历所有文本节点
      textElements.forEach((element, index) => {
        const text = element.textContent || "";

        // 检查单个节点是否包含目标文本
        if (text.toLowerCase().includes(lowercaseHighlight)) {
          element.classList.add("highlight-text");
          return;
        }

        // 处理可能跨节点的文本
        consecutiveNodes.push(element);
        currentText += text;

        // 规范化文本，移除多余空格
        const normalizedText = currentText
          .replace(/\s+/g, " ")
          .toLowerCase()
          .trim();

        // 检查累积的文本是否包含目标文本
        if (normalizedText.includes(lowercaseHighlight)) {
          // 所有相关节点
          consecutiveNodes.forEach((node) => {
            node.classList.add("highlight-text");
          });
          // 重置累积
          consecutiveNodes = [];
          currentText = "";
        } else if (normalizedText.length > lowercaseHighlight.length * 3) {
          // 如果累积文本太长还没找到匹配，开始移除旧的节点
          const firstNode = consecutiveNodes.shift();
          if (firstNode) {
            currentText = currentText.slice(
              (firstNode.textContent || "").length
            );
          }
        }

        // 特殊处理：检查当前节点和下一个节点的组合
        if (index < textElements.length - 1) {
          const nextElement = textElements[index + 1];
          const nextText = nextElement.textContent || "";
          const combinedText = (text + nextText)
            .replace(/\s+/g, "")
            .toLowerCase();

          if (combinedText.includes(lowercaseHighlight)) {
            element.classList.add("highlight-text");
            nextElement.classList.add("highlight-text");
          }
        }
      });
    });
  });
};

// 文档加载完成
const handleDocumentLoad = async ({ numPages }: { numPages: number }) => {
  totalPages.value = numPages;
  loadError.value = false;
  isLoading2.value = false;
  // debugger;
  // 只在初始加载时设置页码
  if (isInitialLoad.value) {
    if (props.data.highlights?.length) {
      const firstHighlightPage = props.data.highlights[0].page;
      if (firstHighlightPage <= numPages) {
        currentPage.value = firstHighlightPage;
      }
    } else if (props.data.initialPage && props.data.initialPage <= numPages) {
      currentPage.value = props.data.initialPage;
    }
    isInitialLoad.value = false;
  }
};

// 文档渲染完成
const handleDocumentRender = async () => {
  isLoading.value = false;
  await nextTick();
  // 添加延时确保文本层完全加载
  setTimeout(highlightTextsOnCurrentPage, 100);
};

// 处理密码请求
const handlePasswordRequest = ({
  callback,
  isWrongPassword,
}: {
  callback: (password: string) => void;
  isWrongPassword: boolean;
}) => {
  const message = isWrongPassword ? "密码错误，请重新输入" : "请输入PDF密码";
  const password = prompt(message);
  if (password) {
    callback(password);
  }
};

// 暴露方法给父组件
defineExpose({
  currentPage,
  totalPages,
  showAllPages,
  // 提供跳转到指定页面的方法
  goToPage: (page: number) => {
    if (page > 0 && page <= totalPages.value) {
      currentPage.value = page;
    }
  },
});

onMounted(async () => {
  await checkAndConvertFile();

  if (props.data.highlights?.length) {
    const firstHighlightPage = props.data.highlights[0].page;
    if (firstHighlightPage > 0) {
      currentPage.value = firstHighlightPage;
    }
  }
});
</script>

<style scoped>
.pdf-container {
  @apply bg-white rounded-lg shadow-lg;
}

:deep(.vue-pdf-embed) {
  @apply w-full relative;
}

:deep(.vue-pdf-embed__page) {
  @apply mb-4 shadow-md relative;
  /* 确保页面容器正确定位 */
  transform-origin: top left;
}

:deep(.textLayer) {
  @apply absolute inset-0;
  /* 移除透明度，使用更好的混合模式 */
  opacity: 1;
  mix-blend-mode: darken;
  /* 确保文本层大小与PDF页面完全匹配 */
  transform-origin: top left;
  pointer-events: none;
  user-select: text;
}

:deep(.textLayer > span) {
  @apply absolute text-transparent;
  color: rgba(0, 0, 0, 0);
  cursor: text;
  transform-origin: top left;
  line-height: 1;
  white-space: pre;
  pointer-events: all;
  /* 防止文本选择时出现蓝色背景 */
  -webkit-touch-callout: none;
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
}

:deep(.textLayer > span.highlight-text) {
  background-color: rgba(237, 75, 30, 0.4);
  border-radius: 2px;
  padding: 0 1px;
  margin: -1px;
}

:deep(.textLayer > span::selection) {
  background-color: rgb(0, 0, 255, 0.2);
}

:deep(.annotationLayer) {
  @apply absolute inset-0;
}

:deep(.annotationLayer section) {
  position: absolute;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  @apply w-2;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

:deep(.textLayer > span.highlight-text::selection) {
  background-color: rgba(237, 75, 30, 0.4);
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 rounded;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500;
}

.header-gradient {
  background: linear-gradient(
    129deg,
    rgba(18, 155, 255, 0.1) 0%,
    rgba(62, 96, 233, 0.15) 100%
  );
}

:deep(.el-checkbox__label) {
  @apply text-gray-700 !important;
}

:deep(.el-button) {
  @apply !text-gray-700 hover:!text-blue-600;
}

/* 全屏模式下的样式 */
:deep(.pdf-fullscreen-dialog .el-dialog__body) {
  height: calc(100vh - 54px);
  padding: 0;
  overflow: hidden;
}

.pdf-content-wrapper {
  @apply w-full;
  max-width: min(90vw, 1000px);
  margin: 0 auto;
}

:deep(.fullscreen-pdf) {
  width: 100%;
}

:deep(.fullscreen-pdf .vue-pdf-embed__page) {
  @apply shadow-md mb-4;
  margin-left: auto;
  margin-right: auto;
  max-width: 100%;
  height: auto;
}

:deep(.vue-pdf-embed) {
  @apply w-full;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Markdown样式 */
.markdown-content {
  line-height: 1.6;
  color: #333;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.markdown-content pre {
  white-space: pre-wrap;
}

.markdown-content code {
  background-color: #f5f5f5;
  padding: 0.2em 0.4em;
  border-radius: 3px;
}
</style>
