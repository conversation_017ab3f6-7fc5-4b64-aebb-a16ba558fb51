import { createWrappedApiClient } from '@/services/api-wrapper';
import { useConfig } from '@/hooks/useConfig';

// 创建一个获取配置的函数
const getApiClient = () => {
    const { serverConfig } = useConfig();
    console.log('console-api getApiClient - serverConfig:', serverConfig);
    console.log(
        'console-api getApiClient - baseURL will be:',
        `${serverConfig.CONSOLE_API_BASE_URL}/console`
    );
    return createWrappedApiClient({
        baseURL: `${serverConfig.CONSOLE_API_BASE_URL}/console`,
        headers: {
            // 'Content-Type': 'multipart/form-data',
            'Content-Type': 'application/json',
        },
        tokenKey: 'console_token',
    });
};

// 创建一个获取license API的客户端
const getLicenseApiClient = () => {
    const { serverConfig } = useConfig();
    console.log(
        'console-api getLicenseApiClient - serverConfig:',
        serverConfig
    );
    console.log(
        'console-api getLicenseApiClient - baseURL will be:',
        serverConfig.CONSOLE_API_BASE_URL
    );
    return createWrappedApiClient({
        baseURL: serverConfig.CONSOLE_API_BASE_URL,
        headers: {
            'Content-Type': 'application/json',
        },
        tokenKey: 'console_token',
    });
};

// 获取应用列表（分页、筛选）
export const fetchAppListApi = async (params) => {
    const apiClient = getApiClient();
    return apiClient.get('/api/installed-apps', {
        params,
    });
};

// 获取应用标签列表
export const fetchTags = async () => {
    const apiClient = getApiClient();
    return apiClient.get('/api/tags', {
        params: { type: 'app' },
    });
};

// 置顶应用
export const pinTopAppApi = async (appId) => {
    const apiClient = getApiClient();
    return apiClient.request(
        'PATCH',
        `/api/installed-apps/${appId}`,
        {}, // 第三个参数应该是params对象，这里不需要URL参数
        {
            headers: {
                'Content-Type': 'application/json',
            },
            data: { is_pinned: true }, // 请求体数据应该放在data属性中
        }
    );
};

// 取消置顶应用
export const unpinAppApi = async (appId) => {
    const apiClient = getApiClient();
    return apiClient.request(
        'PATCH',
        `/api/installed-apps/${appId}`,
        {}, // 第三个参数应该是params对象，这里不需要URL参数
        {
            headers: {
                'Content-Type': 'application/json',
            },
            data: { is_pinned: false }, // 请求体数据应该放在data属性中
        }
    );
};

// 获取版本
export const fetchGetVersion = async () => {
    const apiClient = getApiClient();
    return apiClient.get('/api/version', {});
};

// 获取系统配置
export const fetchGetSysConfigs = async () => {
    const apiClient = getApiClient();
    return apiClient.get('/api/workspaces/current/sys-configs', {});
};

// 更新系统配置
export const fetchSubmitSystemConfigs = async (post_data) => {
    const apiClient = getApiClient();
    return apiClient.post(`/api/workspaces/current/sys-configs`, post_data);
};
