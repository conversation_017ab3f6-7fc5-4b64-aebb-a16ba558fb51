/**
 * 聊天消息类型
 */
export interface ChatMessage {
  role: "user" | "assistant";
  content: string;
  taskmessage_id: string;
  create_time?: string;
  thinkingContent?: string; // 思考内容
  attachments_data?: any[]; // 附件数据

  // 组件相关属性
  topComponent?: any;
  topComponentKey?: string | number;
  topData?: any;
  bottomComponent?: any;
  bottomComponentKey?: string | number;
  bottomData?: any;
  txtBottomComponent?: any;
  txtBottomComponentKey?: string | number;
  txtBottomData?: any;
}

/**
 * 处理流消息的类型
 */
export interface StreamData {
  topic_id: any;
  content: any[];
  taskmessage_id: string;
  error?: string;
}

/**
 * 处理结果的接口
 */
export interface ProcessResult {
  normalText: string;
  thinkingText: string;
  thinkingComplete: boolean;
}
