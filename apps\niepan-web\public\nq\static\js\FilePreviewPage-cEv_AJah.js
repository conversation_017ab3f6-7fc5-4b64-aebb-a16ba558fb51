const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/PDFViewerComponent-D2MtVG9-.js","static/js/pnpm-pnpm-B4aX-tnA.js","static/css/pnpm-pnpm-BdGb95pV.css","static/js/index-oCQpNzhc.js","static/css/index-CgHh2pBR.css","static/css/PDFViewerComponent-CIGfg6C1.css"])))=>i.map(i=>d[i]);
import{c as m,y as h,a as w}from"./index-oCQpNzhc.js";import{d as x,q as y,r as l,z as g,c as s,o,t as P,f as b,aa as D,aF as S,b as c,x as F,y as I}from"./pnpm-pnpm-B4aX-tnA.js";const k=t=>(F("data-v-dd858ac6"),t=t(),I(),t),B={class:"file-preview-container"},E={key:0,class:"flex justify-center items-center min-h-screen"},A=k(()=>c("div",{class:"flex flex-col items-center gap-2"},[c("div",{class:"w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"}),c("span",{class:"text-gray-600"},"加载中...")],-1)),C=[A],U={key:1,class:"text-red-500 text-center py-8"},V={key:2,class:"w-full max-w-6xl mx-auto px-4"},N=x({__name:"FilePreviewPage",setup(t){const u=S(()=>m(()=>import("./PDFViewerComponent-D2MtVG9-.js"),__vite__mapDeps([0,1,2,3,4,5]))),_=y(),r=l(!0),a=l(""),i=l({fileUrl:"",title:"文件预览",hideFullscreenButton:!0,defaultShowAllPages:!0});function f(e){return h(e)}const p=()=>{try{r.value=!0,a.value="";const e=_.query.data;if(!e)throw new Error("缺少必要的文件参数");const d=decodeURIComponent(e),v=f(d),n=JSON.parse(v);if(!n.fileUrl)throw new Error("文件链接不存在");i.value={...n,title:n.title||"文件预览",hideFullscreenButton:!1,defaultShowAllPages:!1}}catch(e){console.error("解析文件参数失败:",e),a.value=e.message||"解析文件参数失败"}finally{r.value=!1}};return g(()=>{p()}),(e,d)=>(o(),s("div",B,[r.value?(o(),s("div",E,C)):a.value?(o(),s("div",U,P(a.value),1)):(o(),s("div",V,[b(D(u),{data:i.value},null,8,["data"])]))]))}}),q=w(N,[["__scopeId","data-v-dd858ac6"]]);export{q as default};
