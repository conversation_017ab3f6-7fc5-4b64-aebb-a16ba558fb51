/**
 * 构建脚本：执行 pnpm nq:build 时，先构建 newquery 项目，然后将构建结果复制到 niepan-web 项目中
 *
 * 执行流程：
 * 1. 删除 apps/newquery/dist 目录
 * 2. 执行 newquery 项目的构建
 * 3. 删除 apps/niepan-web/public/nq 目录
 * 4. 将 apps/newquery/dist 目录的内容复制到 apps/niepan-web/public/nq 目录
 * 5. 修改 apps/niepan-web/public/nq/static-config/config.js 文件，设置 window._server_config.VITE_API_BASE_URL = "__BFF_API_URL__"
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 获取项目根目录
const rootDir = path.resolve(__dirname, '..');
const newqueryDir = path.join(rootDir, 'apps', 'newquery');
const newqueryDistDir = path.join(newqueryDir, 'dist');
const niepanWebPublicNqDir = path.join(rootDir, 'apps', 'niepan-web', 'public', 'nq');
const configJsPath = path.join(niepanWebPublicNqDir, 'static-config', 'config.js');

// 日志函数
function log(message) {
  console.log(`[构建脚本] ${message}`);
}

// 删除目录函数，带重试
async function removeDir(dir) {
  if (!fs.existsSync(dir)) {
    return;
  }

  log(`删除目录: ${dir}`);

  // 最大重试次数
  const maxRetries = 3;
  let retries = 0;

  while (retries < maxRetries) {
    try {
      fs.rmSync(dir, { recursive: true, force: true });
      return; // 成功删除，退出函数
    } catch (error) {
      retries++;
      log(`删除目录失败 (尝试 ${retries}/${maxRetries}): ${error.message}`);

      if (retries >= maxRetries) {
        log('达到最大重试次数，尝试备用方案...');
        try {
          // 备用方案：重命名目录而不是删除
          const backupDir = `${dir}_old_${Date.now()}`;
          fs.renameSync(dir, backupDir);
          log(`已将目录重命名为: ${backupDir}`);
          return;
        } catch (renameError) {
          log(`备用方案失败: ${renameError.message}`);
          throw new Error(`无法删除或重命名目录: ${dir}`);
        }
      }

      // 等待一段时间后重试
      log('等待后重试...');
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}

// 创建目录函数
function createDir(dir) {
  if (!fs.existsSync(dir)) {
    log(`创建目录: ${dir}`);
    fs.mkdirSync(dir, { recursive: true });
  }
}

// 复制目录函数
function copyDir(src, dest) {
  log(`复制目录: ${src} -> ${dest}`);

  // 确保目标目录存在
  createDir(dest);

  // 获取源目录中的所有文件和子目录
  const entries = fs.readdirSync(src, { withFileTypes: true });

  // 遍历并复制每个文件和子目录
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      // 递归复制子目录
      copyDir(srcPath, destPath);
    } else {
      // 复制文件
      fs.copyFileSync(srcPath, destPath);
    }
  }
}

// 修改配置文件函数
function updateConfigFile(filePath) {
  if (!fs.existsSync(filePath)) {
    log(`警告: 配置文件不存在: ${filePath}`);
    return;
  }

  log(`修改配置文件: ${filePath}`);

  try {
    let content = fs.readFileSync(filePath, 'utf8');

    // 移除所有现有的 window._server_config 定义（包括注释掉的）
    // content = content.replace(/\/\/\s*window\._server_config[\s\S]*?};/g, '');
    // content = content.replace(/window\._server_config[\s\S]*?};/g, '');

    // 在文件末尾添加新的配置
    // content += '\n\n// 配置API基础URL，将在部署时被替换\nwindow._server_config = {\n  VITE_API_BASE_URL: "__BFF_API_URL__",\n};\n';
    content +=
      '\n\n// 配置API基础URL，将在部署时被替换\nwindow._server_config.VITE_API_BASE_URL = "__BFF_API_URL__";\n';
    content +=
      '\n\n// 配置ConsoleAPI基础URL，将在部署时被替换\nwindow._server_config.CONSOLE_API_BASE_URL = "__CONSOLE_API_URL__";\n';

    fs.writeFileSync(filePath, content, 'utf8');
    log('配置文件修改成功');
  } catch (error) {
    log(`修改配置文件时出错: ${error.message}`);
    process.exit(1);
  }
}

// 主函数
async function main() {
  try {
    // 步骤 0: 执行 pnpm install 安装依赖
    log('执行 pnpm install 安装依赖');
    execSync('pnpm install --frozen-lockfile --registry https://registry.npmmirror.com/', {
      stdio: 'inherit',
      cwd: rootDir,
    });

    // 步骤 1: 删除 newquery 的 dist 目录
    log('开始构建 newquery 项目');
    await removeDir(newqueryDistDir);

    // 步骤 2: 构建 newquery 项目
    log('执行 newquery 构建命令');
    execSync('pnpm --filter newquery build', { stdio: 'inherit', cwd: rootDir });

    // 检查构建结果
    if (!fs.existsSync(newqueryDistDir)) {
      log('错误: newquery 构建失败，dist 目录不存在');
      process.exit(1);
    }

    // 步骤 3: 删除 niepan-web/public/nq 目录
    log('准备复制构建结果到 niepan-web 项目');
    await removeDir(niepanWebPublicNqDir);

    // 步骤 4: 复制 newquery/dist 到 niepan-web/public/nq
    copyDir(newqueryDistDir, niepanWebPublicNqDir);

    // 步骤 5: 修改配置文件
    // 确保 static-config 目录存在
    createDir(path.dirname(configJsPath));

    // 如果配置文件不存在，从 newquery 项目复制一份
    const srcConfigPath = path.join(newqueryDir, 'public', 'static-config', 'config.js');
    if (!fs.existsSync(configJsPath) && fs.existsSync(srcConfigPath)) {
      log(`复制配置文件模板: ${srcConfigPath} -> ${configJsPath}`);
      fs.copyFileSync(srcConfigPath, configJsPath);
    }

    // 修改配置文件
    updateConfigFile(configJsPath);

    log('构建完成！');
  } catch (error) {
    log(`构建过程中出错: ${error.message}`);
    process.exit(1);
  }
}

// 执行主函数并处理错误
main().catch(error => {
  log(`脚本执行失败: ${error.message}`);
  process.exit(1);
});
