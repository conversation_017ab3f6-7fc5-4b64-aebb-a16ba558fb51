import{w as v,d as y,aQ as x,aR as g,a as S}from"./index-oCQpNzhc.js";import{d as C,s as b,r as w,ae as E,E as r,z as V,e as i,c as D,o as I,b as t,f as u,m as U,p as k,aa as B,x as L,y as M}from"./pnpm-pnpm-B4aX-tnA.js";const d=a=>(L("data-v-2679ee34"),a=a(),M(),a),P={class:"bg-white rounded-[20px] overflow-hidden h-[calc(100vh-120px)] relative z-50"},A={class:"mx-auto max-w-[800px] px-3 py-3"},N=d(()=>t("div",{class:"text-[20px] font-medium text-base px-3 py-4"},"帮助Agent",-1)),R={class:"p-3"},z=d(()=>t("div",{class:"text-sm font-medium mb-2"},"WebAPP 链接",-1)),H={class:"text-right my-3"},O=C({__name:"system-config",setup(a){const p=v(),c=y(),l=x(),n=b(()=>l.sysConfigsData),o=w(""),m=E.debounce(async()=>{const s={data:[]};o.value&&s.data.push({name:"HELP_DOCS_URL",value:o.value});try{(await g(s)).data.result=="success"&&(r.success("设置成功"),setTimeout(()=>location.reload(),1e3))}catch(e){console.error("设置失败:",e),r.error("激活失败")}},100);return V(async()=>{if(p.setActiveMenu("system-config"),c.userInfo||await c.fetchUserInfo(null),await l.fetchSysConfigs(),n.value&&n.value.length>0){const s=n.value.find(e=>e.name==="HELP_DOCS_URL");s&&(o.value=s.value)}}),(s,e)=>{const f=i("el-input"),_=i("el-button");return I(),D("div",P,[t("div",A,[N,t("div",null,[t("div",R,[z,u(f,{modelValue:o.value,"onUpdate:modelValue":e[0]||(e[0]=h=>o.value=h),type:"input",placeholder:"请输入",class:"w-full"},null,8,["modelValue"])]),t("div",H,[u(_,{type:"primary",class:"bg-[#129BFE] rounded-[6px]",style:{"background-color":"#129bfe"},onClick:B(m)},{default:U(()=>[k(" 保存 ")]),_:1},8,["onClick"])])])])])}}}),Q=S(O,[["__scopeId","data-v-2679ee34"]]);export{Q as default};
