<!-- 文件列表和页码组件 -->
<template>
  <div class="w-full pt-4 pb-0 px-0">
    <div class="flex flex-wrap gap-4">
      <div
        v-for="file in data.files"
        :key="file.fileUrl"
        class="w-[calc(50%-8px)]"
      >
        <div
          class="group flex transition-all duration-300 rounded-[18px] bg-white min-h-[98px]"
        >
          <!-- 图标区域 -->
          <div
            class="w-[66px] shrink-0 rounded-l-[18px] flex items-center justify-center border-1 border-[#E5E5E5] border-solid self-stretch"
            :class="getFileTypeClass(file.fileType)"
          >
            <component
              :is="getFileIcon(file.fileType)"
              class="w-[26px] h-[26px]"
            />
          </div>

          <!-- 右侧内容区域 -->
          <div
            class="flex-1 min-w-0 flex flex-col px-[10px] py-3 border border-1 border-[#E5E5E5] border-l-0 rounded-r-[18px] border-solid relative"
            :style="{ height: file.showMore ? 'auto' : 'inherit' }"
          >
            <!-- 文件标题部分 -->
            <div
              class="cursor-pointer"
              @click="(event) => handleFileTitleClick(file, event)"
            >
              <div
                class="text-[#121619] text-[14px] leading-[22px] font-normal font-['PingFang SC'] truncate hover:text-[#0C78DB]"
              >
                {{ file.title }}.{{ file.fileType }}
              </div>
            </div>

            <!-- 页码部分 -->
            <div class="mt-0">
              <!-- 提示文字 -->
              <div class="text-[#A2A9B0] text-[12px] leading-[16px] mb-[10px]">
                点击下面相关联页码，查看详情
              </div>
              <!-- 页码列表 -->
              <div class="flex flex-col">
                <!-- 收起状态的页码区域 -->
                <div class="flex" v-show="!file.showMore">
                  <div class="flex-1 flex items-center" style="gap: 6px">
                    <template
                      v-for="(page, index) in getFirstLinePages(file)"
                      :key="index"
                    >
                      <div
                        class="h-[20px] shrink-0 px-1 rounded leading-[20px] flex items-center justify-center cursor-pointer text-[#0C78DB] text-[12px] hover:bg-[#F5F6F7]"
                        @click="(event) => handlePageClick(file, page, event)"
                      >
                        #{{ page }}
                      </div>
                    </template>
                  </div>

                  <!-- 收起状态的更多按钮 -->
                  <div
                    v-if="shouldShowMore(file)"
                    class="h-[20px] px-1 rounded leading-[20px] flex items-center justify-center cursor-pointer text-[12px] text-[#A2A9B0] hover:text-[#6f7173] shrink-0 ml-1"
                    @click="() => toggleShowMore(file)"
                  >
                    <span class="mr-[2px]">更多</span>
                    <el-icon size="12" color="#A2A9B0">
                      <CaretBottom />
                    </el-icon>
                  </div>
                </div>

                <!-- 展开状态的页码区域 -->
                <div
                  class="flex flex-col"
                  style="gap: 6px"
                  v-show="file.showMore"
                >
                  <!-- 第一行页码 -->
                  <div class="flex">
                    <div class="flex-1 flex items-center" style="gap: 6px">
                      <template
                        v-for="(page, index) in getFirstLinePages(file)"
                        :key="index"
                      >
                        <div
                          class="h-[20px] shrink-0 px-1 rounded leading-[20px] flex items-center justify-center cursor-pointer text-[#0C78DB] text-[12px] hover:bg-[#F5F6F7]"
                          @click="(event) => handlePageClick(file, page, event)"
                        >
                          #{{ page }}
                        </div>
                      </template>
                    </div>

                    <!-- 展开状态的收起按钮 -->
                    <div
                      class="h-[20px] px-1 rounded leading-[20px] flex items-center justify-center cursor-pointer text-[12px] text-[#A2A9B0] hover:text-[#6f7173] shrink-0 ml-1"
                      @click="() => toggleShowMore(file)"
                    >
                      <span class="mr-[2px]">收起</span>
                      <el-icon size="12" color="#A2A9B0">
                        <CaretTop />
                      </el-icon>
                    </div>
                  </div>

                  <!-- 剩余页码 -->
                  <div class="flex flex-wrap" style="gap: 6px">
                    <template
                      v-for="(page, index) in getRemainingPages(file)"
                      :key="index"
                    >
                      <div
                        class="h-[20px] shrink-0 px-1 rounded leading-[20px] flex items-center justify-center cursor-pointer text-[#0C78DB] text-[12px] hover:bg-[#F5F6F7]"
                        @click="(event) => handlePageClick(file, page, event)"
                      >
                        #{{ page }}
                      </div>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getFileIcon } from "@/config/fileIconMap";
import { CaretBottom, CaretTop } from "@element-plus/icons-vue";
import type { PropType } from "vue";
import { useMessageBus } from "../../hooks/useMessageBus";
import { ref, reactive, onMounted, nextTick } from "vue";

interface FileInfo {
  fileUrl: string;
  fileType?: string;
  title: string;
  initialPage?: number;
  pages?: number[];
  showMore?: boolean; // 控制是否显示更多页码
}

interface ModelData {
  files: FileInfo[];
}

const props = defineProps({
  data: {
    type: Object as PropType<ModelData>,
    required: true,
  },
});

const { triggerWorkbench } = useMessageBus();

// 计算页码显示宽度
const calculatePageWidth = (page: number) => {
  const result = 8 + String(page).length * 7 + 8 + 6;
  console.log("calculatePageWidth result", result);
  return result;
};

// 计算第一行能显示的页码数量
const calculateFirstLineCount = (file: FileInfo, containerWidth: number) => {
  if (!file.pages || file.pages.length === 0) return 0;

  // 找出最长的页码数字
  const maxPage = Math.max(...file.pages);
  // 计算单个页码项的最大宽度
  const maxItemWidth = calculatePageWidth(maxPage);

  // 减去"更多"按钮的宽度(约44px)和它的左边距(4px)
  const availableWidth = containerWidth - 48;

  // 计算能放下多少个页码
  const count = Math.floor(availableWidth / maxItemWidth);
  console.log(
    "calculateFirstLineCount count",
    count,
    availableWidth,
    maxItemWidth
  );

  // 返回实际能显示的数量（不超过实际页码数量）
  return Math.min(count, file.pages.length);
};

// 获取第一行页码
const getFirstLinePages = (file: FileInfo) => {
  if (!file.pages) return [];
  // 假设容器宽度为300px（这个值需要根据实际情况调整）
  const count = calculateFirstLineCount(file, 300);
  return file.pages.slice(0, count);
};

// 获取剩余页码
const getRemainingPages = (file: FileInfo) => {
  if (!file.pages) return [];
  const count = calculateFirstLineCount(file, 300);
  return file.pages.slice(count);
};

// 判断是否应该显示更多按钮
const shouldShowMore = (file: FileInfo) => {
  if (!file.pages) return false;
  const count = calculateFirstLineCount(file, 300);
  return file.pages.length > count;
};

// 处理文件标题点击
const handleFileTitleClick = (file: FileInfo, event: Event) => {
  event?.stopPropagation();

  triggerWorkbench("pdf-viewer-component", {
    fileUrl: file.fileUrl,
    fileType: file.fileType,
    title: file.title,
    initialPage: file.initialPage || 1,
  });
};

// 处理页码点击
const handlePageClick = (file: FileInfo, page: number, event: Event) => {
  event?.stopPropagation();

  triggerWorkbench("pdf-viewer-component", {
    fileUrl: file.fileUrl,
    fileType: file.fileType,
    title: file.title,
    initialPage: page,
  });
};

// 切换显示更多页码
const toggleShowMore = (file: FileInfo) => {
  file.showMore = !file.showMore;
};

const getFileTypeClass = (fileName: string) => {
  const extension = fileName.split(".").pop()?.toLowerCase();
  switch (extension) {
    case "ppt":
    case "pptx":
      return "file-type-ppt";
    case "doc":
    case "docx":
      return "file-type-doc";
    default:
      return "file-type-default";
  }
};
</script>

<style scoped>
.file-type-doc {
  background: rgba(64, 165, 238, 0.2);
  border: 1px solid #e5e5e5;
  border-right: none;
}

.file-type-ppt {
  background: rgba(255, 143, 107, 0.2);
  border: 1px solid #e5e5e5;
  border-right: none;
}

.file-type-pdf {
  background: rgba(251, 84, 84, 0.2);
  border: 1px solid #e5e5e5;
  border-right: none;
}

.file-type-default {
  background: rgba(250, 173, 20, 0.2);
  border: 1px solid #e5e5e5;
  border-right: none;
}

/* 添加hover阴影效果 */
.hover\:shadow-hover:hover {
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.1);
}
</style>
