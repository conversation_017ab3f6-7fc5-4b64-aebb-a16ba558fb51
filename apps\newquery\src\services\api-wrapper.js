/**
 * API 客户端包装器，用于统一处理错误响应格式
 */
import { ApiClient } from '@/services/http.ts';

/**
 * 处理API响应，统一错误格式
 * @param {Promise} apiPromise - API调用的Promise
 * @returns {Promise} - 处理后的Promise
 */
export const handleApiResponse = async (apiPromise) => {
    try {
        const response = await apiPromise;

        // 检查响应中是否包含错误信息
        if (response && response.data) {
            // 检查是否有嵌套的detail错误结构
            if (response.data.detail && response.data.detail.error) {
                // 将嵌套的错误信息提取到顶层
                response.data.error = response.data.detail.error;
                response.data.message =
                    response.data.detail.message || '操作失败';
            }
        }

        return response;
    } catch (error) {
        // 处理错误响应
        if (error.response && error.response.data) {
            // 检查是否有嵌套的detail错误结构
            if (
                error.response.data.detail &&
                error.response.data.detail.error
            ) {
                // 将嵌套的错误信息提取到顶层
                error.response.data.error = error.response.data.detail.error;
                error.response.data.message =
                    error.response.data.detail.message || '操作失败';
            }

            // 注意：403状态码的处理已经在http.ts的拦截器中完成
            // 这里不需要重复处理，因为axios拦截器会先执行
        }

        throw error;
    }
};

/**
 * 包装API函数，统一处理错误响应格式
 * @param {Function} apiFn - 原始API函数
 * @returns {Function} - 包装后的API函数
 */
export const wrapApi = (apiFn) => {
    return async (...args) => {
        return handleApiResponse(apiFn(...args));
    };
};

/**
 * 创建一个预先包装好的API客户端实例
 * 所有通过该客户端发出的请求都会自动经过错误处理包装
 * @param {Object} config - API客户端配置
 * @returns {Object} - 包装后的API客户端实例
 */
export const createWrappedApiClient = (config) => {
    // 提取tokenKey和其他axios配置
    const { tokenKey, ...axiosConfig } = config;

    // 按照ApiClient期望的格式重新组织配置
    const apiClientOptions = {
        axiosConfig,
        tokenKey,
    };

    const apiClientInstance = new ApiClient(apiClientOptions);

    // 包装原始方法
    const originalGet = apiClientInstance.get.bind(apiClientInstance);
    const originalPost = apiClientInstance.post.bind(apiClientInstance);
    const originalPut = apiClientInstance.put.bind(apiClientInstance);
    const originalDelete = apiClientInstance.delete.bind(apiClientInstance);
    const originalRequest = apiClientInstance.request.bind(apiClientInstance);

    // 重写方法，添加自动错误处理
    apiClientInstance.get = async (...args) => {
        return handleApiResponse(originalGet(...args));
    };

    apiClientInstance.post = async (...args) => {
        return handleApiResponse(originalPost(...args));
    };

    apiClientInstance.put = async (...args) => {
        return handleApiResponse(originalPut(...args));
    };

    apiClientInstance.delete = async (...args) => {
        return handleApiResponse(originalDelete(...args));
    };

    apiClientInstance.request = async (...args) => {
        return handleApiResponse(originalRequest(...args));
    };

    return apiClientInstance;
};
