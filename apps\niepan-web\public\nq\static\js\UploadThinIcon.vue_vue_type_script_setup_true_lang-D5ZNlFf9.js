import{_ as l}from"./index-oCQpNzhc.js";import{d as a,A as o,o as d,m as r,b as e,n as t}from"./pnpm-pnpm-B4aX-tnA.js";const c=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 18 17"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M15.3006 2.20001C15.6872 2.20001 16.0006 2.51341 16.0006 2.90001V8.50001C16.0006 8.88661 15.6872 9.20001 15.3006 9.20001C14.914 9.20001 14.6006 8.88661 14.6006 8.50001V2.90001C14.6006 2.51341 14.914 2.20001 15.3006 2.20001Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M2.7 7.79999C3.0866 7.79999 3.4 8.11339 3.4 8.49999V14.1C3.4 14.4866 3.0866 14.8 2.7 14.8C2.3134 14.8 2 14.4866 2 14.1V8.49999C2 8.11339 2.3134 7.79999 2.7 7.79999Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M3.96321 3.63883C5.23538 2.32098 7.02242 1.5 9 1.5C12.866 1.5 16 4.63402 16 8.5C16 8.8866 15.6866 9.2 15.3 9.2C14.9134 9.2 14.6 8.8866 14.6 8.5C14.6 5.40721 12.0928 2.9 9 2.9C7.41773 2.9 5.98974 3.5553 4.97046 4.61117C4.70196 4.88931 4.25881 4.89713 3.98067 4.62863C3.70252 4.36012 3.69471 3.91698 3.96321 3.63883ZM2.7 7.8C3.0866 7.8 3.4 8.1134 3.4 8.5C3.4 11.5928 5.90721 14.1 9 14.1C10.5109 14.1 11.8808 13.5026 12.8888 12.5295C13.167 12.261 13.6101 12.2689 13.8786 12.547C14.1471 12.8252 14.1393 13.2683 13.8612 13.5368C12.6027 14.7516 10.888 15.5 9 15.5C5.13402 15.5 2 12.366 2 8.5C2 8.1134 2.3134 7.8 2.7 7.8Z"})],-1),i={name:"RefreshIcon"},m=a({...i,props:{color:{default:"#343A3F"},size:{default:26},className:{default:""}},setup(C){return(s,n)=>(d(),o(l,{color:s.color,size:s.size,class:t(s.className)},{default:r(()=>[c]),_:1},8,["color","size","class"]))}}),p=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 18 18"},[e("mask",{id:"mask0_1196_814",style:{"mask-type":"alpha"},maskUnits:"userSpaceOnUse",x:"0",y:"0",width:"18",height:"18"},[e("path",{d:"M18 0H0V18H18V0Z"})]),e("g",{mask:"url(#mask0_1196_814)"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M15.75 8.25C16.1642 8.25 16.5 8.58579 16.5 9V15.75C16.5 16.1642 16.1642 16.5 15.75 16.5H2.25C1.83579 16.5 1.5 16.1642 1.5 15.75V9.00311C1.5 8.5889 1.83579 8.25311 2.25 8.25311C2.66421 8.25311 3 8.5889 3 9.00311V15H15V9C15 8.58579 15.3358 8.25 15.75 8.25Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M8.46967 1.71967C8.76256 1.42678 9.23744 1.42678 9.53033 1.71967L12.9053 5.09467C13.1982 5.38756 13.1982 5.86244 12.9053 6.15533C12.6124 6.44822 12.1376 6.44822 11.8447 6.15533L9 3.31066L6.15533 6.15533C5.86244 6.44822 5.38756 6.44822 5.09467 6.15533C4.80178 5.86244 4.80178 5.38756 5.09467 5.09467L8.46967 1.71967Z"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M8.99707 1.5C9.41128 1.5 9.74707 1.83579 9.74707 2.25V12C9.74707 12.4142 9.41128 12.75 8.99707 12.75C8.58286 12.75 8.24707 12.4142 8.24707 12V2.25C8.24707 1.83579 8.58286 1.5 8.99707 1.5Z"})])],-1),u={name:"UploadThinIcon"},h=a({...u,props:{color:{default:"#fff"},size:{default:18},className:{default:""}},setup(C){return(s,n)=>(d(),o(l,{color:s.color,size:s.size,class:t(s.className)},{default:r(()=>[p]),_:1},8,["color","size","class"]))}});export{m as _,h as a};
