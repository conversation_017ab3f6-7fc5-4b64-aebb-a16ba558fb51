# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build
/public/nq/
/public/static-config/

/.history

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# npm
package-lock.json

# yarn
.pnp.cjs
.pnp.loader.mjs
.yarn/

.favorites.json

# storybook
/storybook-static
*storybook.log

# mise
mise.toml

