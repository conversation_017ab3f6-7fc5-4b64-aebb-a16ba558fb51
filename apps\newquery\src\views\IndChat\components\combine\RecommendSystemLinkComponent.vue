<template>
  <a
    v-for="(item, index) in data"
    :key="index"
    :href="item.linkUrl"
    :target="item.linkUrl ? '_blank' : ''"
    :class="item.linkUrl ? '' : 'cursor-default'"
  >
    <div
      class="rounded-[6px] border border-solid border-[#F1F1F1] flex items-center bg-white overflow-hidden px-[12px] py-[9px] mr-[15px] mb-[15px] link-item"
    >
      <div>
        <img
          v-if="item.imgUrl"
          :src="item.imgUrl"
          alt=""
          class="w-[24px] h-[24px] object-contain object-center"
        />
        <img
          src="@/assets/images/home/<USER>"
          alt=""
          class="w-[24px] h-[24px] object-contain object-center"
          v-else
        />
        <span class="text-[#00080E] text-base mx-[8px] font-zhongcu">{{
          item.title || "暂无"
        }}</span>
      </div>
      <div class="w-[24px] h-[24px]">
        <img
          src="@/assets/images/chat/right.png"
          alt=""
          class="w-[24px] h-[24px] object-contain object-center hidden right-img"
        />
      </div>
    </div>
  </a>
</template>

<script setup lang="ts">
import { computed } from "vue";

const props = defineProps<{
  data: Array<{
    title?: string;
    imgUrl?: string;
    linkUrl?: string;
  }>;
}>();
</script>
<style scoped>
.link-item:hover {
  box-shadow: 0px 3px 8px 0px rgba(0, 8, 14, 0.1);
}
.link-item:hover .right-img {
  display: block;
}
</style>
