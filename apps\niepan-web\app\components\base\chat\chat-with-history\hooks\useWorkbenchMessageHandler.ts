import { useCallback } from 'react';
import type { WorkbenchComponentData } from '../components/workbench-components/componentRegistry';

// 定义聊天消息类型
export interface ChatMessage {
  id: string;
  type: string;
  content: string;
  metadata?: Record<string, any>;
  workbench?: {
    action: 'add' | 'update' | 'remove' | 'clear';
    components?: WorkbenchComponentData[];
    componentId?: string;
    componentData?: Record<string, any>;
  };
}

// 工作台消息处理Hook
export function useWorkbenchMessageHandler() {
  
  // 解析聊天消息中的工作台指令
  const parseWorkbenchInstructions = useCallback((message: ChatMessage): WorkbenchComponentData[] => {
    const components: WorkbenchComponentData[] = [];

    // 如果消息直接包含工作台配置
    if (message.workbench?.components) {
      return message.workbench.components;
    }

    // 根据消息类型自动生成工作台组件
    switch (message.type) {
      case 'document_uploaded':
        if (message.metadata?.fileUrl) {
          components.push({
            uid: 'pptx-preview-component',
            area: 'main',
            data: {
              title: message.metadata.fileName || '文档预览',
              fileUrl: message.metadata.fileUrl,
              initialPage: 1,
            },
          });
        }
        break;

      case 'analysis_complete':
        components.push({
          uid: 'text-detail-component',
          area: 'main',
          data: {
            title: '分析结果',
            content: message.content,
            type: 'success',
            collapsible: true,
            defaultExpanded: true,
          },
        });
        break;

      case 'analysis_error':
        components.push({
          uid: 'text-detail-component',
          area: 'main',
          data: {
            title: '分析失败',
            content: message.content,
            type: 'error',
            collapsible: false,
            defaultExpanded: true,
          },
        });
        break;

      case 'warning':
        components.push({
          uid: 'text-detail-component',
          area: 'main',
          data: {
            title: '注意事项',
            content: message.content,
            type: 'warning',
            collapsible: true,
            defaultExpanded: true,
          },
        });
        break;

      case 'project_list':
        if (message.metadata?.projects) {
          components.push({
            uid: 'list-component',
            area: 'main',
            data: {
              title: '相关项目',
              showSearch: true,
              showStatus: true,
              items: message.metadata.projects.map((project: any) => ({
                id: project.id,
                title: project.name,
                description: project.description,
                status: project.status || 'active',
                metadata: project.metadata || {},
              })),
            },
          });
        }
        break;

      case 'document_list':
        if (message.metadata?.documents) {
          components.push({
            uid: 'list-component',
            area: 'main',
            data: {
              title: '相关文档',
              showSearch: true,
              showStatus: false,
              items: message.metadata.documents.map((doc: any) => ({
                id: doc.id,
                title: doc.name,
                description: doc.description,
                metadata: {
                  type: doc.type,
                  size: doc.size,
                  modified: doc.modified,
                },
              })),
            },
          });
        }
        break;

      case 'info':
      default:
        // 对于普通信息消息，如果内容较长，创建文本详情组件
        if (message.content.length > 100) {
          components.push({
            uid: 'text-detail-component',
            area: 'main',
            data: {
              title: '详细信息',
              content: message.content,
              type: 'info',
              collapsible: true,
              defaultExpanded: false,
            },
          });
        }
        break;
    }

    return components;
  }, []);

  // 创建工作台更新指令
  const createWorkbenchUpdate = useCallback((
    action: 'add' | 'update' | 'remove' | 'clear',
    components?: WorkbenchComponentData[],
    componentId?: string,
    componentData?: Record<string, any>
  ) => {
    return {
      action,
      components,
      componentId,
      componentData,
    };
  }, []);

  // 处理文档上传
  const handleDocumentUpload = useCallback((fileUrl: string, fileName: string) => {
    return createWorkbenchUpdate('add', [{
      uid: 'pptx-preview-component',
      area: 'main',
      data: {
        title: `文档预览: ${fileName}`,
        fileUrl,
        initialPage: 1,
      },
    }]);
  }, [createWorkbenchUpdate]);

  // 处理分析结果
  const handleAnalysisResult = useCallback((result: string, isSuccess: boolean = true) => {
    return createWorkbenchUpdate('add', [{
      uid: 'text-detail-component',
      area: 'main',
      data: {
        title: isSuccess ? '分析结果' : '分析失败',
        content: result,
        type: isSuccess ? 'success' : 'error',
        collapsible: true,
        defaultExpanded: true,
      },
    }]);
  }, [createWorkbenchUpdate]);

  // 处理项目列表显示
  const handleProjectList = useCallback((projects: any[]) => {
    return createWorkbenchUpdate('add', [{
      uid: 'list-component',
      area: 'main',
      data: {
        title: '相关项目',
        showSearch: true,
        showStatus: true,
        items: projects.map(project => ({
          id: project.id,
          title: project.name,
          description: project.description,
          status: project.status || 'active',
          metadata: project.metadata || {},
        })),
      },
    }]);
  }, [createWorkbenchUpdate]);

  // 处理组件数据更新
  const handleComponentUpdate = useCallback((componentId: string, newData: Record<string, any>) => {
    return createWorkbenchUpdate('update', undefined, componentId, newData);
  }, [createWorkbenchUpdate]);

  // 处理组件移除
  const handleComponentRemove = useCallback((componentId: string) => {
    return createWorkbenchUpdate('remove', undefined, componentId);
  }, [createWorkbenchUpdate]);

  // 处理工作台清空
  const handleWorkbenchClear = useCallback(() => {
    return createWorkbenchUpdate('clear');
  }, [createWorkbenchUpdate]);

  return {
    parseWorkbenchInstructions,
    createWorkbenchUpdate,
    handleDocumentUpload,
    handleAnalysisResult,
    handleProjectList,
    handleComponentUpdate,
    handleComponentRemove,
    handleWorkbenchClear,
  };
}
