/**
 * newquery 项目配置开发脚本
 *
 * 执行流程：
 * 1. 复制指定项目的配置文件到 newquery 项目的 static-config 目录
 * 2. 启动 newquery 项目的开发服务器
 *
 * 使用方法：
 * node scripts/dev-nq-config.js <project-name>
 *
 * 示例：
 * node scripts/dev-nq-config.js aa
 * node scripts/dev-nq-config.js qlj
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 获取项目根目录
const rootDir = path.resolve(__dirname, '..');

// 日志函数
function log(message) {
  console.log(`[newquery配置开发] ${message}`);
}

// 复制目录函数
function copyDir(src, dest) {
  log(`复制配置目录: ${src} -> ${dest}`);

  // 确保目标目录存在
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }

  // 获取源目录中的所有文件和子目录
  const entries = fs.readdirSync(src, { withFileTypes: true });

  // 遍历并复制每个文件和子目录
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);

    if (entry.isDirectory()) {
      // 递归复制子目录
      copyDir(srcPath, destPath);
    } else {
      // 复制文件
      fs.copyFileSync(srcPath, destPath);
      log(`复制文件: ${entry.name}`);
    }
  }
}

// 主函数
function main() {
  try {
    // 获取项目名称参数
    const projectName = process.argv[2];

    if (!projectName) {
      log('错误: 请提供项目名称参数');
      log('使用方法: node scripts/dev-nq-config.js <project-name>');
      log('可用项目: aa, qlj');
      process.exit(1);
    }

    // 定义路径
    const configSourceDir = path.join(
      rootDir,
      'apps',
      'config-project',
      projectName,
      'newquery',
      'static-config'
    );
    const configTargetDir = path.join(rootDir, 'apps', 'newquery', 'public', 'static-config');

    // 检查源配置目录是否存在
    if (!fs.existsSync(configSourceDir)) {
      log(`错误: 配置目录不存在: ${configSourceDir}`);
      log(`可用项目配置目录:`);
      const configProjectDir = path.join(rootDir, 'apps', 'config-project');
      if (fs.existsSync(configProjectDir)) {
        const projects = fs
          .readdirSync(configProjectDir, { withFileTypes: true })
          .filter(entry => entry.isDirectory())
          .map(entry => entry.name);
        projects.forEach(project => log(`  - ${project}`));
      }
      process.exit(1);
    }

    log(`开始为项目 "${projectName}" 配置 newquery 开发环境`);

    // 步骤 1: 复制配置文件
    copyDir(configSourceDir, configTargetDir);

    log('配置文件复制完成');

    // 步骤 2: 启动开发服务器
    log('启动 newquery 开发服务器...');
    execSync('pnpm --filter newquery dev', {
      stdio: 'inherit',
      cwd: rootDir,
    });
  } catch (error) {
    log(`执行过程中出错: ${error.message}`);
    process.exit(1);
  }
}

// 执行主函数
main();
