<template>
  <div
    class="w-[330px] rounded-[22px] bg-white border border-solid border-[#F1F1F1] p-[20px]"
  >
    <div class="text-22 font-zhongcu mb-[5px] text-[#00080E]">
      {{ formattedData.value || "0" }}{{ formattedData.unit || "元" }}
    </div>
    <div class="text-xs font-zhongcu text-[#00080E] mb-[20px]">
      {{ data.title || "商机总金额" }}
    </div>
    <div ref="chartRef" class="w-full min-h-[100px]"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from "vue";
import * as echarts from "echarts";
import type { EChartsType } from "echarts";
import { formatAmount } from "../../utils";

const props = defineProps<{
  data: {
    title?: string;
    total_estimated_amount: number;
    items: Array<{
      name: string;
      value: number;
      color: string;
    }>;
  };
}>();

const processedItems = computed(() => {
  const items = props.data.items;
  if (items.length <= 4) return items;

  const topItems = items.slice(0, 3);
  const otherItems = items.slice(3);

  const otherTotal = otherItems.reduce((sum, item) => sum + item.value, 0);
  const otherItem = {
    name: "其他",
    value: otherTotal,
    color: "#CCCCCC", // 为"其他"类别使用灰色
  };

  return [...topItems, otherItem];
});

const formattedData = computed(() => {
  if (props.data.total_estimated_amount == 0) {
    return { value: 0, unit: "" };
  }
  const { value, unit } = formatAmount(
    props.data.total_estimated_amount,
    false
  );
  return { value, unit };
});

const chartRef = ref<HTMLElement | null>(null);
let chartInstance: EChartsType | null = null;
const selectedItems = ref<Set<string>>(new Set());

const initChart = () => {
  if (!chartRef.value) return;

  if (!chartInstance) {
    chartInstance = echarts.init(chartRef.value);
  }

  const currentData = {
    ...props.data,
    items: processedItems.value,
  };

  // 初始化选中状态
  const selectedMap = Object.fromEntries(
    currentData.items.map((item) => [item.name, true])
  );

  // 计算选中项目的总和
  const calculateSelectedTotal = () => {
    return currentData.items
      .filter((item) => selectedMap[item.name])
      .reduce((sum, item) => sum + item.value, 0);
  };

  const option = {
    legend: {
      orient: "vertical",
      right: "15%",
      top: "middle",
      itemWidth: 6,
      itemHeight: 6,
      icon: "circle",
      itemGap: 10,
      textStyle: {
        fontSize: 12,
        color: "#505D70",
        rich: {
          name: {
            fontSize: 12,
            color: "red",
          },
        },
      },
      formatter: (name: string) => {
        return " " + name;
      },
      selected: selectedMap,
    },
    series: [
      {
        type: "pie",
        radius: ["45%", "60%"],
        center: ["25%", "50%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 2,
          borderWidth: 2,
          borderColor: "#ffffff",
        },
        label: {
          show: false,
          position: "outside",
          formatter: "{c}",
          fontSize: 12,
          fontWeight: 400,
          color: function (params: any) {
            return "red";
            // return currentData.items[params.dataIndex].color;
          },
          distance: -5,
        },
        labelLine: {
          show: false,
        },
        emphasis: {
          scale: false,
          label: {
            show: true,
          },
        },
        data: currentData.items.map((item) => ({
          name: item.name,
          value: item.value,
          label: {
            show: true,
            formatter: "{c}",
            color: item.color,
            fontSize: 12,
            // distance: 0,
            padding: [0, -30],
          },
          itemStyle: {
            color: item.color,
          },
        })),
      },
      {
        type: "pie",
        radius: ["0%", "55%"],
        center: ["25%", "50%"],
        label: {
          show: true,
          position: "center",
          formatter: "{c}",
          fontSize: 12,
          color: "#00080E",
          fontWeight: "bold",
        },
        data: [
          {
            value: calculateSelectedTotal(),
            name: "",
            itemStyle: {
              color: "rgba(255, 255, 255, 0.5)",
            },
          },
        ],
      },
      {
        type: "pie",
        radius: ["0%", "32%"],
        center: ["25%", "50%"],
        label: {
          show: true,
          position: "center",
          formatter: "{c}",
          fontSize: 32,
          color: "#00080E",
          fontWeight: "normal",
        },
        data: [
          {
            value: 1,
            name: "",
            itemStyle: {
              color: "transparent",
            },
          },
        ],
      },
    ],
  };

  chartInstance.setOption(option);

  // 添加图例选择改变事件监听
  chartInstance.on("legendselectchanged", function (params: any) {
    Object.assign(selectedMap, params.selected);
    // 更新总金额
    chartInstance?.setOption({
      series: [
        {}, // 第一个饼图保持不变
        {
          // 更新第二个饼图（中心总金额）
          data: [
            {
              value: calculateSelectedTotal(),
              name: "",
              itemStyle: {
                color: "rgba(255, 255, 255, 0.5)",
              },
            },
          ],
        },
        {}, // 第三个饼图保持不变
      ],
    });
  });
};

onMounted(() => {
  initChart();
  window.addEventListener("resize", () => {
    chartInstance?.resize();
  });
});

watch(
  () => props.data,
  () => {
    initChart();
  },
  { deep: true }
);
</script>

<style scoped>
.business-amount-component {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}
</style>
