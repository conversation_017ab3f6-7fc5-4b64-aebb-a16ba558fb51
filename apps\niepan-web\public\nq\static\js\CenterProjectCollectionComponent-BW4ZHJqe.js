import{f as m}from"./ChatView-BP1WmwpV.js";import{d as h,s as d,r as c,z as _,ad as x,c as w,o as g,b as o,t as e}from"./pnpm-pnpm-B4aX-tnA.js";import"./index-oCQpNzhc.js";const b={class:"w-[330px] h-[110px] rounded-[22px] bg-white border border-solid border-[#F1F1F1] flex justify-between p-[20px]"},y={class:"text-22 leading-7 font-zhongcu"},A={class:"text-xs font-zhongcu"},S=h({__name:"CenterProjectCollectionComponent",props:{data:{}},setup(f){const s=f;function n(a){if(a==0)return{value:0,unit:""};const{value:u,unit:v}=m(a,!1);return{value:u,unit:v}}const l=d(()=>n(s.data.total_received_amount)),r=d(()=>n(s.data.total_contract_amount)),i=c(null),t=c(null),p={series:[{type:"gauge",startAngle:180,endAngle:-180,center:["50%","50%"],radius:"100%",pointer:{show:!1},progress:{show:!0,overlap:!1,roundCap:!0,itemStyle:{color:"#129BFF"}},axisLine:{lineStyle:{width:10},shadowOffsetX:0,shadowOffsetY:0},splitLine:{show:!1,distance:0,length:0},axisTick:{show:!1},axisLabel:{show:!1,distance:50},data:[{value:50.5,detail:{valueAnimation:!0,offsetCenter:["0%","0%"]}}],title:{fontSize:14},detail:{fontSize:14,color:"#000",formatter:"{value}%"}}]};return _(()=>{t.value&&t.value.dispose(),t.value=x(i.value),t.value.setOption(p)}),(a,u)=>(g(),w("div",b,[o("div",null,[o("div",y,e(l.value.value)+e(l.value.unit)+"/"+e(r.value.value)+e(r.value.unit),1),o("div",A,e(a.data.title),1)]),o("div",{ref_key:"chartRef",ref:i,class:"w-[70px] h-[70px]"},null,512)]))}});export{S as default};
