import { createWrappedApiClient } from '@/services/api-wrapper';
import { stringify } from 'qs';
import { useConfig } from '@/hooks/useConfig';

const { serverConfig } = useConfig();

console.log('api.js - serverConfig:', serverConfig);
console.log(
    'api.js - baseURL will be:',
    `${serverConfig.VITE_API_BASE_URL}/myquery`
);

const apiClientInstance = createWrappedApiClient({
    baseURL: `${serverConfig.VITE_API_BASE_URL}/myquery`,
    headers: {
        'Content-Type': 'multipart/form-data',
    },
    tokenKey: 'console_token',
});

// export const handleLogin = async (username, password) => {
//     return apiClientInstance.post('/api/mylogin/', { username, password });
// };

export const handleLogin = async (username, password, sso) => {
    let params;
    if (sso) {
        params = { username, password, sso };
    } else {
        params = { username, password };
    }
    return apiClientInstance.post('/api/mylogin/', params);
};

// 获取用户信息
export const getSiteuserData = async () => {
    return apiClientInstance.get('/api/get_siteuser_data/');
};

// 获取用户可用应用列表
export const getQueryAppList = async () => {
    return apiClientInstance.get('/views/use-queryapp-list/');
};

// 获取topic列表 qid指定应用的id
export const getTopicList = async (qid) => {
    return apiClientInstance.get('/views/topic-list/', {
        params: { qid },
    });
};

// 获取所有历史聊天记录 tid指定topic的id
export const getTaskmessages = async (tid) => {
    return apiClientInstance.get('/views/taskmessage-list/', {
        params: { tid },
    });
};

// 发送聊天
// stream：0正常请求  1流模式请求
export const sendTaskmessage = async (
    content,
    stream,
    qid,
    tid,
    stream_out_type = 'object'
) => {
    if (stream) {
        const token = localStorage.getItem('console_token');
        const formData = new FormData();
        formData.append('content', content);
        let baseMessageUrl = `${serverConfig.VITE_API_BASE_URL}/myquery/views/taskmessage-add/?stream=${stream}&stream_out_type=${stream_out_type}`;
        if (qid) {
            baseMessageUrl += `&qid=${qid}`;
        }
        if (tid) {
            baseMessageUrl += `&tid=${tid}`;
        }
        const response = await fetch(baseMessageUrl, {
            method: 'POST',
            headers: {
                Authorization: `Bearer ${token}`,
            },
            body: formData,
            // timeout: 0,  //永不超时
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        return response.body;
    } else {
        const response = await apiClientInstance.post(
            '/views/taskmessage-add/',
            { content },
            {
                params: { stream, qid, tid },
            }
        );
        return response;
    }
};

// 获取所有知识库列表
export const getPageKBaseList = async (page, count) => {
    return apiClientInstance.get('/ws/kbases/page', {
        params: { page, count },
    });
};

// 创建知识库
export const createKBaseAction = async (
    title,
    description,
    type,
    kbase_type
) => {
    return apiClientInstance.post('/ws/kbases/add', {
        title,
        description,
        type,
        // kbase_type,
    });
};

// 编辑知识库
export const updateKBaseAction = async (
    title,
    description,
    kid,
    siteuser_ids,
    kbase_type
) => {
    return apiClientInstance.post(`ws/kbases/${kid}/modify`, {
        title,
        description,
        siteuser_ids,
        // kbase_type,
    });
};

// 获取知识库详情
export const getKBaseDetail = async (kid) => {
    return apiClientInstance.get(`/ws/kbases/${kid}`, {});
};

// 获取数据集列表
export const getDocList = async (kid, p, count, keyword) => {
    return apiClientInstance.get('/ws/documents/page', {
        params: { kbase_id: kid, page: p, count, keyword },
    });
};

// 获取数据集列表
export const getDocDetail = async (kid, did) => {
    return apiClientInstance.get(`/ws/documents/` + did, {
        params: { kbase_id: kid },
    });
};

// 删除docment
export const delDoc = async (kid, did) => {
    return apiClientInstance.post(`/ws/documents/` + did + `/delete`, {
        kbase_id: kid,
    });
};

// 获取应用列表，在后台管理中的应用列表
export const getMyQueryAppList = async (p, count) => {
    return apiClientInstance.get('/api/queryapp-list/', {
        params: { p, count },
    });
};

// 创建应用
export const createQueryappOne = async (
    title,
    description,
    embedding_model,
    icon_path
) => {
    return apiClientInstance.post('/api/queryapp-add/', {
        title,
        description,
        embedding_model,
        icon_path,
    });
};

// 编辑应用
export const createQueryapp = async (
    title,
    description,
    retrival_type,
    qid,
    history_limit
) => {
    return apiClientInstance.post(
        '/api/queryapp-add/',
        { title, description, retrival_type, history_limit },
        {
            params: { qid },
        }
    );
};

// 获取详情
export const getQueryappDetail = async (qid) => {
    return apiClientInstance.get('/api/queryapp-detail/', {
        params: { qid },
    });
};

// 删除应用
export const delQueryappApi = async (qid) => {
    return apiClientInstance.get('/api/queryapp-del/', {
        params: { qid },
    });
};

// 上下架应用
export const setQueryappStatusApi = async (qid, status) => {
    return apiClientInstance.get('/api/set-queryapp-status/', {
        params: { qid, status },
    });
};

// 更新应用
export const updateQueryappApi = async (
    qid,
    prompt,
    opening_statement,
    kids = '',
    aids = '',
    ftids = '',
    sids = '',
    dids = '',
    model_name = ''
) => {
    return apiClientInstance.post(
        '/api/queryapp-update/',
        {
            prompt,
            opening_statement,
            kids,
            aids,
            ftids,
            sids,
            dids,
            model_name,
        },
        {
            params: { qid },
        }
    );
};

// 创建doc
export const test = async (file) => {
    return apiClientInstance.post('/api/test/', { file });
};

// // 获取数据集状态
// export const getDocStatus = async (kid, did) => {
//     return apiClientInstance.get('/api/get-document-status/',{
//         params:{kid, did}
//     });
// };

// // 更新数据集
// export const updateDocApi = async (kid, dids, learn_type) => {
//     return apiClientInstance.post('/api/document-update/',
//         {dids, learn_type},
//         {
//             params:{kid}
//         }
//     );
// };

// 获取子部门
export const getDepartmentSons = async (did) => {
    return apiClientInstance.get('/api/get-department-sons/', {
        params: { did },
    });
};

// 获取子部门
export const getAllDepartments = async () => {
    return apiClientInstance.get('/api/get-all-departments/', {});
};

// 获取指定部门的员工
export const getstaffs = async (did) => {
    return apiClientInstance.get('/api/get-department-staffs/', {
        params: { did },
    });
};

// 获取没有岗位的员工
export const getNoPostStaffs = async () => {
    return apiClientInstance.get('/api/get-all-no-position-siteusers/', {});
};

// 获取指定部门的员工
export const getAllStaffs = async () => {
    return apiClientInstance.get('/api/get-all-staffs/', {});
};

// 获取当前部门的所有父部门
export const getDepartmentFathers = async (did) => {
    return apiClientInstance.get('/api/get-department-fathers/', {
        params: { did },
    });
};

// 获取所有知识库列表
export const getAllKBaseList = async () => {
    return apiClientInstance.get('/ws/kbases/all', {});
};

// 删除聊天
export const delTaskmessageAction = async (qid, tid, tmid) => {
    return apiClientInstance.post('/views/taskmessage-delete/', {
        qid,
        tid,
        tmid,
    });
};

// 获取数据集文档切片列表
export const getDocFragmentListAction = async (kid, dcid, keyword) => {
    return apiClientInstance.get(
        `/ws/documents/` + dcid + `/doc_fragments/all`,
        {
            params: { kbase_id: kid, keyword },
        }
    );
};

// 获取指定数据集文档切片
export const getDocFragmentAction = async (kid, dcid, dfid) => {
    return apiClientInstance.get(
        `/ws/documents/` + dcid + `/doc_fragments/` + dfid,
        {
            params: { kbase_id: kid },
        }
    );
};

// 修改指定数据集文档切片
export const editDocFragmentAction = async (kid, dcid, dfid, content) => {
    return apiClientInstance.post(
        `/ws/documents/` + dcid + `/doc_fragments/` + dfid + `/modify`,
        { kbase_id: kid, content }
    );
};

// 新建指定数据集文档切片
export const addDocFragmentAction = async (kid, dcid, content) => {
    return apiClientInstance.post(
        `/ws/documents/` + dcid + `/doc_fragments/add`,
        { kbase_id: kid, content }
    );
};

// 更新数据集
export const addDocTextApi = async (
    kid,
    did,
    title,
    content,
    learn_type,
    type
) => {
    let post_data = { title, content, learn_type, type };
    if (did) {
        post_data['did'] = did;
    }
    return apiClientInstance.post(
        `/ws/documents/` + did + `/doc_fragments/add`,
        post_data,
        {
            params: { kid },
        }
    );
};

// 删除指定数据集文档切片
export const delDocFragmentAction = async (kid, dcid, dfid) => {
    return apiClientInstance.post(
        `/ws/documents/` + dcid + `/doc_fragments/` + dfid + `/delete`,
        { kbase_id: kid }
    );
};

// 检索测试
export const searchTestAction = async (kbase_id, content) => {
    return apiClientInstance.post(`/ws/kbases/${kbase_id}/test`, { content });
};

// 封装上传视频的函数
export const uploadVideo = async (formData) => {
    const response = await apiClientInstance.post('api/test-video/', formData, {
        headers: {
            'Content-Type': 'multipart/form-data', //上传文件的方式就是这种
        },
    });
    return response;
};

// 获取tool列表
export const getToolListPageAction = async (page, count) => {
    return apiClientInstance.get(`/ws/query_tools/page`, {
        params: { page, count },
    });
};

// 创建tool
export const createToolAction = async (name, description) => {
    return apiClientInstance.post(`/ws/query_tools/add`, { name, description });
};

// 获取tool
export const getToolAction = async (tid) => {
    return apiClientInstance.get(`/ws/query_tools/` + tid, {
        params: {},
    });
};

// 编辑tool
export const editToolAction = async (
    tid,
    apiurl,
    apimethod,
    apiheaders,
    apiqueryparams,
    apibody,
    response,
    fun_name
) => {
    const data = {
        apiurl,
        apimethod,
        apiheaders: apiheaders || '{}', // 确保是JSON格式的字符串
        apiqueryparams: apiqueryparams || '{}', // 确保是JSON格式的字符串
        apibody: apibody || '{}', // 确保是JSON格式的字符串
        response: response || '{}', // 确保是JSON格式的字符串
        fun_name: fun_name || '',
    };
    return apiClientInstance.post(`/ws/query_tools/${tid}/modify`, data);
};

// 修改tool名字、介绍、添加使用人员
export const updateToolAction = async (
    tid,
    name,
    description,
    siteuser_ids
) => {
    const data = {
        name,
        description,
        siteuser_ids,
    };

    return apiClientInstance.post(`/ws/query_tools/${tid}/modify`, data);
};

// 删除工具
export const delToolAction = async (tid) => {
    return apiClientInstance.post(`/ws/query_tools/${tid}/delete`);
};

// 删除知识库
export const delKBaseAction = async (kid) => {
    return apiClientInstance.post(`/ws/kbases/${kid}/delete`);
};

// 获取tool列表，无翻页
export const getToolsAllAction = async () => {
    return apiClientInstance.get(`/ws/query_tools/all`, {});
};

// tool工具api接口验证
export const toolValidateAction = async (tid, query_params, request_body) => {
    return apiClientInstance.post(`/ws/query_tools/${tid}/validate`, {
        query_params,
        request_body,
    });
};

// // 更新数据集
// export const addDocTextApi1 = async (kid, did, title, content, learn_type, type) => {
//     let post_data = {title, content, learn_type, type}
//     if (did){
//         post_data['did'] = did
//     }
//     return apiClientInstance.post('/api/documents/add-text/',
//         post_data,
//         {
//             params:{kid}
//         }
//     );
// };

// 创建文档
export const addDocumentAction = async (
    kbase_id,
    learn_type,
    type = '',
    title = '',
    content = '',
    file = ''
) => {
    let post_data = { kbase_id, learn_type };
    if (type == 'text') {
        post_data['title'] = title;
        post_data['content'] = content;
        post_data['type'] = type;
    } else {
        post_data['file'] = file;
    }
    return apiClientInstance.post('/ws/documents/add', post_data);
};

// 学习文档
export const learnDocmentAction = async (kbase_id, did) => {
    return apiClientInstance.post(
        `/ws/documents/${did}/learn`,
        { kbase_id },
        { timeout: 0 } //请求永不超时，没有超时时间
    );
};

// 删除应用的指定上下文
export const delTopicAction = async (qid, tid) => {
    return apiClientInstance.post('/views/topic-delete/', { qid, tid });
};

// 获取queryfiletemplate列表
export const getFileTemplateListPageAction = async (page, count) => {
    return apiClientInstance.get(`/ws/query_file_templates/page`, {
        params: { page, count },
    });
};

// 获取queryfiletemplate列表
export const getFileTemplateListAction = async () => {
    return apiClientInstance.get(`/ws/query_file_templates/all`, {
        params: {},
    });
};

// 获取文件模板
export const getFileTemplateAction = async (ftid) => {
    return apiClientInstance.get(`/ws/query_file_templates/` + ftid, {
        params: {},
    });
};

// 新建文件模板
export const addFileTemplateAction = async (
    name,
    description,
    type,
    params,
    file
) => {
    const data = {
        name,
        description,
        type,
        params,
        file,
    };
    return apiClientInstance.post(`/ws/query_file_templates/add`, data);
};

// 编辑文件模板
export const editFileTemplateAction = async (
    ftid,
    name,
    description,
    params,
    file
) => {
    const data = {
        name,
        description,
        params,
        file,
    };
    return apiClientInstance.post(
        `/ws/query_file_templates/` + ftid + `/modify`,
        data
    );
};

// 删除文件模板
export const deleteFileTemplateAction = async (ftid) => {
    return apiClientInstance.post(
        `/ws/query_file_templates/` + ftid + `/delete`,
        {},
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
};

export const getTaskMessageAttachments = async (tmid) => {
    return apiClientInstance.post(`/views/taskmessage-attachments/`, { tmid });
};

// 获取用户sso_token
export const getSiteuserSsoTokenAction = async (sso) => {
    return apiClientInstance.get('/api/get-siteuser-sso-token/', {
        params: { sso },
    });
};

// 获取appremodel列表
export const getAppReModelListApi = async (page, count) => {
    return apiClientInstance.get('/ws/app_remodels/page', {
        params: { page, count },
    });
};

// 删除appremodel，批量删除或单个删除
export const delAppReModelApi = async (app_remodel_ids) => {
    return apiClientInstance.post(
        `/ws/app_remodels/batch_delete`,
        {
            app_remodel_ids: app_remodel_ids,
        },
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
};

// 修改模型状态，批量修改或单个修改 status:0 禁用，1 启用
export const updateAppReModelStatusApi = async (app_remodel_ids, status) => {
    return apiClientInstance.post(
        `/ws/app_remodels/batch_update_status`,
        {
            app_remodel_ids: app_remodel_ids,
            status: status,
        },
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
};

// 创建模型
export const addAppRemodelApi = async (app_remodels) => {
    return apiClientInstance.post(
        `/ws/app_remodels/add`,
        {
            app_remodels: app_remodels,
        },
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
};

// 获取modelcraft层模型列表
export const getMcModelListApi = async () => {
    return apiClientInstance.get(`/ws/app_remodels/modelcraft_models_all`, {});
};

// 更新文档描述
export const updateDocDescription = async (kbase_id, did, description) => {
    return apiClientInstance.post(`/ws/documents/${did}/modify`, {
        kbase_id,
        description,
    });
};

// 批量修改文档状态
export const batchModifyDocumentsStatus = async ({
    kbase_id,
    document_ids,
    is_enabled,
}) => {
    return apiClientInstance.post('/ws/documents/batch_modify', {
        kbase_id,
        document_ids,
        is_enabled,
    });
};

// 批量删除文档
export const batchDeleteDocuments = async ({ document_ids, kbase_id }) => {
    return apiClientInstance.post('/ws/documents/batch_delete', {
        document_ids,
        kbase_id,
    });
};

// 批量获取文档状态
export const apiGetBatchDocumentsStatus = async (kbase_id, document_ids) => {
    return apiClientInstance.post('/ws/documents/status', {
        kbase_id,
        document_ids,
    });
};

// 修改文档片段状态
export const modifyDocFragmentStatus = async (kid, dcid, dfid, is_enabled) => {
    return apiClientInstance.post(
        `/ws/documents/${dcid}/doc_fragments/${dfid}/modify_info`,
        { kbase_id: kid, is_enabled }
    );
};

// 删除文档片段要点
export const deleteDocPointApi = async (
    kbase_id,
    document_id,
    doc_fragment_id,
    point_id
) => {
    return apiClientInstance.post(
        `/ws/documents/${document_id}/doc_fragments/${doc_fragment_id}/points/${point_id}/delete`,
        {
            kbase_id,
        }
    );
};

// 添加文档片段要点
export const addDocPointApi = async (
    kbase_id,
    document_id,
    doc_fragment_id,
    content
) => {
    return apiClientInstance.post(
        `/ws/documents/${document_id}/doc_fragments/${doc_fragment_id}/add_point`,
        {
            kbase_id,
            content,
        }
    );
};

// 编辑文档片段要点
export const editDocPointApi = async (
    kbase_id,
    document_id,
    doc_fragment_id,
    point_id,
    content
) => {
    return apiClientInstance.post(
        `/ws/documents/${document_id}/doc_fragments/${doc_fragment_id}/points/${point_id}/modify`,
        {
            kbase_id,
            content,
        }
    );
};

// 搜索模型列表
export const searchAppReModelsApi = async (page, count, keyword) => {
    return apiClientInstance.get('/ws/app_remodels/page', {
        params: { page, count, keyword },
    });
};
