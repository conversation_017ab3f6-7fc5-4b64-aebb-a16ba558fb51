<template>
  <div class="bg-white rounded-[20px] overflow-hidden h-[calc(100vh-120px)] relative z-50">
    <div class="mx-auto max-w-[800px] px-3 py-3">
      <!-- 授权管理标题 -->
      <div class="text-[20px] font-medium text-base px-3 py-4">帮助Agent</div>
      <div>
        <div class="p-3">
          <div class="text-sm font-medium mb-2">WebAPP 链接</div>
          <el-input v-model="helpDocsUrl" type="input" placeholder="请输入" class="w-full" />
        </div>
        <div class="text-right my-3">
          <el-button
            type="primary"
            class="bg-[#129BFE] rounded-[6px]"
            style="background-color: #129bfe"
            @click="handleSubmitSystemConfigs"
          >
            保存
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useMenuV3Store } from '@/store/modules/menuV3';
import { useUserStore } from '@/store/modules/userInfo';
import { useSysConfigsStore } from '@/store/sysConfigs';
import { ElMessage, ElLoading } from 'element-plus';
import { fetchSubmitSystemConfigs } from '@/services/console-api';
import { debounce } from 'lodash';

// 菜单状态管理
const menuStore = useMenuV3Store();
const userStore = useUserStore();
const sysConfigsStore = useSysConfigsStore();
const sysConfigs = computed(() => sysConfigsStore.sysConfigsData);

const helpDocsUrl = ref(''); // 帮助文档URL

// 提交系统配置
const handleSubmitSystemConfigs = debounce(async () => {
  const propsData = {
    data: [],
  };

  if (helpDocsUrl.value) {
    propsData.data.push({ name: 'HELP_DOCS_URL', value: helpDocsUrl.value });
  }

  try {
    // 提交系统配置
    const res = await fetchSubmitSystemConfigs(propsData);
    if (res.data.result == 'success') {
      ElMessage.success('设置成功');
      setTimeout(() => location.reload(), 1000);
    }
  } catch (error) {
    console.error('设置失败:', error);
    ElMessage.error('激活失败');
  }
}, 100); // 防抖时间为1秒

onMounted(async () => {
  // 设置当前激活的菜单
  menuStore.setActiveMenu('system-config');

  // 确保用户信息已加载
  if (!userStore.userInfo) {
    await userStore.fetchUserInfo(null);
  }
  await sysConfigsStore.fetchSysConfigs();

  // 初始化帮助文档URL
  if (sysConfigs.value && sysConfigs.value.length > 0) {
    const helpDocs = sysConfigs.value.find(item => item.name === 'HELP_DOCS_URL');
    if (helpDocs) {
      helpDocsUrl.value = helpDocs.value;
    }
  }
});
</script>

<style scoped>
/* 自定义样式 */
:deep(.el-input__wrapper) {
  padding: 12px !important;
}
:deep(.el-input__inner) {
  height: 24px !important;
}
</style>
