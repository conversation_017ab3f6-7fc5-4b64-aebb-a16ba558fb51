import type { FC } from 'react';
import { useState, useCallback } from 'react';
import { useWorkbenchComponents } from '../../hooks/useWorkbenchComponents';
import type { ChatMessage } from '../../hooks/useWorkbenchMessageHandler';

// 模拟聊天消息示例
const mockChatMessages: ChatMessage[] = [
  {
    id: '1',
    type: 'document_uploaded',
    content: '文档已上传成功',
    metadata: {
      fileUrl: 'http://192.168.10.10:8012/PLM21x_%E9%A3%8E%E5%9C%BAGoldenBOM%E7%AE%A1%E7%90%86%E7%94%A8%E6%88%B7%E6%93%8D%E4%BD%9C%E6%89%8B%E5%86%8Cpptx.pdf',
      fileName: '项目管理手册.pdf'
    }
  },
  {
    id: '2',
    type: 'analysis_complete',
    content: '文档分析已完成！\n\n主要发现：\n• 文档包含完整的项目管理流程\n• 涵盖了风险管理和质量控制\n• 建议重点关注第3-5页的核心流程',
  },
  {
    id: '3',
    type: 'project_list',
    content: '找到相关项目',
    metadata: {
      projects: [
        {
          id: 'proj-001',
          name: '智能制造系统',
          description: '基于AI的智能制造解决方案',
          status: 'active',
          metadata: { progress: '75%', team: 'AI团队' }
        },
        {
          id: 'proj-002',
          name: '质量管理平台',
          description: '企业级质量管理和监控平台',
          status: 'pending',
          metadata: { progress: '45%', team: '质量团队' }
        }
      ]
    }
  },
  {
    id: '4',
    type: 'warning',
    content: '请注意：在执行下一步操作前，建议先备份当前数据，并确保所有相关人员已收到通知。',
  }
];

const ChatIntegrationExample: FC = () => {
  const { 
    renderComponents, 
    handleChatMessage, 
    clearComponents,
    componentStats,
    componentsData 
  } = useWorkbenchComponents();

  const [currentMessageIndex, setCurrentMessageIndex] = useState(-1);
  const [isProcessing, setIsProcessing] = useState(false);

  // 模拟处理聊天消息
  const handleProcessMessage = useCallback(async (messageIndex: number) => {
    if (messageIndex >= mockChatMessages.length) return;

    setIsProcessing(true);
    const message = mockChatMessages[messageIndex];
    
    // 模拟处理延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 处理消息并更新工作台
    handleChatMessage(message);
    
    setCurrentMessageIndex(messageIndex);
    setIsProcessing(false);
  }, [handleChatMessage]);

  // 处理下一条消息
  const handleNextMessage = useCallback(() => {
    const nextIndex = currentMessageIndex + 1;
    if (nextIndex < mockChatMessages.length) {
      handleProcessMessage(nextIndex);
    }
  }, [currentMessageIndex, handleProcessMessage]);

  // 重置演示
  const handleReset = useCallback(() => {
    clearComponents();
    setCurrentMessageIndex(-1);
    setIsProcessing(false);
  }, [clearComponents]);

  // 处理所有消息
  const handleProcessAll = useCallback(async () => {
    clearComponents();
    setCurrentMessageIndex(-1);
    
    for (let i = 0; i < mockChatMessages.length; i++) {
      await handleProcessMessage(i);
      // 每条消息之间间隔1秒
      if (i < mockChatMessages.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }, [clearComponents, handleProcessMessage]);

  return (
    <div className="w-full h-full flex">
      {/* 聊天模拟控制面板 */}
      <div className="w-80 bg-gray-50 border-r p-4 overflow-auto">
        <h2 className="text-lg font-semibold mb-4">聊天集成演示</h2>
        
        {/* 统计信息 */}
        <div className="mb-6 p-3 bg-white rounded border">
          <h3 className="font-medium mb-2">工作台状态</h3>
          <div className="text-sm text-gray-600">
            <div>组件总数: {componentStats.total}</div>
            <div>当前消息: {currentMessageIndex + 1}/{mockChatMessages.length}</div>
          </div>
        </div>

        {/* 控制按钮 */}
        <div className="mb-6 space-y-2">
          <button
            onClick={handleNextMessage}
            disabled={isProcessing || currentMessageIndex >= mockChatMessages.length - 1}
            className="w-full px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessing ? '处理中...' : '处理下一条消息'}
          </button>
          
          <button
            onClick={handleProcessAll}
            disabled={isProcessing}
            className="w-full px-3 py-2 text-sm bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            处理所有消息
          </button>
          
          <button
            onClick={handleReset}
            disabled={isProcessing}
            className="w-full px-3 py-2 text-sm bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50"
          >
            重置演示
          </button>
        </div>

        {/* 消息列表 */}
        <div>
          <h3 className="font-medium mb-3">模拟消息队列</h3>
          <div className="space-y-2">
            {mockChatMessages.map((message, index) => (
              <div
                key={message.id}
                className={`p-3 rounded border text-sm ${
                  index <= currentMessageIndex
                    ? 'bg-green-50 border-green-200'
                    : index === currentMessageIndex + 1 && isProcessing
                    ? 'bg-yellow-50 border-yellow-200'
                    : 'bg-white border-gray-200'
                }`}
              >
                <div className="flex items-center justify-between mb-1">
                  <span className="font-medium text-xs text-gray-500">
                    {message.type}
                  </span>
                  <span className="text-xs text-gray-400">
                    {index <= currentMessageIndex ? '✓' : 
                     index === currentMessageIndex + 1 && isProcessing ? '⏳' : '⏸'}
                  </span>
                </div>
                <div className="text-gray-700 line-clamp-2">
                  {message.content}
                </div>
                {message.metadata && (
                  <div className="mt-1 text-xs text-gray-500">
                    {Object.keys(message.metadata).length} 个附加数据
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 工作台预览区域 */}
      <div className="flex-1 p-4 overflow-auto">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-xl font-semibold mb-4">
            工作台实时更新演示
            {isProcessing && (
              <span className="ml-2 text-sm text-blue-500">正在处理消息...</span>
            )}
          </h2>
          
          <div className="bg-white rounded-lg border min-h-96">
            <div className="p-4">
              {renderComponents('main')}
            </div>
          </div>

          {/* 使用说明 */}
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-2">使用说明</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 点击"处理下一条消息"逐步查看工作台更新</li>
              <li>• 点击"处理所有消息"查看完整的聊天流程</li>
              <li>• 每种消息类型会自动生成对应的工作台组件</li>
              <li>• 这演示了聊天过程中工作台的动态更新机制</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatIntegrationExample;
