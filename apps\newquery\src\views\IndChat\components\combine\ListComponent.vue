<!-- 列表组件 -->
<template>
    <div class="w-[360px] rounded-[22px] bg-white border border-solid border-[#F1F1F1] p-[20px] text-xs">
        <div class="flex justify-between items-center">
            <div>
                <span class="text-3xl font-zhongcu">{{ data.totalNum }}</span>
                <span class="font-zhongcu ml-1">{{ data.title }}</span>
            </div>
        </div>
        <div class="mt-[10px]">
            <div 
                v-for="(item, index) in data?.items"
                :key="index"
                class="flex justify-between item-center py-[8px] border-0 border-b border-[#ECECF2] border-solid text-xs"
            >
                <span class="overflow-one w-[80%]">{{ item.title }} </span>
                <span class="px-[5px] overflow-one">{{ item.status }}</span>
            </div>
            <a v-if="data.isMore" :href="linkUrl"  target="_blank"  class="text-[#129BFE] mt-[5px] block">{{ data.moreTitle }}</a>
        </div> 
    </div>
</template>

<script setup lang="ts">
import { computed } from "vue";


interface ModelData {
    totalNum: number;
    title?:string;
    isMore?:boolean;
    moreTitle?:string;
    link?:string;
    items?: Array<{
        title: string;
        status: string;
    }>;
}

const props = defineProps<{
    data: ModelData;  //必填项
}>();

const linkUrl = computed(() => {
    if(props.data.link){
        return `${props.data.link}`
    }else{
        return 'javascript:;'
    }
});
</script>