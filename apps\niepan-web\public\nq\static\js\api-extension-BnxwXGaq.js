import{w as t,x as n}from"./index-oCQpNzhc.js";import{d as a,r,z as c,c as i,o as l,b as p}from"./pnpm-pnpm-B4aX-tnA.js";const u={class:"bg-white rounded-[20px] overflow-hidden h-[calc(100vh-120px)] relative z-50"},d=["src"],x=a({__name:"api-extension",setup(f){const s=t(),{globalConfig:e}=n(),o=r("");return c(()=>{s.setActiveMenu("api_extension"),e.workspaceSettings&&e.workspaceSettings.apiExtensions?o.value=e.workspaceSettings.apiExtensions:(console.warn("API扩展嵌入URL未配置，请检查config.js文件"),o.value="http://localhost:3000/embed/api-extensions")}),(m,_)=>(l(),i("div",u,[p("iframe",{src:o.value,class:"w-full h-full border-0",frameborder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowfullscreen:""},null,8,d)]))}});export{x as default};
