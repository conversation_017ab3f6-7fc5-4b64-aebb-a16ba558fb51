import{d as J,A as Y,o as i,m as U,b as e,n as G,c as v,u as ne,r as l,z as ce,E as s,ae as W,Q as O,e as C,f as _,w as Q,k as X,v as q,aa as H,J as N,K as re,a as y,t as A,x as de,y as ie,aO as we,_ as be,X as xe,S as ye,D as ke,p as Z,q as Ie,N as De}from"./pnpm-pnpm-B4aX-tnA.js";import{_ as se,V as Ae,W as te,X as Fe,Y as Le,Z as Be,$ as Ee,a as ue,v as Se,B as Me,a0 as ze,a1 as Ve,z as Te,a2 as He,a3 as Ne,a4 as Pe,a5 as Ue,a6 as Ke,a7 as Re,P as je,a8 as Ze,a9 as Oe,aa as $e}from"./index-oCQpNzhc.js";import{_ as We}from"./BackArrowIcon.vue_vue_type_script_setup_true_lang-BntbR4_Q.js";import{_ as Qe}from"./SearchIcon.vue_vue_type_script_setup_true_lang-4BGMlqW3.js";import{_ as Xe,a as qe}from"./UploadThinIcon.vue_vue_type_script_setup_true_lang-D5ZNlFf9.js";import{_ as Je}from"./DeleteIcon.vue_vue_type_script_setup_true_lang-TAzPfaAy.js";const Ye=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 18 18"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M9.7727 3.75098C9.77324 3.33677 9.43789 3.00054 9.02368 3C8.60946 2.99946 8.27324 3.33481 8.2727 3.74902L8.26681 8.25H3.75C3.33579 8.25 3 8.58579 3 9C3 9.41421 3.33579 9.75 3.75 9.75H8.26485L8.25897 14.249C8.25843 14.6632 8.59378 14.9995 9.00799 15C9.42221 15.0005 9.75843 14.6652 9.75897 14.251L9.76486 9.75H14.25C14.6642 9.75 15 9.41421 15 9C15 8.58579 14.6642 8.25 14.25 8.25H9.76682L9.7727 3.75098Z"})],-1),Ge={name:"AddIcon"},eo=J({...Ge,props:{color:{default:"white"},size:{default:18},className:{default:""}},setup(p){return(c,r)=>(i(),Y(se,{color:c.color,size:c.size,class:G(c.className)},{default:U(()=>[Ye]),_:1},8,["color","size","class"]))}}),oo={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20"},to=["fill"],so={name:"PopCloseIcon"},le=J({...so,props:{color:{default:"#697077"},size:{default:26},className:{default:""}},setup(p){return(c,r)=>(i(),Y(se,{color:c.color,size:c.size,class:G(c.className)},{default:U(()=>[(i(),v("svg",oo,[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",fill:c.color,d:"M3.92259 2.74408C3.59715 2.41864 3.06951 2.41864 2.74408 2.74408C2.41864 3.06951 2.41864 3.59715 2.74408 3.92259L8.82149 10L2.74408 16.0774C2.41864 16.4028 2.41864 16.9305 2.74408 17.2559C3.06951 17.5814 3.59715 17.5814 3.92259 17.2559L10 11.1785L16.0774 17.2559C16.4028 17.5814 16.9305 17.5814 17.2559 17.2559C17.5814 16.9305 17.5814 16.4028 17.2559 16.0774L11.1785 10L17.2559 3.92259C17.5814 3.59715 17.5814 3.06951 17.2559 2.74408C16.9305 2.41864 16.4028 2.41864 16.0774 2.74408L10 8.82149L3.92259 2.74408Z"},null,8,to)]))]),_:1},8,["color","size","class"]))}}),ao=e("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 48 48"},[e("g",{"clip-path":"url(#clip0_1282_11778)"},[e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M9.0004 17C8.05305 17 7.23566 17.6647 7.04244 18.5921L2.40585 28.7926C2.19023 29.0769 2.0489 29.4207 2.01079 29.7949C2.00214 29.8779 1.99876 29.9604 2.0004 30.042V43C2.0004 44.1046 2.89583 45 4.0004 45H44.0004C45.105 45 46.0004 44.1046 46.0004 43V30.0419C46.002 29.9606 45.9987 29.8785 45.9901 29.796C45.9522 29.4213 45.8108 29.0772 45.5949 28.7926L40.9584 18.5921C40.7651 17.6647 39.9478 17 39.0004 17H9.0004ZM40.8476 28L37.3741 21H10.6267L7.1532 28H14.9095C15.7907 28 16.568 28.5767 16.8235 29.42L18.2114 34H29.7894L31.1773 29.42C31.4328 28.5767 32.2101 28 33.0913 28H40.8476ZM6.0004 41V32H13.4258L14.8137 36.58C15.0692 37.4233 15.8465 38 16.7277 38H31.2731C32.1543 38 32.9316 37.4233 33.1871 36.58L34.5751 32H42.0004V41H6.0004Z",fill:"#C1C7CD"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M24 0C25.1046 0 26 0.89543 26 2V9C26 10.1046 25.1046 11 24 11C22.8954 11 22 10.1046 22 9V2C22 0.89543 22.8954 0 24 0Z",fill:"#C1C7CD"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M46.1925 5.41421C46.9736 6.19526 46.9736 7.46159 46.1925 8.24264L41.2428 13.1924C40.4617 13.9734 39.1954 13.9734 38.4144 13.1924C37.6333 12.4113 37.6333 11.145 38.4144 10.364L43.3641 5.41421C44.1452 4.63316 45.4115 4.63316 46.1925 5.41421Z",fill:"#C1C7CD"}),e("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M1.41421 5.58554C2.19526 4.80449 3.46159 4.80449 4.24264 5.58554L9.19239 10.5353C9.97344 11.3163 9.97344 12.5827 9.19239 13.3637C8.41134 14.1448 7.14501 14.1448 6.36396 13.3637L1.41421 8.41397C0.633165 7.63292 0.633165 6.36659 1.41421 5.58554Z",fill:"#C1C7CD"})]),e("defs",null,[e("clipPath",{id:"clip0_1282_11778"},[e("rect",{width:"48",height:"48",fill:"white"})])])],-1),lo={name:"PopQushengIcon"},no=J({...lo,props:{color:{default:"#C1C7CD"},size:{default:48},className:{default:""}},setup(p){return(c,r)=>(i(),Y(se,{color:c.color,size:c.size,class:G(c.className)},{default:U(()=>[ao]),_:1},8,["color","size","class"]))}}),ee=p=>(de("data-v-ecf2a7b3"),p=p(),ie(),p),co={class:"new-modal-overlay"},ro={class:"new-modal-container max-w-[1320px] w-[68vw]",style:{padding:"24px"}},io={class:"new-modal-top flex items-center justify-between"},uo=ee(()=>e("div",{class:"font18 font-zhongcu"},"分段详情",-1)),mo={class:"new-modal-center flex justify-between",style:{margin:"18px 0 0"}},vo={class:"w-[67%] mr-5"},fo={class:"new-modal-bottom mt-3 text-base"},_o={class:"w-[33%] flex-shrink-0"},ho=ee(()=>e("div",{class:"font16 font-zhongcu leading-6 mb-[9px]"},"关联问题",-1)),po={class:"overflow-y h-[478px]"},go={key:0,class:"h-full flex flex-col items-center justify-center"},Co=ee(()=>e("div",{class:"text-sm text-[#A2A9B0]"}," 基础版和普通版暂无关联问题, ",-1)),wo=ee(()=>e("div",{class:"text-sm text-[#A2A9B0]"},"请切换到高级版",-1)),bo={key:1,class:""},xo={class:"w-full p-[6px] rounded-lg overflow-hidden bg-[#F5F5F5]"},yo={class:"new-modal-bottom mt-2 text-xs"},ko={class:"docpoint-list"},Io={class:"text-sm w-[80%] leading-6 multi-line-ellipsis"},Do={class:"flex-shrink-0"},Ao=["onClick"],Fo=["onClick"],Lo={key:0,class:"p-[6px] rounded-lg overflow-hidden bg-[#F5F5F5] absolute top-0 left-0 w-full h-full flex flex-col justify-between"},Bo={class:"new-modal-bottom mt-2 text-xs flex-shrink-0"},Eo=J({__name:"DocFragmentDetail",props:{kbaseId:{},documentId:{},docfragmentId:{}},emits:["close","onSave"],setup(p,{emit:c}){const r=p;ne();const o=c,f=()=>{o("close")},d=l(""),V=l([]),w=l(null),E=l({});ce(()=>{console.log("props received:",r),k()});const k=async()=>{try{console.log("calling API with:",{kbaseId:r.kbaseId,documentId:r.documentId,docfragmentId:r.docfragmentId});const a=await Ae(r.kbaseId,r.documentId,r.docfragmentId);console.log("getDocFragmentAction response",a),a.data.error=="0"?(d.value=a.data.content,V.value=a.data.docpoints,E.value=a.data.document):s.error("获取分段内容失败")}catch{s.error("获取详情失败")}},P=W.debounce(async()=>{if(w.value=O.service({lock:!0,text:"提交中",background:"rgba(0, 0, 0, 0.5)"}),d.value)try{const a=await te(r.kbaseId,r.documentId,r.docfragmentId,d.value);a.data.error=="0"?(s.success("保存成功"),o("onSave",{id:r.docfragmentId,content:d.value}),f()):a.data.error?s.error("保存失败，"+a.data.message):s.error("保存失败")}catch{s.error("保存失败")}finally{w.value.close()}else s("请填写分段内容")}),L=l(!1),S=l("永久删除问题"),R=l("该问题将被永久删除，不可恢复及撤销。确定要删除吗？"),T=l(""),b=l(""),F=a=>{L.value=!0,T.value=a},M=()=>{L.value=!1,T.value=""},x=W.debounce(async()=>{L.value=!1;try{const a=await Fe(r.kbaseId,r.documentId,r.docfragmentId,T.value);a.data.error=="0"?(s.success("删除成功"),k()):a.data.message?s.error(a.data.message):s.error("删除失败")}catch(a){console.error("删除失败",a),s.error("删除失败")}}),n=W.debounce(async()=>{if(console.log(b.value),!b.value.trim()){s("请填写关联问题内容");return}w.value=O.service({lock:!0,text:"保存中",background:"rgba(0, 0, 0, 0.5)"});try{const a=await Le(r.kbaseId,r.documentId,r.docfragmentId,b.value);a.data.error=="0"?(s.success("保存成功"),b.value="",k()):a.data.message?s.error(a.data.message):s.error("保存失败")}catch(a){console.error("保存失败",a),s.error("保存失败")}finally{w.value.close()}}),I=l(""),u=l(""),$=(a,m)=>{I.value=a,u.value=m},j=W.debounce(async()=>{if(console.log(I.value,u.value),!u.value.trim()){s("请填写要点内容");return}w.value=O.service({lock:!0,text:"保存中",background:"rgba(0, 0, 0, 0.5)"});try{const a=await Be(r.kbaseId,r.documentId,r.docfragmentId,I.value,u.value);a.data.error=="0"?(s.success("保存成功"),I.value="id",u.value="",k()):a.data.message?s.error(a.data.message):s.error("保存失败")}catch(a){console.error("保存失败",a),s.error("保存失败")}finally{w.value.close()}});return(a,m)=>{const B=C("v2ConfirmsModal");return i(),v(N,null,[e("div",co,[e("div",ro,[e("div",io,[uo,e("button",{onClick:Q(f,["stop"]),class:"p-0 border-0 bg-transparent"},[_(le,{class:"w-6 h-6"})])]),e("div",mo,[e("div",vo,[X(e("textarea",{class:"modal-edit-content overflow-y text-base","onUpdate:modelValue":m[0]||(m[0]=h=>d.value=h)},null,512),[[q,d.value]]),e("div",fo,[e("button",{type:"button",onClick:f,class:"new-modal-bottom-cancel"}," 取消 "),e("button",{type:"button",onClick:m[1]||(m[1]=(...h)=>H(P)&&H(P)(...h)),class:"new-modal-bottom-confirm"}," 保存 ")])]),e("div",_o,[ho,e("div",po,[E.value&&E.value.learn_type!=="advance"?(i(),v("div",go,[_(no,{class:"w-12 h-12 mb-4"}),Co,wo])):(i(),v("div",bo,[e("div",xo,[X(e("textarea",{class:"py-[8px] px-3 h-[86px] w-full rounded-lg overflow-hidden resize-none border-[#E5E5E5] text-sm docpoint-textarea","onUpdate:modelValue":m[2]||(m[2]=h=>b.value=h)},null,512),[[q,b.value]]),e("div",yo,[e("button",{type:"button",onClick:m[3]||(m[3]=h=>b.value=""),class:"new-modal-bottom-cancel",style:{width:"40px",height:"22px","border-radius":"6px"}}," 清除 "),e("button",{type:"button",onClick:m[4]||(m[4]=(...h)=>H(n)&&H(n)(...h)),class:"new-modal-bottom-confirm",style:{width:"40px",height:"22px","border-radius":"6px"}}," 保存 ")])]),e("div",ko,[(i(!0),v(N,null,re(V.value,h=>(i(),v("div",{class:"mt-[9px] p-3 bg-[#F5F5F5] flex justify-between w-full rounded-lg overflow-hidden relative min-h-[90px]",key:h.id},[e("div",Io,A(h.content),1),e("div",Do,[e("button",{class:"bg-transparent border-0",onClick:z=>$(h.id,h.content)},[_(Ee,{size:"20"})],8,Ao),e("button",{class:"bg-transparent border-0",onClick:z=>F(h.id)},[_(le,{size:"20",color:"#A2A9B0"})],8,Fo)]),h.id==I.value?(i(),v("div",Lo,[X(e("textarea",{class:"py-[8px] px-3 w-full rounded-lg overflow-y resize-none border-[#E5E5E5] text-sm h-[75%] docpoint-textarea","onUpdate:modelValue":m[5]||(m[5]=z=>u.value=z)},null,512),[[q,u.value]]),e("div",Bo,[e("button",{type:"button",onClick:m[6]||(m[6]=z=>{u.value="",I.value=""}),class:"new-modal-bottom-cancel",style:{width:"40px",height:"22px","border-radius":"6px"}}," 取消 "),e("button",{type:"button",onClick:m[7]||(m[7]=(...z)=>H(j)&&H(j)(...z)),class:"new-modal-bottom-confirm",style:{width:"40px",height:"22px","border-radius":"6px"}}," 保存 ")])])):y("",!0)]))),128))])]))])])])])]),_(B,{show:L.value,title:S.value,message:R.value,onClose:M,onConfirm:H(x)},null,8,["show","title","message","onConfirm"])],64)}}}),So=ue(Eo,[["__scopeId","data-v-ecf2a7b3"]]),Mo=!1,zo={name:"DocFragmentLIstView",components:{BaseNavComponent:ze,Delete:ke,ArrowLeftBold:ye,CirclePlus:xe,BreadCrumbComponent:Me,BackArrowIcon:We,SearchIcon:Qe,UploadThinIcon:qe,AddIcon:eo,DeleteIcon:Je,RefreshIcon:Xe,v2ConfirmsModal:Se,DocFragmentDetail:So,Loading:be,Document:we},setup(){const p=l("kbase"),c=l("doc"),r=Ie(),o=ne(),f=l(null),d=l(null),V=l(!0),w=l(""),E=l(null),k=l(!1),P=l("永久删除文档切片"),L=l("该文档切片数据将被永久删除，不可恢复及撤销。确定要删除吗？"),S=l([]),R=l(1),T=l(24),b=l(0),F=l(null),M=l(!1),x=l(null),n=l(!1),I=l("添加"),u=l(!1),$=l([]),j=l(""),a=l(!1),m=l(null),B=async(t,g)=>{try{u.value=!0;const D=await Re(t,g,j.value);S.value=D.data,b.value=D.data.length}catch{s.error("获取切片列表失败")}finally{u.value=!1}},h=t=>{k.value=!0,x.value=t},z=()=>{k.value=!1,x.value=null},me=async()=>{k.value=!1,(await $e(d.value,f.value,x.value)).data.error=="0"?(s.success("删除成功"),B(d.value,f.value),x.value=null):s.error("当前切片内容不存在，删除失败")};De(F,t=>{t!==""?M.value=!0:M.value=!1});const ve=async()=>{const t=await je(d.value);return t.data.error=="0"?`知识库（${t.data.title}）`:"知识库"};ce(async()=>{if(r.params.docId){f.value=r.params.docId,d.value=r.params.id,B(d.value,f.value);const t=await Ve(d.value,f.value);try{t.data.error=="0"?(w.value=t.data.title,E.value=t.data.type):t.data.error=="1"||t.data.error=="2"||t.data.error=="404"?o.push({name:"KBaseList"}):t.data.error=="3"?o.push({name:"DocList",params:{kid:d.value}}):s.error(t.data.message)}catch{}let g="";g=await ve(),$.value=[{path:"/admin/knowledge",name:g},{path:`/admin/knowledge/detail/${d.value}/docs`,name:"文档"},{path:"",name:`切片（${w.value}）`}]}});const fe=t=>{t||(V.value=!1)},ae=()=>{n.value=!1,x.value=null,M.value=!0,F.value="",I.value="添加"},_e=t=>{t?(console.log("Opening detail modal with:",{kbaseId:d.value,documentId:f.value,fragmentId:t}),x.value=t,a.value=!0,m.value=t):n.value=!0},he=async()=>{n.value=!1;let t;if(F.value){if(x.value)try{t=await te(d.value,f.value,x.value,F.value)}catch{s.error("保存失败")}else try{t=await Oe(d.value,f.value,F.value)}catch{s.error("保存失败")}ae(),u.value.close();try{t.data.error=="0"?(s.success("保存成功"),B(d.value,f.value)):t.data.error?s.error("保存失败，"+t.data.message):s.error("保存失败")}catch{s.error("保存失败")}}else s("请填写切片内容")},pe=()=>{u.value=O.service({lock:!0,text:"提交中",background:"rgba(0, 0, 0, 0.5)"})},ge=()=>{o.push(`/admin/knowledge/detail/${d.value}/docs`)},Ce=Te(()=>{B(d.value,f.value)},300);return{selectedBar:p,selectedKBaseNav:c,handleIsEdit:fe,canPerformAction:V,docTitle:w,docType:E,showDelModal:k,modalDelTitle:P,modalDelMessage:L,delFragment:h,handleClose:z,handleConfirm:me,pageSize:T,totalItems:b,items:S,currentPage:R,getFileIcon:He,selectedKid:d,selectedDCid:f,highlight:M,fragmentContent:F,closeModal:ae,showEditModal:n,modalTitle:I,showEditModalAction:_e,submitContent:he,openFullScreen:pe,breadcrumbs:$,handleBack:ge,searchKeyword:j,handleSearch:Ce,handleStatusChange:async(t,g)=>{try{(await Ze(d.value,f.value,t,g)).data.error==="0"?s.success("状态修改成功"):(s.error("状态修改失败"),B(d.value,f.value))}catch{s.error("状态修改失败"),B(d.value,f.value)}},handleRelearn:async(t,g)=>{var D;try{u.value=O.service({lock:!0,text:"重新学习中",background:"rgba(0, 0, 0, 0.5)"});const oe=await te(d.value,f.value,t,g);u.value.close(),oe.data.error==="0"?(s.success("已开始重新学习"),B(d.value,f.value)):s.error("重新学习失败："+oe.data.message)}catch{(D=u.value)==null||D.close(),s.error("重新学习失败")}},loading:u,showDetailModal:a,selectedFragmentId:m,handleDetailClose:()=>{a.value=!1,m.value=null},handleDetailSave:t=>{const g=S.value.findIndex(D=>D.id===t.id);g!==-1&&(S.value[g].content=t.content)},SHOW_ENABLE_DISABLE_FEATURES:Mo}}},K=p=>(de("data-v-61d58753"),p=p(),ie(),p),Vo={class:"h-full overflow-hidden"},To={class:"flex items-center mb-6 pb-[12px] border-0 border-solid border-b border-[#E5E5E5]"},Ho=["src"],No={class:"text-[18px] font-semibold text-[#121619] leading-[26px] font-[PingFang SC]"},Po={class:"flex items-center justify-between mb-[18px]"},Uo={class:"rounded-[6px] pt-[4px] pb-[4px] pl-3 pr-4 text-[16px] flex items-center leading-[22px] font-medium relative"},Ko={class:"flex items-center gap-3"},Ro={class:"relative"},jo={class:"h-[calc(100vh-340px)]"},Zo={class:"fragment-list overflow-y"},Oo={key:0,class:"col-span-full flex justify-center py-20"},$o=K(()=>e("span",{class:"ml-2"},"加载中...",-1)),Wo={key:1,class:"col-span-full flex flex-col items-center justify-center py-20"},Qo={class:"text-gray-400 text-6xl mb-4"},Xo=K(()=>e("p",{class:"text-gray-500 text-lg mb-6"},"暂无切片数据",-1)),qo=["onClick"],Jo={class:"fragment-item-top"},Yo={class:"flex items-center gap-3"},Go={class:"fragment-item-top-left"},et={class:"text-[#666666]"},ot={key:0},tt={class:"fragment-item-center mb-2 text-[#697077]"},st={class:"fragment-item-bottom"},at={class:"flex justify-between items-center"},lt={class:"text-[#343A3F] text-xs"},nt={class:"text-[#A2A9B0]"},ct=K(()=>e("img",{src:Ne,alt:""},null,-1)),rt=K(()=>e("img",{src:Pe,alt:""},null,-1)),dt=K(()=>e("img",{src:Ue,alt:""},null,-1)),it=K(()=>e("img",{src:Ke,alt:""},null,-1)),ut={key:0,class:"fragment-item-modal"},mt={key:0,class:"new-modal-overlay"},vt={class:"new-modal-container"},ft={class:"new-modal-top"},_t={class:"font18 font-zhongcu"},ht={class:"new-modal-top-right font16 overflow-one"},pt=["src"],gt={class:"file-name"},Ct={class:"new-modal-center"},wt={class:"new-modal-bottom"},bt=["disabled"],xt={class:"mt-[24px]"};function yt(p,c,r,o,f,d){const V=C("BackArrowIcon"),w=C("SearchIcon"),E=C("el-input"),k=C("AddIcon"),P=C("Loading"),L=C("el-icon"),S=C("Document"),R=C("el-switch"),T=C("RefreshIcon"),b=C("DeleteIcon"),F=C("v2ConfirmsModal"),M=C("BreadCrumbComponent"),x=C("DocFragmentDetail");return i(),v("div",Vo,[e("div",To,[e("div",{class:"flex items-center cursor-pointer",onClick:c[0]||(c[0]=(...n)=>o.handleBack&&o.handleBack(...n))},[_(V,{size:24,color:"#121619"})]),o.docType?(i(),v("img",{key:0,src:o.getFileIcon(o.docType),alt:"file icon",class:"file-icon ml-2"},null,8,Ho)):y("",!0),e("span",No,A(o.docTitle)+"."+A(o.docType),1)]),e("div",Po,[e("div",Uo,A(o.totalItems)+"组 ",1),e("div",Ko,[_(E,{modelValue:o.searchKeyword,"onUpdate:modelValue":[c[1]||(c[1]=n=>o.searchKeyword=n),o.handleSearch],placeholder:"搜索",class:"w-[280px] !h-[32px] rounded-[6px]"},{prefix:U(()=>[_(w,{size:"16",color:"#999"})]),_:1},8,["modelValue","onUpdate:modelValue"]),e("div",Ro,[o.canPerformAction?(i(),v("button",{key:0,onClick:c[2]||(c[2]=n=>o.showEditModalAction()),class:"h-[32px] min-w-[110px] bg-[#129BFE] rounded-[8px] px-[14px] text-white text-sm border-0 flex items-center gap-[6px] font-medium hover:bg-[#118AE3] transition-colors duration-200"},[_(k,{color:"#fff"}),Z(" 插入切片 ")])):y("",!0)])])]),e("div",jo,[e("div",Zo,[o.loading?(i(),v("div",Oo,[_(L,{class:"is-loading"},{default:U(()=>[_(P)]),_:1}),$o])):y("",!0),!o.loading&&o.items.length===0?(i(),v("div",Wo,[e("div",Qo,[_(L,null,{default:U(()=>[_(S)]),_:1})]),Xo])):y("",!0),!o.loading&&o.items.length>0?(i(!0),v(N,{key:2},re(o.items,(n,I)=>(i(),v("div",{class:"fragment-item font12",onClick:u=>o.canPerformAction&&o.showEditModalAction(n.id),key:n.id},[e("div",Jo,[e("div",Yo,[e("div",Go," #"+A(I+1),1),e("div",et," 集合ID："+A(n.vectorid),1)]),o.SHOW_ENABLE_DISABLE_FEATURES?(i(),v("div",ot,[_(R,{modelValue:n.is_enabled,"onUpdate:modelValue":u=>n.is_enabled=u,onClick:c[3]||(c[3]=Q(()=>{},["stop"])),onChange:u=>o.handleStatusChange(n.id,u)},null,8,["modelValue","onUpdate:modelValue","onChange"])])):y("",!0)]),e("div",tt,A(n.content),1),e("div",st,[e("div",at,[e("div",lt,A(n.content_total_chars)+"字符 ",1),e("div",nt,[n.status=="wait"?(i(),v(N,{key:0},[ct,Z("待学习 ")],64)):n.status=="doing"?(i(),v(N,{key:1},[rt,Z("学习中 ")],64)):n.status=="success"?(i(),v(N,{key:2},[dt,Z("学习成功 ")],64)):n.status=="fail"?(i(),v(N,{key:3},[it,Z("学习失败 ")],64)):y("",!0)])])]),o.canPerformAction?(i(),v("div",ut,[_(T,{size:"18",title:"重新学习",color:"#A2A9B0",onClick:Q(u=>o.handleRelearn(n.id,n.content),["stop"]),class:"mr-2 hover:text-[#129BFE]"},null,8,["onClick"]),_(b,{size:"18",title:"删除",color:"#A2A9B0",onClick:Q(u=>o.delFragment(n.id),["stop"]),class:"hover:text-[#129BFE]"},null,8,["onClick"])])):y("",!0)],8,qo))),128)):y("",!0)])]),_(F,{show:o.showDelModal,title:o.modalDelTitle,message:o.modalDelMessage,onClose:o.handleClose,onConfirm:o.handleConfirm},null,8,["show","title","message","onClose","onConfirm"]),o.showEditModal?(i(),v("div",mt,[e("div",vt,[e("div",ft,[e("div",_t,A(o.modalTitle)+"数据",1),e("div",ht,[o.docType?(i(),v("img",{key:0,src:o.getFileIcon(o.docType),alt:"file icon",class:"file-icon"},null,8,pt)):y("",!0),e("span",gt,A(o.docTitle)+"."+A(o.docType),1)])]),e("div",Ct,[X(e("textarea",{class:"modal-edit-content font12 overflow-y","onUpdate:modelValue":c[4]||(c[4]=n=>o.fragmentContent=n)},null,512),[[q,o.fragmentContent]])]),e("div",wt,[e("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:c[5]||(c[5]=(...n)=>o.closeModal&&o.closeModal(...n))}," 取消 "),e("button",{type:"button",class:G(["new-modal-bottom-confirm font14 font-zhongcu modal-footer-confirm common-confirm-btn",o.highlight?"modal-footer-confirm-active":""]),disabled:!o.highlight,onClick:c[6]||(c[6]=n=>{o.submitContent(),o.openFullScreen()})}," 保存 ",10,bt)])])])):y("",!0),e("div",xt,[_(M,{breadcrumbs:o.breadcrumbs},null,8,["breadcrumbs"])]),o.showDetailModal?(i(),Y(x,{key:1,kbaseId:o.selectedKid,documentId:o.selectedDCid,docfragmentId:o.selectedFragmentId,onClose:o.handleDetailClose,onOnSave:o.handleDetailSave},null,8,["kbaseId","documentId","docfragmentId","onClose","onOnSave"])):y("",!0)])}const zt=ue(zo,[["render",yt],["__scopeId","data-v-61d58753"]]);export{zt as default};
