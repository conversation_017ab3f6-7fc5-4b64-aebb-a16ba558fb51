'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { fetchGetSysConfigs } from '@/service/common';
import type { GetSysConfigsResponse } from '@/models/common';

type SysConfigContextType = {
  sysConfigs: GetSysConfigsResponse | null;
  loading: boolean;
  error: Error | null;
  refreshConfigs: () => Promise<void>;
};

const SysConfigContext = createContext<SysConfigContextType | undefined>(undefined);

export function SysConfigProvider({ children }: { children: React.ReactNode }) {
  const [sysConfigs, setSysConfigs] = useState<GetSysConfigsResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchConfigs = async () => {
    try {
      setLoading(true);
      const data = await fetchGetSysConfigs();
      setSysConfigs(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch system configs'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConfigs();
  }, []);

  return (
    <SysConfigContext.Provider
      value={{
        sysConfigs,
        loading,
        error,
        refreshConfigs: fetchConfigs,
      }}
    >
      {children}
    </SysConfigContext.Provider>
  );
}

export function useSysConfig() {
  const context = useContext(SysConfigContext);
  if (context === undefined)
    throw new Error('useSysConfig must be used within a SysConfigProvider');

  return context;
}
