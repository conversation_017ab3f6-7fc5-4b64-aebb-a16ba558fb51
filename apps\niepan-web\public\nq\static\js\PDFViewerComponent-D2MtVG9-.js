import{d as ue,A,o as a,n as ye,m as h,b as l,r as i,s as E,z as re,P as we,N as Z,e as I,c as o,a as U,f as r,t as y,p as f,aa as P,_ as M,J as R,b0 as ce,R as b,x as ke,y as Ce}from"./pnpm-pnpm-B4aX-tnA.js";import{_ as Le,aU as Pe,aV as be,a as Ve}from"./index-oCQpNzhc.js";const Te=l("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 14 14"},[l("path",{d:"M9.62533 2.33341H10.8127L8.92118 4.22493L9.74614 5.04989L11.667 3.12903V4.37508H12.8337V1.75008C12.8337 1.42792 12.5725 1.16675 12.2503 1.16675H9.62533V2.33341Z"}),l("path",{d:"M2.33366 3.14836L4.2566 5.05198L5.07738 4.22286L3.16875 2.33341H4.37533V1.16675H1.75033C1.42816 1.16675 1.16699 1.42792 1.16699 1.75008V4.37508H2.33366V3.14836Z"}),l("path",{d:"M5.07738 9.74796L3.13911 11.6667H4.37533V12.8334H1.75033C1.42816 12.8334 1.16699 12.5722 1.16699 12.2501V9.62508H2.33366V10.8225L4.2566 8.91884L5.07738 9.74796Z"}),l("path",{d:"M10.8714 11.6667H9.62533V12.8334H12.2503C12.5725 12.8334 12.8337 12.5722 12.8337 12.2501V9.62508H11.667V10.8124L9.77548 8.92091L8.95052 9.74587L10.8714 11.6667Z"})],-1),He={name:"FullscreenIcon"},Fe=ue({...He,props:{color:{default:"#343A3F"},size:{default:14},className:{default:""}},setup(w){return(V,N)=>(a(),A(Le,{color:V.color,size:V.size,class:ye(V.className)},{default:h(()=>[Te]),_:1},8,["color","size","class"]))}}),Ee=w=>(ke("data-v-5fac11a7"),w=w(),Ce(),w),Ue={class:"relative"},De={key:0,class:"absolute inset-0 bg-white/90 z-50 flex items-center justify-center"},ze=Ee(()=>l("div",{class:"flex flex-col items-center gap-2"},[l("div",{class:"w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"}),l("span",{class:"text-gray-600"},"加载中...")],-1)),$e=[ze],Se={key:1,class:"text-base font-medium mt-4 mb-2 text-black"},je={class:"w-full h-full flex flex-col bg-gray-100 rounded-lg max-h-[calc(100vh-130px)]"},Ie={class:"flex justify-between items-center px-4 py-3 header-gradient text-gray-700 rounded-t-lg"},Me={key:0,class:"flex items-center"},Re={key:1,class:"text-red-500"},Ae={key:2,class:"flex justify-between items-center w-full"},Ne={class:"flex items-center gap-4"},qe={key:0,class:"flex items-center gap-2"},Be={key:1},Ze={class:"flex items-center"},Oe={key:0,class:"flex items-center justify-center h-full"},We={class:"text-red-500 text-lg"},Ke={key:1,class:"flex items-center justify-center h-full"},Ge={class:"flex items-center text-gray-600"},Je={key:2,class:"max-w-4xl mx-auto max-h-[calc(100vh-200px)]"},Ye={key:3,class:"max-w-4xl mx-auto bg-white rounded-lg p-6 shadow-lg overflow-auto markdown-content"},Qe={class:"whitespace-pre-wrap text-gray-800 font-mono text-sm leading-relaxed"},Xe={class:"h-full flex flex-col bg-gray-100"},et={class:"flex justify-between items-center px-4 py-3 header-gradient text-gray-700"},tt={key:0,class:"flex items-center"},at={key:1,class:"text-red-500"},st={key:2,class:"flex justify-between items-center w-full"},lt={class:"flex items-center gap-4"},ot={key:0,class:"flex items-center gap-2"},nt={key:1},it={class:"flex items-center"},rt={key:0,class:"flex items-center justify-center h-full"},ct={class:"text-red-500 text-lg"},dt={key:1,class:"flex items-center justify-center h-full"},ut={class:"flex items-center text-gray-600"},ft={key:2,class:"pdf-content-wrapper mx-auto max-h-[calc(100vh-156px)]"},vt={key:3,class:"max-w-4xl mx-auto bg-white rounded-lg p-6 shadow-lg overflow-auto markdown-content max-h-[calc(100vh-156px)]"},ht={class:"whitespace-pre-wrap text-gray-800 font-mono text-sm leading-relaxed"},de="",pt=ue({__name:"PDFViewerComponent",props:{data:{type:Object,required:!0}},setup(w,{expose:V}){const N=["pdf","ppt","pptx","doc","docx"];function fe(t){return be(t)}const O=()=>{if(!s.data.title)return"";const{title:t,fileType:e}=s.data;return!e||t.toLowerCase().endsWith(`.${e.toLowerCase()}`)?t:`${t}.${e}`},s=w,v=i(!0),x=i(!0),p=i(!0),n=i(s.data.initialPage||1),_=i(0),g=i(s.data.defaultShowAllPages||!1),W=i(null),C=i(""),m=i(!1),K=i(!0),T=i(""),H=i(!1),G=i(null),ve=i(`pdf-${Date.now()}`),J=i(`pdf-fullscreen-${Date.now()}`),Y=i(window.innerHeight),he=E(()=>Math.floor(Y.value*.78)),pe=["ppt","pptx"],ge=E(()=>{const t=s.data.fileType||s.data.fileUrl.toLowerCase().split(".").pop();return pe.includes(t)}),Q=E(()=>{const t=(s.data.fileType||s.data.fileUrl.toLowerCase().split(".").pop()||"").toLowerCase();return N.includes(t)}),X=E(()=>(s.data.fileType||s.data.fileUrl.toLowerCase().split(".").pop()||"").toLowerCase()==="md"),me=E(()=>(ge.value,0)),ee=()=>{Y.value=window.innerHeight};re(()=>{window.addEventListener("resize",ee)}),we(()=>{window.removeEventListener("resize",ee)}),Z(H,async t=>{t&&(await b(),J.value=`pdf-fullscreen-${Date.now()}`,await b(),setTimeout(D,100))}),Z(n,async(t,e)=>{e!==0&&(await b(),D())});const xe=async()=>{const t=(s.data.fileType||s.data.fileUrl.toLowerCase().split(".").pop()||"").toLowerCase();if(!N.includes(t)){console.error("[PDF预览] 不支持的文件格式:",t),m.value=!0,p.value=!1,v.value=!1,x.value=!1;return}if(t==="md")try{v.value=!0,x.value=!0,m.value=!1,console.log("[Markdown预览] 开始加载Markdown文件:",s.data.fileUrl);try{const e=await fetch(s.data.fileUrl);if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);T.value=await e.text(),p.value=!1,v.value=!1,x.value=!1,_.value=1,n.value=1}catch(e){console.error("[Markdown预览] 加载错误:",e),m.value=!0,p.value=!1,x.value=!1,v.value=!1}return}catch(e){console.error("[Markdown预览] 发生错误:",e),m.value=!0,p.value=!1,x.value=!1,v.value=!1;return}if(t!=="pdf")try{v.value=!0,x.value=!0,m.value=!1,console.log("[PDF转换] 开始转换文件:",s.data.fileUrl);const e=fe(s.data.fileUrl),c=encodeURIComponent(e);try{C.value=Pe({url:c}),p.value=!1}catch(d){throw console.error("[PDF转换] 发生错误:",d),m.value=!0,p.value=!1,x.value=!1,d}}catch(e){console.error("[PDF转换] 发生错误:",e),m.value=!0,p.value=!1,x.value=!1}finally{v.value=!1}else console.log("[PDF加载] 直接加载PDF文件"),C.value=s.data.fileUrl,p.value=!1,v.value=!1,x.value=!1};Z(g,async t=>{t||(n.value=1),await b(),setTimeout(D,500)});const te=t=>{n.value=t},D=async()=>{if(!s.data.highlights)return;await b();const t=H.value?G.value:W.value;if(!t)return;const e=g.value?Array.from(t.querySelectorAll(".textLayer")||[]):[t.querySelector(".textLayer")].filter(Boolean);e.forEach(c=>{c.querySelectorAll(".highlight-text").forEach(L=>{L.classList.remove("highlight-text")})}),e.forEach((c,d)=>{var oe;const L=g.value?d+1:n.value,z=(oe=s.data.highlights)==null?void 0:oe.find(q=>q.page===L);if(!z||!c)return;const u=Array.from(c.querySelectorAll("span"));z.texts.forEach(q=>{const $=q.toLowerCase().trim();let S=[],F="";u.forEach((j,ne)=>{const B=j.textContent||"";if(B.toLowerCase().includes($)){j.classList.add("highlight-text");return}S.push(j),F+=B;const ie=F.replace(/\s+/g," ").toLowerCase().trim();if(ie.includes($))S.forEach(k=>{k.classList.add("highlight-text")}),S=[],F="";else if(ie.length>$.length*3){const k=S.shift();k&&(F=F.slice((k.textContent||"").length))}if(ne<u.length-1){const k=u[ne+1],_e=k.textContent||"";(B+_e).replace(/\s+/g,"").toLowerCase().includes($)&&(j.classList.add("highlight-text"),k.classList.add("highlight-text"))}})})})},ae=async({numPages:t})=>{var e;if(_.value=t,m.value=!1,x.value=!1,K.value){if((e=s.data.highlights)!=null&&e.length){const c=s.data.highlights[0].page;c<=t&&(n.value=c)}else s.data.initialPage&&s.data.initialPage<=t&&(n.value=s.data.initialPage);K.value=!1}},se=async()=>{v.value=!1,await b(),setTimeout(D,100)},le=({callback:t,isWrongPassword:e})=>{const d=prompt(e?"密码错误，请重新输入":"请输入PDF密码");d&&t(d)};return V({currentPage:n,totalPages:_,showAllPages:g,goToPage:t=>{t>0&&t<=_.value&&(n.value=t)}}),re(async()=>{var t;if(await xe(),(t=s.data.highlights)!=null&&t.length){const e=s.data.highlights[0].page;e>0&&(n.value=e)}}),(t,e)=>{const c=I("el-icon"),d=I("el-button"),L=I("el-checkbox"),z=I("el-dialog");return a(),o("div",Ue,[x.value?(a(),o("div",De,$e)):U("",!0),w.data.title?(a(),o("div",Se,y(O()),1)):U("",!0),l("div",je,[l("div",Ie,[v.value?(a(),o("div",Me,[r(c,{class:"animate-spin mr-2"},{default:h(()=>[r(P(M))]),_:1}),f(" 加载中... ")])):m.value?(a(),o("div",Re,"文件加载失败")):(a(),o("div",Ae,[l("div",Ne,[g.value?(a(),o("span",Be,"共 "+y(_.value)+" 页",1)):(a(),o("div",qe,[r(d,{disabled:n.value<=1,onClick:e[0]||(e[0]=u=>n.value--),size:"small",class:"!text-gray-600"},{default:h(()=>[f(" ❮ ")]),_:1},8,["disabled"]),l("span",null,y(n.value)+" / "+y(_.value),1),r(d,{disabled:n.value>=_.value,onClick:e[1]||(e[1]=u=>n.value++),size:"small",class:"!text-gray-600"},{default:h(()=>[f(" ❯ ")]),_:1},8,["disabled"])]))]),l("div",Ze,[r(L,{modelValue:g.value,"onUpdate:modelValue":e[2]||(e[2]=u=>g.value=u),class:"!text-gray-200"},{default:h(()=>[f(" 显示全部页面 ")]),_:1},8,["modelValue"]),s.data.hideFullscreenButton!==!0?(a(),A(d,{key:0,onClick:e[3]||(e[3]=u=>H.value=!0),size:"small",class:"ml-2 !text-gray-600 !px-1.5"},{default:h(()=>[r(Fe)]),_:1})):U("",!0)])]))]),l("div",{class:"flex-1 p-4 overflow-auto",ref_key:"pdfContainer",ref:W},[m.value?(a(),o("div",Oe,[l("div",We,[Q.value?(a(),o(R,{key:1},[f(" 文件加载失败，请检查文件格式或网络连接 ")],64)):(a(),o(R,{key:0},[f(" 不支持预览该文件格式，目前仅支持 PDF、PPT、Word 文档格式 ")],64))])])):v.value||p.value?(a(),o("div",Ke,[l("div",Ge,[r(c,{class:"animate-spin mr-2"},{default:h(()=>[r(P(M))]),_:1}),f(" 文档加载中... ")])])):!p.value&&C.value?(a(),o("div",Je,[(a(),A(P(ce),{key:ve.value,source:C.value,height:me.value,page:g.value?null:n.value,"image-resources-path":de,onPasswordRequested:le,onLoaded:ae,onRendered:se,onInternalLinkClicked:te,class:"pdf-container"},null,8,["source","height","page"]))])):X.value&&T.value?(a(),o("div",Ye,[l("pre",Qe,y(T.value),1)])):U("",!0)],512)]),r(z,{modelValue:H.value,"onUpdate:modelValue":e[7]||(e[7]=u=>H.value=u),title:O()||"文档预览",fullscreen:"","show-close":!0,"close-on-click-modal":!1,"close-on-press-escape":!0,class:"pdf-fullscreen-dialog"},{default:h(()=>[l("div",Xe,[l("div",et,[v.value?(a(),o("div",tt,[r(c,{class:"animate-spin mr-2"},{default:h(()=>[r(P(M))]),_:1}),f(" 加载中... ")])):m.value?(a(),o("div",at,"文件加载失败")):(a(),o("div",st,[l("div",lt,[g.value?(a(),o("span",nt,"共 "+y(_.value)+" 页",1)):(a(),o("div",ot,[r(d,{disabled:n.value<=1,onClick:e[4]||(e[4]=u=>n.value--),size:"small",class:"!text-gray-600"},{default:h(()=>[f(" ❮ ")]),_:1},8,["disabled"]),l("span",null,y(n.value)+" / "+y(_.value),1),r(d,{disabled:n.value>=_.value,onClick:e[5]||(e[5]=u=>n.value++),size:"small",class:"!text-gray-600"},{default:h(()=>[f(" ❯ ")]),_:1},8,["disabled"])]))]),l("div",it,[r(L,{modelValue:g.value,"onUpdate:modelValue":e[6]||(e[6]=u=>g.value=u),class:"!text-gray-200"},{default:h(()=>[f(" 显示全部页面 ")]),_:1},8,["modelValue"])])]))]),l("div",{class:"flex-1 p-4 overflow-auto flex justify-center",ref_key:"fullscreenPdfContainer",ref:G},[m.value?(a(),o("div",rt,[l("div",ct,[Q.value?(a(),o(R,{key:1},[f(" 文件加载失败，请检查文件格式或网络连接 ")],64)):(a(),o(R,{key:0},[f(" 不支持预览该文件格式，目前仅支持 PDF、PPT、Word 文档格式 ")],64))])])):v.value||p.value?(a(),o("div",dt,[l("div",ut,[r(c,{class:"animate-spin mr-2"},{default:h(()=>[r(P(M))]),_:1}),f(" 文档加载中... ")])])):!p.value&&C.value?(a(),o("div",ft,[(a(),A(P(ce),{key:J.value,source:C.value,page:g.value?null:n.value,"image-resources-path":de,height:he.value,onPasswordRequested:le,onLoaded:ae,onRendered:se,onInternalLinkClicked:te,class:"fullscreen-pdf"},null,8,["source","page","height"]))])):X.value&&T.value?(a(),o("div",vt,[l("pre",ht,y(T.value),1)])):U("",!0)],512)])]),_:1},8,["modelValue","title"])])}}}),_t=Ve(pt,[["__scopeId","data-v-5fac11a7"]]);export{_t as default};
