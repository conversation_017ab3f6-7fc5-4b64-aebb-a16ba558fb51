// utils.ts
export interface AmountFormatResult {
  value: number;
  unit: string;
}

export function formatAmount(
  amount: number,
  isShowYuanUnit = false
): AmountFormatResult {
  let amountParse = parseFloat(`${amount}`);

  const units = ["", "万", "亿", "万亿"];
  let index = 0;
  let unit = "";

  // 将金额转换为以亿为单位的数值
  while (amountParse >= 10000 && index < units.length - 1) {
    amountParse /= 10000;
    index++;
  }

  // 保留一位小数
  amountParse = parseFloat(amountParse.toFixed(1));

  // 如果整数部分为0，则不显示整数部分
  if (amountParse < 1) {
    return {
      value: 0,
      unit: "",
    };
  }

  // 如果小数部分为0，则不显示小数部分
  if (amountParse % 1 === 0) {
    amountParse = parseInt(amountParse.toString());
  }

  // 构建单位字符串
  unit = `${units[index]}${isShowYuanUnit ? "元" : ""}`;

  return {
    value: amountParse,
    unit: unit,
  };
}

// 获取当前时间，年月日时分
// utils.ts
export function getCurrentTime(): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1; // 月份从0开始，所以需要+1
  const day = now.getDate();
  const hours = now.getHours();
  const minutes = now.getMinutes();
  // const seconds = now.getSeconds();

  // 格式化时间，确保月、日、时、分、秒都是两位数
  const formattedTime =
    [
      year,
      month.toString().padStart(2, "0"),
      day.toString().padStart(2, "0"),
    ].join("-") +
    " " +
    [
      hours.toString().padStart(2, "0"),
      minutes.toString().padStart(2, "0"),
      //   seconds.toString().padStart(2, '0')
    ].join(":");

  return formattedTime;
}
