<!-- 列表组件 -->
<template>
  <div
    class="w-[360px] rounded-[22px] bg-white border border-solid border-[#F1F1F1] p-[20px] text-xs"
  >
    <div class="flex justify-between items-center">
      <div class="flex items-start">
        <img
          v-if="data?.titleImgUrl"
          :src="data?.titleImgUrl"
          alt=""
          class="w-[20px] h-[20px] object-contain object-center mt-[2px]"
        />
        <img
          v-else
          src="@/assets/images/home/<USER>/<EMAIL>"
          alt=""
          class="w-[20px] h-[20px] object-contain object-center mt-[2px]"
        />
        <span class="font-zhongcu ml-1.5 text-15 leading-[24px] break-words">{{
          data.title
        }}</span>
      </div>
    </div>
    <div class="mt-[12px]">
      <div
        v-for="(item, index) in data?.items"
        :key="index"
        class="flex justify-between item-center py-[6px] px-[10px] rounded-[10px] bg-gradient-to-r from-[#ebfbff] to-white mt-[10px]"
      >
        <div class="flex items-center">
          <img
            v-if="item?.imgUrl"
            :src="item?.imgUrl"
            alt=""
            class="w-[16px] h-[16px] object-contain object-center"
          />
          <img
            v-else
            src="@/assets/images/home/<USER>/<EMAIL>"
            alt=""
            class="w-[16px] h-[16px] object-contain object-center"
          />
          <span class="overflow-one w-[10em] ml-[4px]">{{ item.title }}</span>
        </div>
        <span class="overflow-one text-xl font-semibold"
          >{{ item.total }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ModelData {
  title?: string;
  titleImgUrl?: string;
  items?: Array<{
    title: string;
    total: string;
    imgUrl?: string;
  }>;
}

const props = defineProps<{
  data: ModelData; //必填项
}>();
</script>
