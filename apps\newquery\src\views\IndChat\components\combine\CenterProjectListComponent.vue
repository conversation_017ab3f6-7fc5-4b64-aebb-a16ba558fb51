<!-- 项目列表组件 -->
<template>
    <div class="w-[272px] rounded-[22px] bg-white border border-solid border-[#F1F1F1] p-[20px] text-xs">
        <div class="flex justify-between items-center">
            <div>
                <span class="text-3xl font-zhongcu">{{ formattedData.value }}</span>
                <span class="font-zhongcu ml-1">{{ formattedData.unit }}个项目</span>
            </div>
            <div class="w-[42px] h-[42px] bg-theme rounded-full text-center leading-[42px]">
                <img 
                    
                    src="@/assets/images/chat/xiangmu.png" 
                    alt="" 
                    class="w-[24px] h-[24px] object-contain object-center"
                >
            </div>
        </div>
        <div class="mt-[10px]">
            <div 
                v-for="(item, index) in data?.items"
                :key="index"
                class="flex justify-between item-center py-[8px] border-0 border-b border-[#ECECF2] border-solid"
            >
                <span class="overflow-one w-[70%]">{{ item.name }}</span>
                <span class="bg-[#FF9400] px-[5px] rounded-[5px] font10 text-white overflow-one">{{ progressToPercentage(item.execution_progress) }}</span>
            </div>
        </div> 
    </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { formatAmount } from "../../utils"

interface Project {
    name:string,  //项目名称
    execution_progress:string    //项目阶段
}

interface ModelData {
    total_count: number;
    items?: Project[];
}

const props = defineProps<{
    data: ModelData;  //必填项
}>();

// 使用 computed 来确保 formatAmount 函数的返回值是响应式的
const formattedData = computed(() => {
    if(props.data.total_count == 0){
        return { value: 0, unit: '' };
    }
    const { value, unit } = formatAmount(props.data.total_count, false); 
    return { value, unit };
});

// 将 progress 转换为百分比
const progressToPercentage = (progress: string) => {
    const progresss = {
        'contract_review': '合同审查',
        'contract_sent': '合同发出',
        'contract_confirmation': '合同确认',
        'feasibility_study_construction': '可研/建设',
        'requirements_analysis': '需求分析',
        'product_design': '产品设计',
        'software_development': '软件研发',
        'software_testing': '软件测试',
        'software_delivery': '软件交付',
        'after_sales_maintenance': '售后维护',
    };
    return progresss[progress] || '合同审查';
};
</script>