import { createWrappedApiClient } from '@/services/api-wrapper';
import { useConfig } from '@/hooks/useConfig';

const { serverConfig } = useConfig();

console.log('apiAvatar.js - serverConfig:', serverConfig);
console.log(
    'apiAvatar.js - baseURL will be:',
    `${serverConfig.VITE_API_BASE_URL}/avatar`
);

const apiClientInstance = createWrappedApiClient({
    baseURL: `${serverConfig.VITE_API_BASE_URL}/avatar`,
    headers: {
        'Content-Type': 'multipart/form-data',
    },
    tokenKey: 'console_token',
});

// 获取所有部门列表，树形结构用
export const getAllDepartments = async () => {
    return apiClientInstance.get('/ws/departments/all', {});
};

// 获取部门列表，部门页面用到
export const getDepartmentListAction = async (page, count) => {
    return apiClientInstance.get('/ws/departments/page', {
        params: { page, count },
    });
};

// 删除指定部门
export const delDepartmentAction = async (did) => {
    return apiClientInstance.post(`/ws/departments/${did}/delete`);
};

// 通过id获取部门
export const getDepartmentAction = async (did) => {
    return apiClientInstance.get(`/ws/departments/` + did, {});
};

// 创建部门
export const addDepartmentAction = async (father_id, name) => {
    return apiClientInstance.post(`/ws/departments/add`, { father_id, name });
};

// 编辑部门
export const editDepartmentAction = async (did, name) => {
    return apiClientInstance.post(`/ws/departments/` + did + `/modify`, {
        name,
    });
};

// 获取部门下的员工
export const getDepartmentSiteusers = async (did, page, count) => {
    return apiClientInstance.get(`/ws/departments/` + did + `/siteusers/page`, {
        params: { page, count },
    });
};

// 获取部门下的员工岗位
export const getDepartmentPositionsAction = async (did, page, count) => {
    return apiClientInstance.get(`/ws/departments/` + did + `/positions/page`, {
        params: { page, count },
    });
};

// 删除指定岗位
export const delPositionAction = async (pid) => {
    return apiClientInstance.post(
        `/ws/positions/${pid}/delete`,
        {},
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
};

// 获取指定岗位
export const getPositionAction = async (pid) => {
    return apiClientInstance.get(`/ws/positions/` + pid);
};

// 编辑岗位
export const editPositionAction = async (pid, name, is_able, is_leader) => {
    return apiClientInstance.post(`/ws/positions/` + pid + `/modify`, {
        name,
        is_able,
        is_leader,
    });
};

// 新建岗位
export const addPositionAction = async (
    department_id,
    siteuser_id,
    name,
    is_able,
    is_leader
) => {
    return apiClientInstance.post(`/ws/positions/add`, {
        department_id,
        siteuser_id,
        name,
        is_able,
        is_leader,
    });
};

// 获取人员（用于搜索人员用）
export const getSearchSiteusersAction = async (keywords) => {
    return apiClientInstance.get(`/ws/siteusers/search`, {
        params: { keywords },
    });
};

// 获取所有员工，含分页
export const getSiteusersAction = async (page, count) => {
    return apiClientInstance.get(`/ws/siteusers/page`, {
        params: { page, count },
    });
};

// 删除人员
export const delSiteuserAction = async (sid) => {
    return apiClientInstance.post(
        `/ws/siteusers/` + sid + `/delete`,
        {},
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
};

// 获取指定员工的所有岗位
export const getSiteuserPositionsAction = async (sid, page, count) => {
    return apiClientInstance.get(`/ws/siteusers/` + sid + `/positions/page`, {
        params: { page, count },
    });
};

// 通过id获取用户
export const getSiteuserAction = async (sid) => {
    return apiClientInstance.get(`/ws/siteusers/` + sid, {});
};

// 创建人员
export const addSiteuserAction = async (
    username,
    password,
    nickname,
    tel,
    is_valid,
    role,
    permissions
) => {
    // 定义 _password 变量
    let _password = '';
    // 根据条件设置 _password 的值
    if (password !== '' && password !== undefined && password !== null) {
        _password = password;
    }

    let _tel = '';
    // 根据条件设置 _tel 的值
    if (tel !== '' && tel !== undefined && tel !== null) {
        _tel = tel;
    }
    return apiClientInstance.post(`/ws/siteusers/add`, {
        username,
        password: _password,
        nickname,
        tel: _tel,
        is_valid,
        role,
        permissions,
    });
};

// 编辑人员
export const editSiteuserAction = async (
    sid,
    username,
    password,
    nickname,
    tel,
    is_valid,
    role,
    permissions
) => {
    // 定义 _password 变量
    let _password = '';
    // 根据条件设置 _password 的值
    if (password !== '' && password !== undefined && password !== null) {
        _password = password;
    }

    let _tel = '';
    // 根据条件设置 _tel 的值
    if (tel !== '' && tel !== undefined && tel !== null) {
        _tel = tel;
    }
    return apiClientInstance.post(`/ws/siteusers/` + sid + `/modify`, {
        username,
        password: _password,
        nickname,
        tel: _tel,
        is_valid,
        role,
        permissions,
    });
};

// 获取文件模板
export const modifyPasswordAction = async (sid, old_password, new_password) => {
    return apiClientInstance.post(`/api/siteusers/${sid}/modify_password`, {
        old_password,
        new_password,
    });
};
