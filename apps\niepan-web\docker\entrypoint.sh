#!/bin/bash



# if you are using windows, you may need to convert the file to unix format
# you can use the Ubuntu terminal to convert this file to unix format
# otherwise, you may get the error after running the docker container

# sudo apt-get install dos2unix
# dos2unix entrypoint.sh


set -e

export NEXT_PUBLIC_DEPLOY_ENV=${DEPLOY_ENV}
export NEXT_PUBLIC_EDITION=${EDITION}
export NEXT_PUBLIC_API_PREFIX=${CONSOLE_API_URL}/console/api
export NEXT_PUBLIC_PUBLIC_API_PREFIX=${APP_API_URL}/api
export NEXT_PUBLIC_MARKETPLACE_API_PREFIX=${MARKETPLACE_API_URL}/api/v1
export NEXT_PUBLIC_MARKETPLACE_URL_PREFIX=${MARKETPLACE_URL}

export NEXT_PUBLIC_API_V1_PREFIX=${BFF_API_URL}/api/v1

export NEXT_PUBLIC_SENTRY_DSN=${SENTRY_DSN}
export NEXT_PUBLIC_SITE_ABOUT=${SITE_ABOUT}
export NEXT_TELEMETRY_DISABLED=${NEXT_TELEMETRY_DISABLED}

export NEXT_PUBLIC_TEXT_GENERATION_TIMEOUT_MS=${TEXT_GENERATION_TIMEOUT_MS}
export NEXT_PUBLIC_CSP_WHITELIST=${CSP_WHITELIST}
export NEXT_PUBLIC_TOP_K_MAX_VALUE=${TOP_K_MAX_VALUE}
export NEXT_PUBLIC_INDEXING_MAX_SEGMENTATION_TOKENS_LENGTH=${INDEXING_MAX_SEGMENTATION_TOKENS_LENGTH}
export NEXT_PUBLIC_MAX_TOOLS_NUM=${MAX_TOOLS_NUM}


# 替换静态文件中的占位符
if [ ! -z "${BFF_API_URL}" ]; then
  echo "Replacing BFF_API_URL placeholder in static files..."
  # 检查文件是否存在
  if [ -f "/app/web/public/nq/static-config/config.js" ]; then
    echo "Found config.js file, current content:"
    cat /app/web/public/nq/static-config/config.js

    # 使用sed替换config.js中的占位符
    sed -i.bak "s|__BFF_API_URL__|${BFF_API_URL}|g" /app/web/public/nq/static-config/config.js

    # 删除备份文件
    rm -f /app/web/public/nq/static-config/config.js.bak

    echo "Replacement completed. New content:"
    cat /app/web/public/nq/static-config/config.js
  else
    echo "Warning: config.js file not found at /app/web/public/nq/static-config/config.js"
    # 尝试查找文件的实际位置
    find /app/web -name "config.js" | grep -v "node_modules"
  fi
fi

if [ ! -z "${CONSOLE_API_URL}" ]; then
  echo "Replacing CONSOLE_API_URL placeholder in static files..."
  # 检查文件是否存在
  if [ -f "/app/web/public/nq/static-config/config.js" ]; then
    echo "Found config.js file, current content:"
    cat /app/web/public/nq/static-config/config.js

    # 使用sed替换config.js中的占位符
    sed -i.bak "s|__CONSOLE_API_URL__|${CONSOLE_API_URL}|g" /app/web/public/nq/static-config/config.js

    # 删除备份文件
    rm -f /app/web/public/nq/static-config/config.js.bak

    echo "Replacement completed. New content:"
    cat /app/web/public/nq/static-config/config.js
  else
    echo "Warning: config.js file not found at /app/web/public/nq/static-config/config.js"
    # 尝试查找文件的实际位置
    find /app/web -name "config.js" | grep -v "node_modules"
  fi
fi

# 替换静态文件中的占位符 __CONSOLE_API_URL__, __APP_API_URL__, __BFF_API_URL__
# if [ ! -z "${APP_API_URL}" ]; then
#   echo "Replacing APP_API_URL placeholder in static files..."
#   # 检查文件是否存在
#   if [ -f "/app/web/.env" ]; then
#     echo "Found .env file, current content:"
#     cat /app/web/.env

#     # 使用sed替换.env中的占位符，使用更简单的占位符格式
#     sed -i.bak "s|__BFF_API_URL__|${BFF_API_URL}|g" /app/web/.env
#     sed -i.bak "s|__CONSOLE_API_URL__|${CONSOLE_API_URL}|g" /app/web/.env
#     sed -i.bak "s|__APP_API_URL__|${APP_API_URL}|g" /app/web/.env

#     # 删除备份文件
#     rm -f /app/web/.env.bak

#     echo "Replacement completed. New content:"
#     cat /app/web/.env

#     # 导出替换后的环境变量，确保它们在运行时可用
#     export NEXT_PUBLIC_API_V1_PREFIX="${BFF_API_URL}/api/v1"
#     echo "Exported NEXT_PUBLIC_API_V1_PREFIX=${NEXT_PUBLIC_API_V1_PREFIX}"
#   else
#     echo "Warning: .env file not found at /app/web/.env"
#     # 尝试查找文件的实际位置
#     find /app/web -name ".env" | grep -v "node_modules"
#   fi
# fi



# window._server_config = {
#   VITE_API_BASE_URL: "__BFF_API_URL__",
# };


pm2 start /app/web/server.js --name niepan-web --cwd /app/web -i ${PM2_INSTANCES} --no-daemon
