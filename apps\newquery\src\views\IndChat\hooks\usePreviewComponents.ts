/* eslint-disable no-unused-vars */
import { markRaw } from "vue";
import { resolveComponent } from "../components/componentRegister";
import { PreviewMessage, MessageComponent } from "./types";

export function usePreviewComponents() {
  const generateComponentKey = (uid: string): string => {
    return `${uid}-${Date.now()}`;
  };

  const createComponent = (uid: string, data: any): MessageComponent => {
    return {
      component: markRaw(resolveComponent(uid)),
      data: data,
      key: generateComponentKey(uid),
    };
  };

  const updatePreviewComponents = (
    messages: PreviewMessage | PreviewMessage[]
  ): PreviewMessage[] => {
    if (!Array.isArray(messages)) {
      messages = [messages];
    }

    return messages.map((msg) => {
      if (!msg?.uid || !msg?.area) return msg;

      const componentInfo = createComponent(msg.uid, msg.data);
      return {
        ...msg,
        componentKey: componentInfo.key,
        component: componentInfo.component,
        data: componentInfo.data,
      };
    });
  };

  return {
    updatePreviewComponents,
  };
}
