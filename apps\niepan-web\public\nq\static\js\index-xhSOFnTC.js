import{d as q,al as J,q as V,u as U,r,z,e as L,c as l,o as t,b as s,p as P,a as S,A as B,f as p,M as x,aa as v,t as h,m as C,I as Q,H as T,w as A,J as Z,K as O,Q as W,x as X,y as Y}from"./pnpm-pnpm-B4aX-tnA.js";import{b as ee,a as D,w as te,x as se}from"./index-oCQpNzhc.js";import{E,_ as N,g as ae}from"./apiUrl-l4DNRpzY.js";import{_ as oe}from"./SearchIcon.vue_vue_type_script_setup_true_lang-4BGMlqW3.js";const le=m=>(X("data-v-8f098114"),m=m(),Y(),m),ne={class:"w-full h-[52px] px-4 py-3 relative flex justify-center items-center",style:{"border-bottom":"1px solid #e5e5e5"}},ce=le(()=>s("button",{class:"p-[6px] border-none bg-transparent h-[30px]"},[s("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 18 18",fill:"none"},[s("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M9.53033 5.03033C9.82322 4.73744 9.82322 4.26256 9.53033 3.96967C9.23744 3.67678 8.76256 3.67678 8.46967 3.96967L4.01065 8.42869C3.85104 8.56623 3.75 8.76985 3.75 8.99707C3.75 8.99757 3.75 8.99807 3.75 8.99857C3.74964 9.19099 3.82286 9.38352 3.96967 9.53033L8.46967 14.0303C8.76256 14.3232 9.23744 14.3232 9.53033 14.0303C9.82322 13.7374 9.82322 13.2626 9.53033 12.9697L6.30773 9.74707H13.5C13.9142 9.74707 14.25 9.41128 14.25 8.99707C14.25 8.58286 13.9142 8.24707 13.5 8.24707H6.31359L9.53033 5.03033Z",fill:"#697077"})])],-1)),re={class:"flex items-center justify-center w-full"},pe={class:"relative"},ie={key:0,class:"flex flex-col items-center mr-2"},de={key:1,class:"w-[30px] h-[30px] rounded-full flex items-center justify-center text-white text-xl font-medium"},ue=["src","alt"],fe={key:1},me={key:0,class:"truncate max-w-[182px] mr-[6px]"},ve={key:0,class:"absolute top-[40px] left-0 bg-[#fff] rounded-lg w-full shadow-sm p-2 text-[#343A3F] text-xs"},_e={class:"relative"},xe={class:"max-h-[40vh] overflow-y-auto custom-scrollbar"},he=["onClick"],we={class:"relative"},ge={class:"flex flex-col items-center mr-2"},ye={key:1,class:"w-6 h-6 rounded-full flex items-center justify-center text-white text-xl font-medium"},ke=["src","alt"],be={class:"truncate max-w-[182px] mr-[6px]"},Ce={name:"ChangeAppNav"},Ae=q({...Ce,setup(m){const{t:i}=J(),w=V(),g=U(),c=r(w.params.appId),e=r({}),y=n=>ae(n),d=r([]),M=r(1),K=r(1e6),_=r(""),u=r(!1),k=r(null),R=()=>{u.value=!u.value},G=n=>{c.value=n.id,e.value=n,u.value=!u.value,g.push(`/app-chat/${n.id}`)},H=n=>{n.key==="Enter"&&(console.log("搜索"),I())},I=async()=>{const n={page:M.value,limit:K.value};_.value&&(n.name=_.value);try{k.value=W.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});const a=await ee(n);if(d.value=a.data.installed_apps,k.value.close(),c.value){const f=d.value.findIndex(b=>b.id==c.value);f!==-1?(c.value=d.value[f].id,e.value=d.value[f]):a.data.length>0&&(c.value=a.data[0].id,e.value=a.data[0])}else a.data.length>0&&(c.value=a.data[0].id,e.value=a.data[0])}finally{k.value.close()}};return z(()=>{I()}),(n,a)=>{var j,$;const f=L("el-icon"),b=L("el-input");return t(),l("div",ne,[s("div",{class:"flex items-center text-sm font-medium absolute top-3 left-4 cursor-pointer",onClick:a[0]||(a[0]=o=>n.$emit("goBackClick"))},[ce,P(" 返回 ")]),s("div",{class:"text-sm flex items-center cursor-pointer relative min-w-[250px]",onClick:A(R,["stop"])},[s("div",re,[s("div",pe,[e.value.app?(t(),l("div",ie,[e.value.app.icon_type==="emoji"?(t(),l("div",{key:0,class:"w-[30px] h-[30px] rounded-full flex items-center justify-center text-white text-xl font-medium",style:x(`background-color: ${e.value.app.icon_background||"#FFEAD5"}`)},[p(E,{emojiId:e.value.app.icon,size:14},null,8,["emojiId"])],4)):e.value.app.icon_url?(t(),l("div",de,[s("img",{src:y(e.value.app.icon_url),alt:v(i)("apps.appIcon"),class:"w-[40px] h-[40px] object-contain rounded-full"},null,8,ue)])):(t(),l("div",{key:2,class:"w-[30px] h-[30px] rounded-full flex items-center justify-center text-white text-xl font-medium",style:x(`background-color: ${e.value.app.icon_background||"#FFEAD5"}`)},h((j=e.value.app.name)==null?void 0:j.charAt(0)),5)),p(N,{type:e.value.app.mode,wrapperClassName:"!rounded-full absolute -bottom-[2px] right-[4px] w-4 h-4 shadow-sm",class:"h-4 w-4 rounded-full"},null,8,["type"])])):(t(),l("div",fe))]),e.value.app?(t(),l("div",me,h(($=e.value)==null?void 0:$.app.name),1)):S("",!0),u.value?(t(),B(f,{key:2,class:"flex-shrink-0",color:"#A2A9B0"},{default:C(()=>[p(v(T))]),_:1})):(t(),B(f,{key:1,class:"flex-shrink-0",color:"#A2A9B0"},{default:C(()=>[p(v(Q))]),_:1}))]),u.value?(t(),l("div",ve,[s("div",_e,[p(b,{modelValue:_.value,"onUpdate:modelValue":a[1]||(a[1]=o=>_.value=o),placeholder:"搜索",size:"default",class:"w-full search-input",onClick:a[2]||(a[2]=A(()=>{},["stop"])),onKeydown:H},{prefix:C(()=>[p(oe,{color:"#343A3F"})]),_:1},8,["modelValue"])]),s("div",xe,[(t(!0),l(Z,null,O(d.value,o=>{var F;return t(),l("div",{class:"flex items-center cursor-pointer py-[5px] px-3",key:o.app.id,onClick:A(Le=>G(o),["stop"])},[s("div",we,[s("div",ge,[o.app.icon_type==="emoji"?(t(),l("div",{key:0,class:"w-6 h-6 rounded-full flex items-center justify-center text-white text-xl font-medium",style:x(`background-color: ${o.app.icon_background||"#FFEAD5"}`)},[p(E,{emojiId:o.app.icon,size:13},null,8,["emojiId"])],4)):o.app.icon_url?(t(),l("div",ye,[s("img",{src:y(o.app.icon_url),alt:v(i)("apps.appIcon"),class:"w-full h-full object-contain rounded-full"},null,8,ke)])):(t(),l("div",{key:2,class:"w-6 h-6 rounded-full flex items-center justify-center text-white text-xl font-medium",style:x(`background-color: ${o.app.icon_background||"#FFEAD5"}`)},h((F=o.app.name)==null?void 0:F.charAt(0)),5)),p(N,{type:o.app.mode,wrapperClassName:"!rounded-full absolute -bottom-[2px] right-[4px] w-4 h-4 shadow-sm",class:"h-[14px] w-[14px] rounded-full"},null,8,["type"])])]),s("div",be,h(o.app.name),1)],8,he)}),128))])])):S("",!0)])])}}}),Ie=D(Ae,[["__scopeId","data-v-8f098114"]]),je={class:"w-[100vw] h-[100vh] chat-container overflow-hidden"},$e=["src"],Fe={__name:"index",setup(m){const i=V();console.log(i.params,"route.params"),console.log(i.params.appId,"route.params.appId");const w=U(),g=()=>{w.push("/index")};te();const{globalConfig:c}=se(),e=r("");return z(()=>{c.workspaceSettings&&c.workspaceSettings.installedApp?e.value=c.workspaceSettings.installedApp:(console.warn("模型供应商嵌入URL未配置，请检查config.js文件"),e.value="http://localhost:3000/embed/installed-app"),console.log("embedUrl:",e.value),console.log("Final URL:",e.value+"/"+i.params.appId)}),(y,d)=>(t(),l("div",je,[p(Ie,{onGoBackClick:g}),s("iframe",{src:e.value+"/"+v(i).params.appId,class:"w-[100vw] h-[calc(100vh-55px)] border-0",frameborder:"0",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowfullscreen:""},null,8,$e)]))}},Ve=D(Fe,[["__scopeId","data-v-a6b78348"]]);export{Ve as default};
