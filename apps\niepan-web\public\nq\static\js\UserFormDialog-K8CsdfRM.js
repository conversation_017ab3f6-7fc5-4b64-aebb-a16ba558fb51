import{d as B,b1 as j,aL as G,am as H,e as m,c as F,o as h,b as r,A as k,a as M,m as n,ar as P,n as Q,t as W,r as v,b2 as X,s as I,f as s,J as Y,K as Z,p as $,x as ee,y as le}from"./pnpm-pnpm-B4aX-tnA.js";import{a as S}from"./index-oCQpNzhc.js";const oe=B({name:"CustomCheckboxRenderer",components:{Check:H,CircleCheck:G,Select:j},props:{isChecked:{type:Boolean,default:!1},label:{type:String,default:""}}}),te={class:"custom-checkbox-renderer"},ae={class:"checkbox-label"};function se(d,D,w,c,x,f){const p=m("el-icon");return h(),F("div",te,[r("div",{class:Q(["checkbox-box",{"is-checked":d.isChecked}])},[d.isChecked?(h(),k(p,{key:0,class:"check-icon"},{default:n(()=>[(h(),k(P(d.isChecked?"Check":"")))]),_:1})):M("",!0)],2),r("span",ae,W(d.label),1)])}const q=S(oe,[["render",se],["__scopeId","data-v-ba49453a"]]),A=d=>(ee("data-v-7c6340cb"),d=d(),le(),d),ne=A(()=>r("div",{class:"section-title mb-4"},"基本信息",-1)),re={class:"form-grid"},de={class:"form-column"},ce={class:"form-column"},ie=A(()=>r("div",{class:"section-title mb-4 mt-6"},"身份信息",-1)),me={class:"form-grid"},pe={class:"form-column"},ue={class:"form-column"},fe={class:"dialog-footer"},_e=B({__name:"UserFormDialog",props:{departmentData:{type:Array,required:!0},roleData:{type:Array,required:!0}},emits:["submit","update:modelValue"],setup(d,{expose:D,emit:w}){const c=d,x=w,f=v(),p=v(!1),_=v(!1),V=v(null),e=X({id:null,userId:null,name:"",email:"",password:"",phone:"",departmentIds:[],roleIds:[],departments:[],roles:[]}),R=I(()=>({email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],password:[{required:!_.value,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6个字符",trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$|^$/,message:"请输入正确的手机号格式",trigger:"blur"}],departments:[{required:!0,message:"请选择部门",trigger:"change"}],roles:[{required:!0,message:"请选择角色",trigger:"change"}]})),E=I(()=>c.departmentData),N=I(()=>c.roleData),z=o=>{e.departmentIds=o,e.departments=o.map(l=>{const a=(y,g)=>{for(const u of y){if(u.id===g)return{id:u.id,name:u.name};if(u.children&&u.children.length>0){const b=a(u.children,g);if(b)return b}}return null};return a(c.departmentData,l)}).filter(Boolean)},L=o=>{e.roleIds=o,e.roles=o.map(l=>{const a=c.roleData.find(i=>i.id===l);return a?{id:a.id,name:a.name}:null}).filter(Boolean)},T=async()=>{if(f.value)try{await f.value.validate(async(o,l)=>{if(o){const a={...e,id:V.value,status:"enabled"};x("submit",a,_.value)}else console.log("表单验证失败",l)})}catch(o){console.error("表单验证过程中发生错误:",o)}},J=()=>{p.value=!1},C=()=>{f.value&&f.value.resetFields(),e.id=null,e.userId=null,e.name="",e.email="",e.password="",e.phone="",e.departmentIds=[],e.roleIds=[],e.departments=[],e.roles=[],V.value=null};return D({openAddDialog:o=>{_.value=!1,C(),o&&o.length>0&&c.roleData.splice(0,c.roleData.length,...o),p.value=!0},openEditDialog:(o,l)=>{_.value=!0,C(),l&&l.length>0&&c.roleData.splice(0,c.roleData.length,...l),V.value=o.id,e.id=o.id,e.userId=o.userId,e.name=o.name,e.email=o.email,e.password="",e.phone=o.phone,e.departments=o.departments||[],e.departmentIds=e.departments.map(a=>a.id),e.roles=o.roles||[],e.roleIds=e.roles.map(a=>a.id),p.value=!0},closeDialog:()=>{p.value=!1}}),(o,l)=>{const a=m("el-input"),i=m("el-form-item"),y=m("el-tree-select"),g=m("el-option"),u=m("el-select"),b=m("el-form"),U=m("el-button"),K=m("el-dialog");return h(),k(K,{modelValue:p.value,"onUpdate:modelValue":l[6]||(l[6]=t=>p.value=t),title:_.value?"编辑用户":"添加用户",width:"760px","close-on-click-modal":!1,"show-close":!0,onClosed:C,class:"user-form-dialog"},{footer:n(()=>[r("div",fe,[s(U,{onClick:J,class:"cancel-btn"},{default:n(()=>[$("取消")]),_:1}),s(U,{type:"primary",onClick:T,class:"confirm-btn"},{default:n(()=>[$("确认")]),_:1})])]),default:n(()=>[s(b,{ref_key:"formRef",ref:f,model:e,rules:R.value,"label-position":"top",class:"user-form",size:"large"},{default:n(()=>[ne,r("div",re,[r("div",de,[s(i,{label:"邮箱",prop:"email"},{default:n(()=>[s(a,{modelValue:e.email,"onUpdate:modelValue":l[0]||(l[0]=t=>e.email=t),placeholder:"请输入邮箱",class:"form-input"},null,8,["modelValue"])]),_:1}),s(i,{label:"姓名",prop:"name"},{default:n(()=>[s(a,{modelValue:e.name,"onUpdate:modelValue":l[1]||(l[1]=t=>e.name=t),placeholder:"请输入姓名",class:"form-input"},null,8,["modelValue"])]),_:1})]),r("div",ce,[s(i,{label:"设置密码",prop:"password"},{default:n(()=>[s(a,{modelValue:e.password,"onUpdate:modelValue":l[2]||(l[2]=t=>e.password=t),placeholder:_.value?"编辑时密码可不填":"请输入密码",type:"password",class:"form-input","show-password":""},null,8,["modelValue","placeholder"])]),_:1}),s(i,{label:"手机号",prop:"phone"},{default:n(()=>[s(a,{modelValue:e.phone,"onUpdate:modelValue":l[3]||(l[3]=t=>e.phone=t),placeholder:"请输入手机号",class:"form-input"},null,8,["modelValue"])]),_:1})])]),ie,r("div",me,[r("div",pe,[s(i,{label:"选择部门",prop:"departments"},{default:n(()=>[s(y,{modelValue:e.departmentIds,"onUpdate:modelValue":l[4]||(l[4]=t=>e.departmentIds=t),data:E.value,props:{label:"name",children:"children"},"node-key":"id",multiple:"","collapse-tags":"","collapse-tags-tooltip":"",placeholder:"请选择部门",class:"form-input custom-checkbox-select",clearable:"","check-strictly":"","tag-type":"info","max-collapse-tags":2,onChange:z},{default:n(({node:t,data:O})=>[s(q,{"is-checked":e.departmentIds.includes(O.id),label:t.label},null,8,["is-checked","label"])]),_:1},8,["modelValue","data"])]),_:1})]),r("div",ue,[s(i,{label:"选择角色",prop:"roles"},{default:n(()=>[s(u,{modelValue:e.roleIds,"onUpdate:modelValue":l[5]||(l[5]=t=>e.roleIds=t),multiple:"","collapse-tags":"","collapse-tags-tooltip":"",placeholder:"请选择角色",class:"form-input custom-checkbox-select",clearable:"","tag-type":"info","max-collapse-tags":2,onChange:L},{default:n(()=>[(h(!0),F(Y,null,Z(N.value,t=>(h(),k(g,{key:t.id,label:t.name,value:t.id},{default:n(()=>[s(q,{"is-checked":e.roleIds.includes(t.id),label:t.name},null,8,["is-checked","label"])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])])]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])}}}),Ve=S(_e,[["__scopeId","data-v-7c6340cb"]]);export{Ve as default};
