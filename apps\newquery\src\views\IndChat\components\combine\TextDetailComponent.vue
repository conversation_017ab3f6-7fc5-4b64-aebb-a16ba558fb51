<!-- 列表组件 -->
<template>
  <div
    class="w-[360px] rounded-[22px] bg-white border border-solid border-[#F1F1F1] p-[20px] text-xs"
  >
    <div class="flex justify-between items-center">
      <div class="flex items-start">
        <img
          v-if="data?.titleImgUrl"
          :src="data?.titleImgUrl"
          alt=""
          class="w-[20px] h-[20px] object-contain object-center mt-[2px]"
        />
        <img
          v-else
          src="@/assets/images/home/<USER>/<EMAIL>"
          alt=""
          class="w-[20px] h-[20px] object-contain object-center mt-[2px]"
        />
        <span class="font-zhongcu ml-1.5 text-15 leading-[24px] break-words">{{
          data.title || "详细内容"
        }}</span>
      </div>
    </div>
    <div class="mt-[10px]">
      <div
        v-for="(item, index) in data?.items"
        :key="index"
        class="flex item-center py-[8px] border-0 border-b border-[#ECECF2] border-solid mt-[10px]"
      >
        <span class="truncate w-[10em] text-[#A2A9B0] shrink-0 mr-[5px]">{{
          item.title
        }}</span>
        <span v-if="['singleLine', 'multiLine'].includes(item.itemType)">{{
          item.content
        }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface ModelData {
  title?: string;
  titleImgUrl?: string;
  items?: Array<{
    title: string;
    content: string;
    itemType: string; //singleLine:单行   multiLine:多行
  }>;
}

const props = defineProps<{
  data: ModelData; //必填项
}>();
</script>
