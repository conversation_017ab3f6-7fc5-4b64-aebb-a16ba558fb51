<template>
  <div
    class="w-full h-[52px] px-4 py-3 relative flex justify-center items-center"
    style="border-bottom: 1px solid #e5e5e5"
  >
    <div
      class="flex items-center text-sm font-medium absolute top-3 left-4 cursor-pointer"
      @click="$emit('goBackClick')"
    >
      <button class="p-[6px] border-none bg-transparent h-[30px]">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="18"
          viewBox="0 0 18 18"
          fill="none"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M9.53033 5.03033C9.82322 4.73744 9.82322 4.26256 9.53033 3.96967C9.23744 3.67678 8.76256 3.67678 8.46967 3.96967L4.01065 8.42869C3.85104 8.56623 3.75 8.76985 3.75 8.99707C3.75 8.99757 3.75 8.99807 3.75 8.99857C3.74964 9.19099 3.82286 9.38352 3.96967 9.53033L8.46967 14.0303C8.76256 14.3232 9.23744 14.3232 9.53033 14.0303C9.82322 13.7374 9.82322 13.2626 9.53033 12.9697L6.30773 9.74707H13.5C13.9142 9.74707 14.25 9.41128 14.25 8.99707C14.25 8.58286 13.9142 8.24707 13.5 8.24707H6.31359L9.53033 5.03033Z"
            fill="#697077"
          />
        </svg>
      </button>
      返回
    </div>

    <div
      class="text-sm flex items-center cursor-pointer relative min-w-[250px]"
      @click.stop="showAppList"
    >
      <div class="flex items-center justify-center w-full">
        <div class="relative">
          <!-- 添加空值检查 -->
          <div v-if="selectedApp.app" class="flex flex-col items-center mr-2">
            <div
              v-if="selectedApp.app.icon_type === 'emoji'"
              class="w-[30px] h-[30px] rounded-full flex items-center justify-center text-white text-xl font-medium"
              :style="`background-color: ${selectedApp.app.icon_background || '#FFEAD5'}`"
            >
              <EmojiIcon :emojiId="selectedApp.app.icon" :size="14" />
            </div>
            <div
              v-else-if="selectedApp.app.icon_url"
              class="w-[30px] h-[30px] rounded-full flex items-center justify-center text-white text-xl font-medium"
            >
              <img
                :src="getIconUrl(selectedApp.app.icon_url)"
                :alt="t('apps.appIcon')"
                class="w-[40px] h-[40px] object-contain rounded-full"
              />
            </div>
            <div
              v-else
              class="w-[30px] h-[30px] rounded-full flex items-center justify-center text-white text-xl font-medium"
              :style="`background-color: ${selectedApp.app.icon_background || '#FFEAD5'}`"
            >
              {{ selectedApp.app.name?.charAt(0) }}
            </div>
            <AppTypeIcon
              :type="selectedApp.app.mode"
              wrapperClassName="!rounded-full absolute -bottom-[2px] right-[4px] w-4 h-4 shadow-sm"
              class="h-4 w-4 rounded-full"
            />
          </div>
          <div v-else>
            <!-- 可以添加加载状态或占位符 -->
          </div>
        </div>
        <div class="truncate max-w-[182px] mr-[6px]" v-if="selectedApp.app">
          {{ selectedApp?.app.name }}
        </div>
        <el-icon v-if="!isShowApp" class="flex-shrink-0" color="#A2A9B0"><ArrowDownBold /></el-icon>
        <el-icon v-else class="flex-shrink-0" color="#A2A9B0"><ArrowUpBold /></el-icon>
      </div>

      <div
        class="absolute top-[40px] left-0 bg-[#fff] rounded-lg w-full shadow-sm p-2 text-[#343A3F] text-xs"
        v-if="isShowApp"
      >
        <div class="relative">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索"
            size="default"
            class="w-full search-input"
            @click.stop
            @keydown="handleKeyDown"
          >
            <template #prefix>
              <SearchIcon color="#343A3F" />
            </template>
          </el-input>
        </div>
        <div class="max-h-[40vh] overflow-y-auto custom-scrollbar">
          <div
            class="flex items-center cursor-pointer py-[5px] px-3"
            v-for="item in appList"
            :key="item.app.id"
            @click.stop="selectAppItem(item)"
          >
            <div class="relative">
              <div class="flex flex-col items-center mr-2">
                <div
                  v-if="item.app.icon_type === 'emoji'"
                  class="w-6 h-6 rounded-full flex items-center justify-center text-white text-xl font-medium"
                  :style="`background-color: ${item.app.icon_background || '#FFEAD5'}`"
                >
                  <EmojiIcon :emojiId="item.app.icon" :size="13" />
                </div>
                <div
                  v-else-if="item.app.icon_url"
                  class="w-6 h-6 rounded-full flex items-center justify-center text-white text-xl font-medium"
                >
                  <img
                    :src="getIconUrl(item.app.icon_url)"
                    :alt="t('apps.appIcon')"
                    class="w-full h-full object-contain rounded-full"
                  />
                </div>
                <div
                  v-else
                  class="w-6 h-6 rounded-full flex items-center justify-center text-white text-xl font-medium"
                  :style="`background-color: ${item.app.icon_background || '#FFEAD5'}`"
                >
                  {{ item.app.name?.charAt(0) }}
                </div>
                <AppTypeIcon
                  :type="item.app.mode"
                  wrapperClassName="!rounded-full absolute -bottom-[2px] right-[4px] w-4 h-4 shadow-sm"
                  class="h-[14px] w-[14px] rounded-full"
                />
              </div>
            </div>
            <div class="truncate max-w-[182px] mr-[6px]">{{ item.app.name }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
export default {
  name: 'ChangeAppNav',
};
</script>
<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue';
import { ArrowDownBold, ArrowUpBold } from '@element-plus/icons-vue';
import { fetchAppListApi } from '@/services/console-api';
import { ElLoading, ElMessage } from 'element-plus';
import { useRoute, useRouter } from 'vue-router';
import TagIcon from '@/components/icons/TagIcon.vue';
import AppTypeIcon from '@/components/icons/apps/AppTypeIcon.vue';
import EmojiIcon from '@/components/EmojiIcon/index.vue';
import SearchIcon from '@/components/icons/SearchIcon.vue';
import { getConsoleApiUrl } from '@/utils/apiUrl';
import { useI18n } from 'vue-i18n';

// 初始化i18n
const { t } = useI18n();
const route = useRoute();
const router = useRouter();
const selectedAppId = ref(route.params.appId); //默认选中第一个应用的id
const selectedApp = ref({}); //默认选中第一个应用

const getIconUrl = (url: string) => {
  return getConsoleApiUrl(url);
};

// 获取应用列表
const appList = ref([]);
const currentPage = ref(1);
const pageSize = ref(1000000);
const searchKeyword = ref('');
const isShowApp = ref(false);
const loading = ref(null);

// 展示应用列表
const showAppList = () => {
  isShowApp.value = !isShowApp.value;
  if (searchKeyword.value) {
    searchKeyword.value = '';
    handleGetAppList();
  }
};

// 切换应用
// 设置选择应用的函数
const selectAppItem = item => {
  //   console.log(item, '===================');
  selectedAppId.value = item.id;
  selectedApp.value = item;
  isShowApp.value = !isShowApp.value;
  router.push(`/app-chat/${item.id}`); // 跳转到应用详情页面
  //   console.log(item.id, 'item.app.id');
};

// 监听键盘事件
const handleKeyDown = event => {
  // 检查是否按下了Enter键
  if (event.key === 'Enter') {
    // console.log('搜索');
    handleGetAppList();
  }
};

const handleGetAppList = async () => {
  const params = {
    page: currentPage.value,
    limit: pageSize.value,
  };

  // 添加搜索关键词
  if (searchKeyword.value) {
    params.name = searchKeyword.value;
  }
  try {
    loading.value = ElLoading.service({
      lock: true,
      text: '加载中',
      background: 'rgba(0, 0, 0, 0.5)',
    });
    const response = await fetchAppListApi(params);
    appList.value = response.data.installed_apps;
    loading.value.close();
    if (selectedAppId.value) {
      const index = appList.value.findIndex(item => item.id == selectedAppId.value);
      //判断连接地址上是否有appId，如果有，就默认选中指定的应用，反之选择第一个应用
      if (index !== -1) {
        selectedAppId.value = appList.value[index].id;
        selectedApp.value = appList.value[index];
      } else {
        if (response.data.length > 0) {
          selectedAppId.value = response.data[0].id;
          selectedApp.value = response.data[0];
        }
      }
    } else {
      if (response.data.length > 0) {
        selectedAppId.value = response.data[0].id;
        selectedApp.value = response.data[0];
      }
    }
  } finally {
    loading.value.close();
  }
};

onMounted(() => {
  handleGetAppList();
});
</script>
<style lang="css" scoped>
:deep(.el-input__wrapper) {
  background-color: #f2f7fb;
  border: none;
  border-radius: 8px;
  box-shadow: none;
}
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #d4d4d4 transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #d4d4d4;
  border-radius: 3px;
}
</style>
