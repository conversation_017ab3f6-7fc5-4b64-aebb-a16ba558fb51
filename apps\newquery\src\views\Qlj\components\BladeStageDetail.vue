<template>
  <div>
    <div class="text-sm text-[#121619]">叶片级号 STAGE</div>
    <div class="flex items-center justify-between mt-[10px] mb-4">
      <el-button disabled class="!w-4 !h-4 !p-0 !rounded-full flex-shrink-0 !mr-3">
        <el-icon size="10"><ArrowLeft /></el-icon>
      </el-button>
      <div class="text-xs text-[#343A3F] flex w-full">
        <el-button
          class="!h-5 !px-2 !py-0 !text-[10px] !text-[#343A3F] flex items-center"
          v-for="(item, index) in bladesStageList"
          :key="index"
        >
          {{ item.stage }}
        </el-button>
      </div>
      <el-button class="!w-4 !h-4 !p-0 !rounded-full flex-shrink-0 !ml-3">
        <el-icon size="10"><ArrowRight /></el-icon>
      </el-button>
    </div>
    <div class="flex items-center text-sm text-[#121619] px-3 w-full">
      <el-tabs v-model="selectedStage" class="demo-tabs w-full" @tab-click="handleClick">
        <el-tab-pane label="叶片基本信息" name="first">
          <div class="bg-red-50 h-[500px] w-full">
            {{ bladesStageList }}
          </div>
        </el-tab-pane>
        <el-tab-pane label="叶片构成信息" name="second">叶片构成信息</el-tab-pane>
        <el-tab-pane label="叶型检验信息" name="third">叶型检验信息</el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';

const props = defineProps({
  bladesStageList: {
    type: Array,
    required: true,
  },
});

const selectedStage = ref('first');

const handleClick = (tab, event) => {
  console.log(tab, event);
};
</script>

<style scoped>
:deep(.el-tabs__item.is-active) {
  color: #129bfe;
}
:deep(.el-tabs__nav-wrap::after) {
  display: none;
}
:deep(.el-tabs__nav) {
  border: none;
}
:deep(.el-tabs__active-bar) {
  background-color: #129bfe;
}
:deep(.el-tabs__item) {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: auto;
  padding: 0 20px;
}
</style>
