## 背景

为了使得不同项目有不同的体验，并做最大化模块复用，当前 仓库采用配置的模式来进行个性化配置。
apps/config-project 用于存放具体项目的配置文件，在构件或者开发的时候，可以使用对应的配置文件覆盖对应项目的 static-config目录

配置原则：

- 在 config hooks 里面添加配置项默认值，默认为 aialign 产品的默认形体
- 如果需要调整配置，则在对应的 static-config/config.js 里面修改
- 所有新增配置，默认不配则默认为原有功能，不能在不配置的情况下影响原功能

## 项目说明

aa : 公司通用产品
qlj : 汽轮机项目

## 开发说明

执行命令如下，开发哪个项目执行哪个命令

pnpm nq:dev:aa
pnpm nq:dev:qlj

pnpm nw:dev:aa
pnpm nw:dev:qlj

## 配置说明

以下为具体的配置说明
[待补充]
