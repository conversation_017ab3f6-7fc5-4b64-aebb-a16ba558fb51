{"name": "niepan-web-plus", "version": "0.0.1", "private": true, "description": "Niepan Web Plus Monorepo", "scripts": {"build": "pnpm -r build", "dev": "pnpm -r dev", "lint": "pnpm -r lint", "test": "pnpm -r test", "clean": "pnpm -r clean && rimraf node_modules", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "nw": "pnpm --filter niepan-web", "nq": "pnpm --filter new<PERSON>y", "nq:build": "node scripts/build-nq.js", "nq:dev:aa": "node scripts/dev-nq-config.js aa", "nq:dev:qlj": "node scripts/dev-nq-config.js qlj", "nw:dev:aa": "node scripts/dev-nw-config.js aa", "nw:dev:qlj": "node scripts/dev-nw-config.js qlj", "deploy:test": "pnpm nq:build && cd apps/niepan-web && ./update-docker.sh aialign-niepan-web-test aialign/niepan-web-test 30002 http://niepantest.aialign.com.cn http://idtest.aialign.com.cn && cd ../..", "deploy:prod": "pnpm nq:build && cd ./apps/niepan-web/ && docker build . -t aialign/niepan-web:1.1.3 && cd ../.. && node scripts/git-tag.js"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "eslint-config-prettier": "^9.0.0", "prettier": "^3.0.0", "rimraf": "^5.0.1", "typescript": "^5.1.6"}, "engines": {"node": ">=18.0.0"}, "packageManager": "pnpm@8.6.0"}