// 导入组件注册函数
import { registerAsyncComponent, registerComponent } from './componentRegistry';

// 同步注册一些基础组件
import TextDetailComponent from './TextDetailComponent';
import ListComponent from './ListComponent';

// 注册同步组件
registerComponent('text-detail-component', TextDetailComponent);
registerComponent('list-component', ListComponent);

// 注册异步组件（懒加载）
registerAsyncComponent('pptx-preview-component', () => import('./PPTXPreviewComponent'));

// 可以继续注册更多组件
// registerAsyncComponent('pdf-viewer-component', () => import('./PDFViewerComponent'));
// registerAsyncComponent('chart-component', () => import('./ChartComponent'));
// registerAsyncComponent('table-component', () => import('./TableComponent'));

// 导出所有需要的类型和函数
export * from './componentRegistry';
export { default as PPTXPreviewComponent } from './PPTXPreviewComponent';
export { default as TextDetailComponent } from './TextDetailComponent';
export { default as ListComponent } from './ListComponent';
