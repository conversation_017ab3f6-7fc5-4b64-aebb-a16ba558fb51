import { createWrappedApiClient } from '@/services/api-wrapper';
import { useConfig } from '@/hooks/useConfig';

const { serverConfig } = useConfig();

console.log('aios.js - serverConfig:', serverConfig);
console.log(
    'aios.js - baseURL will be:',
    `${serverConfig.VITE_API_BASE_URL}/ai_os`
);

const apiClientInstance = createWrappedApiClient({
    baseURL: `${serverConfig.VITE_API_BASE_URL}/ai_os`,
    headers: {
        'Content-Type': 'multipart/form-data',
    },
    tokenKey: 'console_token',
});

// 获取时间、系统状态、网络状态数据
export const getDesktopsData1Action = async () => {
    return apiClientInstance.get(`/ws/desktops/realtime`, {
        timeout: 10000,
    });
};

// 获取存储空间、大模型数据
export const getDesktopsData2Action = async () => {
    return apiClientInstance.get(`/ws/desktops/delay`, {});
};

// // 获取存储空间、大模型数据
// export const getDesktopsData2Action = async () => {
//     return apiClientInstance.get(`/ws/desktops/delay`,{
//     });
// };
