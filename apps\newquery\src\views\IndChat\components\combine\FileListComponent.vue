<!-- 文件列表组件 -->
<template>
  <div class="w-full pt-4 pb-0 px-0">
    <div
      class="grid grid-cols-auto gap-4"
      style="grid-template-columns: repeat(auto-fill, 248px)"
    >
      <div
        v-for="file in data.files"
        :key="file.fileUrl"
        class="group flex h-[68px] cursor-pointer transition-all duration-300 rounded-[18px] hover:shadow-hover"
        @click="(event) => handleFileClick(file, event)"
      >
        <!-- 图标区域 -->
        <div
          class="w-[66px] h-[68px] rounded-l-[18px] flex items-center justify-center border-1 border-[#E5E5E5] border-solid"
          :class="getFileTypeClass(file.title)"
        >
          <component :is="getFileIcon(file.title)" class="w-[26px] h-[26px]" />
        </div>
        <!-- 文字区域 -->
        <div
          class="flex-1 min-w-0 h-full flex flex-col justify-center px-3 bg-white border border-1 border-[#E5E5E5] border-l-0 rounded-r-[18px] border-solid"
        >
          <div
            class="text-[#121619] text-[14px] leading-[22px] font-normal font-['PingFang SC'] truncate mb-1"
          >
            {{ file.title }}
          </div>
          <div
            class="text-[#A2A9B0] text-[12px] leading-[16px] font-normal font-['PingFang SC'] truncate"
          >
            点击打开文档
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getFileIcon } from "@/config/fileIconMap";
import type { PropType } from "vue";
import { useMessageBus } from "../../hooks/useMessageBus";

interface FileInfo {
  fileUrl: string;
  fileType?: string;
  title: string;
  initialPage?: number;
}

interface ModelData {
  files: FileInfo[];
  // title?: string;
  // totalNum?: number;
}

const props = defineProps({
  data: {
    type: Object as PropType<ModelData>,
    required: true,
  },
});

const { triggerWorkbench } = useMessageBus();

const handleFileClick = (file: FileInfo, event: Event) => {
  // 阻止事件冒泡
  event?.stopPropagation();

  // 触发工作台展示PDF预览
  triggerWorkbench("pdf-viewer-component", {
    fileUrl: file.fileUrl,
    fileType: file.fileType,
    title: file.title,
    initialPage: file.initialPage,
  });
};

const getFileTypeClass = (fileName: string) => {
  const extension = fileName.split(".").pop()?.toLowerCase();
  console.log(extension);
  switch (extension) {
    case "ppt":
    case "pptx":
      return "file-type-ppt";
    case "doc":
    case "docx":
      return "file-type-doc";
    // case "pdf":
    //   return "file-type-pdf";
    default:
      return "file-type-default";
  }
};
</script>

<style scoped>
.file-type-doc {
  background: rgba(64, 165, 238, 0.2);
  border: 1px solid #e5e5e5;
  border-right: none;
}

.file-type-ppt {
  background: rgba(255, 143, 107, 0.2);
  border: 1px solid #e5e5e5;
  border-right: none;
}

.file-type-pdf {
  background: rgba(251, 84, 84, 0.2);
  border: 1px solid #e5e5e5;
  border-right: none;
}

.file-type-default {
  background: rgba(250, 173, 20, 0.2);
  border: 1px solid #e5e5e5;
  border-right: none;
}

/* 添加hover阴影效果 */
.hover\:shadow-hover:hover {
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.1);
}
</style>
