
<template>
    <div class="flex justify-between flex-wrap text-sm">
        <div 
            v-for="(item, index) in data.items"
            :key="index"
            class="w-[49%] flex bg-[#F7F9FC] rounded-md py-[9px] px-[16px] mb-[12px]"
        >
            <div class="w-[8em] shrink-0 truncate mr-[8px] text-[#A2A9B0]">{{ item.title }}</div>
            <div v-if="['singleLine', 'multiLine'].includes(item.itemType)">{{ item.content }}</div>
        </div>
    </div>
</template>

<script setup lang="ts">

interface ModelData {
    items?: Array<{
        title: string;
        content: string;
        itemType: string;  //singleLine:单行   multiLine:多行
    }>;
}

const props = defineProps<{
    data: ModelData
}>();

</script>