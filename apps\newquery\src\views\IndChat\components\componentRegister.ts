import { defineAsyncComponent } from "vue";

const componentMap: Record<string, () => Promise<any>> = {
  "project-collection-component": () =>
    import("./combine/CenterProjectCollectionComponent.vue"),
  "opportunity-list-component": () =>
    import("./combine/CenterOpportunityListComponent.vue"),
  "project-list-component": () =>
    import("./combine/CenterProjectListComponent.vue"),
  "business-amount-component": () =>
    import("./combine/CenterBusinessAmountComponent.vue"),
  "recommend-system-link-component": () =>
    import("./combine/RecommendSystemLinkComponent.vue"),
  "list-component": () => import("./combine/ListComponent.vue"),
  "total-summary-component": () =>
    import("./combine/TotalSummaryComponent.vue"),
  "text-detail-component": () => import("./combine/TextDetailComponent.vue"),
  "workbench-text-detail-component": () =>
    import("./combine/WorkbenchTextDetailComponent.vue"),
  "pdf-viewer-component": () => import("./combine/PDFViewerComponent.vue"),
  // "file-list-component": () => import("./combine/FileListComponent.vue"),
  "file-list-component": () =>
    import("./combine/FileListAndPagesComponent.vue"),
};

// 基础加载组件
const LoadingComponent = {
  template: `
    <div class="flex items-center justify-center h-full">
      <div class="text-gray-500">加载中...</div>
    </div>
  `,
};

// 基础错误组件
const ErrorComponent = {
  template: `
    <div class="flex items-center justify-center h-full">
      <div class="text-red-500">加载失败</div>
    </div>
  `,
};

// 注册新组件
export function registerComponent(uid: string, loader: () => Promise<any>) {
  componentMap[uid] = loader;
}

// 解析组件
export function resolveComponent(uid: string) {
  const loader = componentMap[uid];
  if (!loader) {
    return defineAsyncComponent({
      loader: () => import("./combine/NotFound.vue"),
      loadingComponent: LoadingComponent,
      errorComponent: ErrorComponent,
    });
  }

  return defineAsyncComponent({
    loader,
    loadingComponent: LoadingComponent,
    errorComponent: ErrorComponent,
    delay: 100,
    timeout: 10000,
    suspensible: false,
    onError(error, retry, fail, attempts) {
      if (attempts <= 3) {
        retry();
      } else {
        console.error("Component load failed:", error);
        fail();
      }
    },
  });
}
