import type { FC } from 'react';
import { useState } from 'react';
import { useWorkbenchComponents } from '../../hooks/useWorkbenchComponents';
import { 
  pptxPreviewExample, 
  textDetailSuccessExample, 
  textDetailWarningExample,
  projectListExample,
  documentListExample,
  fullWorkbenchExample 
} from './examples';

const WorkbenchDemo: FC = () => {
  const { 
    renderComponents, 
    addComponent, 
    removeComponent, 
    clearComponents,
    updateComponentData,
    componentStats,
    componentsData 
  } = useWorkbenchComponents();

  const [selectedExample, setSelectedExample] = useState<string>('');

  const handleAddExample = (exampleKey: string) => {
    switch (exampleKey) {
      case 'pptx':
        addComponent(pptxPreviewExample);
        break;
      case 'text-success':
        addComponent(textDetailSuccessExample);
        break;
      case 'text-warning':
        addComponent(textDetailWarningExample);
        break;
      case 'project-list':
        addComponent(projectListExample);
        break;
      case 'document-list':
        addComponent(documentListExample);
        break;
      case 'full-example':
        clearComponents();
        fullWorkbenchExample.forEach(component => addComponent(component));
        break;
    }
    setSelectedExample('');
  };

  const handleUpdatePPTPage = () => {
    updateComponentData('pptx-preview-component', {
      initialPage: Math.floor(Math.random() * 10) + 1
    });
  };

  return (
    <div className="w-full h-full flex">
      {/* 控制面板 */}
      <div className="w-80 bg-gray-50 border-r p-4 overflow-auto">
        <h2 className="text-lg font-semibold mb-4">工作台组件演示</h2>
        
        {/* 统计信息 */}
        <div className="mb-6 p-3 bg-white rounded border">
          <h3 className="font-medium mb-2">组件统计</h3>
          <div className="text-sm text-gray-600">
            <div>总数: {componentStats.total}</div>
            <div>主区域: {componentStats.byArea.main || 0}</div>
          </div>
        </div>

        {/* 添加组件 */}
        <div className="mb-6">
          <h3 className="font-medium mb-3">添加组件</h3>
          <div className="space-y-2">
            <button
              onClick={() => handleAddExample('pptx')}
              className="w-full px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              PPT预览组件
            </button>
            <button
              onClick={() => handleAddExample('text-success')}
              className="w-full px-3 py-2 text-sm bg-green-500 text-white rounded hover:bg-green-600"
            >
              成功文本组件
            </button>
            <button
              onClick={() => handleAddExample('text-warning')}
              className="w-full px-3 py-2 text-sm bg-yellow-500 text-white rounded hover:bg-yellow-600"
            >
              警告文本组件
            </button>
            <button
              onClick={() => handleAddExample('project-list')}
              className="w-full px-3 py-2 text-sm bg-purple-500 text-white rounded hover:bg-purple-600"
            >
              项目列表组件
            </button>
            <button
              onClick={() => handleAddExample('document-list')}
              className="w-full px-3 py-2 text-sm bg-indigo-500 text-white rounded hover:bg-indigo-600"
            >
              文档列表组件
            </button>
            <button
              onClick={() => handleAddExample('full-example')}
              className="w-full px-3 py-2 text-sm bg-gray-700 text-white rounded hover:bg-gray-800"
            >
              完整示例
            </button>
          </div>
        </div>

        {/* 组件操作 */}
        <div className="mb-6">
          <h3 className="font-medium mb-3">组件操作</h3>
          <div className="space-y-2">
            <button
              onClick={handleUpdatePPTPage}
              className="w-full px-3 py-2 text-sm bg-orange-500 text-white rounded hover:bg-orange-600"
              disabled={!componentsData.some(c => c.uid === 'pptx-preview-component')}
            >
              随机更新PPT页码
            </button>
            <button
              onClick={clearComponents}
              className="w-full px-3 py-2 text-sm bg-red-500 text-white rounded hover:bg-red-600"
            >
              清空所有组件
            </button>
          </div>
        </div>

        {/* 当前组件列表 */}
        <div>
          <h3 className="font-medium mb-3">当前组件</h3>
          <div className="space-y-2">
            {componentsData.map((component) => (
              <div key={component.uid} className="flex items-center justify-between p-2 bg-white rounded border text-sm">
                <div className="flex-1 min-w-0">
                  <div className="font-medium truncate">{component.uid}</div>
                  <div className="text-gray-500 text-xs">{component.area}</div>
                </div>
                <button
                  onClick={() => removeComponent(component.uid)}
                  className="ml-2 px-2 py-1 text-xs bg-red-100 text-red-600 rounded hover:bg-red-200"
                >
                  删除
                </button>
              </div>
            ))}
            {componentsData.length === 0 && (
              <div className="text-gray-500 text-sm text-center py-4">
                暂无组件
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 工作台预览区域 */}
      <div className="flex-1 p-4 overflow-auto">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-xl font-semibold mb-4">工作台预览</h2>
          <div className="bg-white rounded-lg border min-h-96">
            <div className="p-4">
              {renderComponents('main')}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkbenchDemo;
