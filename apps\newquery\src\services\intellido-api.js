import { stringify } from 'qs';
import { useConfig } from '@/hooks/useConfig';
import { createWrappedApiClient } from '@/services/api-wrapper';

const { serverConfig } = useConfig();

// console.log('serverConfig', serverConfig);
// console.log('baseURL will be:', `${serverConfig.VITE_API_BASE_URL}/api/v1`);
// 创建一个预包装的API客户端实例，所有请求都会自动经过错误处理
const apiClientInstance = createWrappedApiClient({
    baseURL: `${serverConfig.VITE_API_BASE_URL}/api/v1`,
    headers: {
        'Content-Type': 'multipart/form-data',
        // 'Content-Type': 'application/json',
    },
    tokenKey: 'console_token',
});

export const loginApi = async (params) => {
    return apiClientInstance.post('/login', params);
};

// get localhost:9102/api/v1/users/current
export const getCurrentUserApi = async () => {
    return apiClientInstance.get('/users/current');
};

// get localhost:9102/api/v1/tenants/{tenant_id}/departments/all
export const getDepartmentsApi = async (tenant_id) => {
    return apiClientInstance.get(`/tenants/${tenant_id}/departments/all`);
};

// 添加部门 POST localhost:9102/api/v1/tenants/{tenant_id}/departments
export const addDepartmentApi = async (tenant_id, parent_id, name) => {
    const data = { name };
    if (parent_id) {
        data.parent_id = parent_id;
    }
    return apiClientInstance.post(`/tenants/${tenant_id}/departments`, data);
};

// 编辑部门 PUT localhost:9102/api/v1/tenants/{tenant_id}/departments/{department_id}
export const updateDepartmentApi = async (
    tenant_id,
    department_id,
    parent_id,
    name
) => {
    const data = { name };
    // if (parent_id) {
    //     data.parent_id = parent_id;
    // }
    data.sequence = 1;
    return apiClientInstance.post(
        `/tenants/${tenant_id}/departments/${department_id}`,
        data
    );
};

// 删除部门 DELETE localhost:9102/api/v1/tenants/{tenant_id}/departments/{department_id}
export const deleteDepartmentApi = async (tenant_id, department_id) => {
    return apiClientInstance.delete(
        `/tenants/${tenant_id}/departments/${department_id}`
    );
};

// 获取用户列表 GET http://*************:9102/api/v1/tenants/{tenant_id}/members?page=1&limit=20
// 可筛选参数: department_id, name, email, status, page, limit
export const getUsersApi = async (tenant_id, params = {}) => {
    // 构建查询参数
    const queryParams = {};

    // 添加筛选参数
    if (params.department_id) queryParams.department_id = params.department_id;
    if (params.name) queryParams.name = params.name;
    if (params.email) queryParams.email = params.email;
    if (params.status) queryParams.status = params.status;
    if (params.enabled)
        queryParams.enabled = params.enabled == 'enabled' ? true : false;

    // 添加分页参数
    queryParams.page = params.page || 1;
    queryParams.limit = params.limit || 20;

    // 将查询参数转换为URL查询字符串
    const queryString = stringify(queryParams);

    return apiClientInstance.get(
        `/tenants/${tenant_id}/members?${queryString}`
    );
};

// 更新用户状态 POST localhost:9102/api/v1/users/{user_id}/status
export const updateUserStatusApi = async (user_id, status) => {
    // 构建请求数据
    const data = { status };

    return apiClientInstance.post(`/users/${user_id}/status`, data);
};

// 创建用户 POST localhost:9102/api/v1/users/create_user_and_member
export const createUserApi = async (userData) => {
    return apiClientInstance.post('/users/create_user_and_member', userData, {
        headers: {
            'Content-Type': 'application/json',
        },
    });
};

// 编辑用户 POST localhost:9102/api/v1/users/update_user_and_member/{member_id}
export const updateUserApi = async (member_id, userData) => {
    return apiClientInstance.post(
        `/users/update_user_and_member/${member_id}`,
        userData,
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
};

// 获取角色列表 GET localhost:9102/api/v1/tenants/{tenant_id}/roles?page=1&limit=20
export const getRolesApi = async (tenant_id, params = {}) => {
    // 构建查询参数
    const queryParams = {};

    // 添加筛选参数
    if (params.name) queryParams.name = params.name;

    // 添加分页参数
    queryParams.page = params.page || 1;
    queryParams.limit = params.limit || 20;

    // 将查询参数转换为URL查询字符串
    const queryString = stringify(queryParams);

    return apiClientInstance.get(`/tenants/${tenant_id}/roles?${queryString}`);
};

// 获取license信息 GET /api/v1/tenants/{tenant_id}/license
export const getLicenseApi = async (tenant_id) => {
    console.log('获取license信息URL:', `/tenants/${tenant_id}/license`);
    return apiClientInstance.request(
        'get',
        `/tenants/${tenant_id}/license`,
        {},
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
};

// 激活license POST /api/v1/tenants/{tenant_id}/license/activate
export const activateLicenseApi = async (tenant_id, licenseCode) => {
    console.log('激活license URL:', `/tenants/${tenant_id}/license`);
    return apiClientInstance.request(
        'post',
        `/tenants/${tenant_id}/license`,
        {},
        {
            headers: {
                'Content-Type': 'application/json',
            },
            data: {
                data: licenseCode,
            },
        }
    );
};

// 移除license DELETE /api/v1/tenants/{tenant_id}/license
export const removeLicenseApi = async (tenant_id) => {
    console.log('移除license URL:', `/tenants/${tenant_id}/license`);
    return apiClientInstance.request(
        'delete',
        `/tenants/${tenant_id}/license`,
        {},
        {
            headers: {
                'Content-Type': 'application/json',
            },
        }
    );
};

// 修改成员状态 POST localhost:9102/api/v1/tenants/{tenant_id}/members/{member_id}/status
export const updateMemberStatusApi = async (tenant_id, member_id, enabled) => {
    // 构建请求数据
    const data = { enabled };

    return apiClientInstance.post(
        `/tenants/${tenant_id}/members/${member_id}/status`,
        data
    );
};
