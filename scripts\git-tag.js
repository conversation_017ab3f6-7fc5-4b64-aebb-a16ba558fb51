// 创建带时间戳的 git 标签并推送到远程仓库
const { execSync } = require('child_process');

// 生成时间戳格式：YYYYMMDDHHMM
const now = new Date();
const year = now.getFullYear();
const month = String(now.getMonth() + 1).padStart(2, '0');
const day = String(now.getDate()).padStart(2, '0');
const hours = String(now.getHours()).padStart(2, '0');
const minutes = String(now.getMinutes()).padStart(2, '0');

const timestamp = `${year}${month}${day}${hours}${minutes}`;
const tagName = `release-${timestamp}`;

console.log(`Creating git tag: ${tagName}`);

try {
  // 创建标签
  execSync(`git tag ${tagName}`, { stdio: 'inherit' });
  console.log(`Successfully created tag: ${tagName}`);
  
  // 推送标签到远程仓库
  execSync('git push origin --tags', { stdio: 'inherit' });
  console.log('Successfully pushed tags to remote repository');
} catch (error) {
  console.error('Error creating or pushing git tag:', error.message);
  process.exit(1);
}
