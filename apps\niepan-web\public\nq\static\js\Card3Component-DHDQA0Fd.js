import{f as _}from"./utils-ahBajhV-.js";import{u as g}from"./useLinkHandler-ppdGPCKM.js";import{d as k,s as C,c as r,o as i,b as e,t as o,J as x,K as w,n as l,aa as d}from"./pnpm-pnpm-B4aX-tnA.js";import"./index-oCQpNzhc.js";const y=["href","target"],F={class:"flex justify-between items-center"},L={class:"text-3xl font-zhongcu"},z={class:"font-zhongcu ml-1"},B={class:"text-[#3C3C43]"},E={class:"mt-[8px]"},N={class:"overflow-one w-[70%]"},H=k({__name:"Card3Component",props:{data:{}},setup(f){const n=f,{linkUrl:h,handleClick:u}=g(n),p=C(()=>{if(n.data.totalNum==0)return{value:0,unit:""};const{value:t,unit:a}=_(n.data.totalNum,!1);return{value:t,unit:a}}),v=t=>({initial:"20",requirement:"40",quotation:"60",negotiation:"80",win:"100",lost:"0"})[t]||"0";return(t,a)=>{var c;return i(),r("a",{href:d(h),onClick:a[0]||(a[0]=(...s)=>d(u)&&d(u)(...s)),target:t.data.isLink?"_blank":"",class:l(t.data.isLink?"":"cursor-default")},[e("div",{class:l(["bg-white p-[15px] pt-[7px] pb-[13px] border border-solid border-[#F1F1F1] rounded-[20px] h-full text-xs",{"transition-all duration-300 hover:shadow-lg hover:-translate-y-0.5":t.data.isLink}])},[e("div",F,[e("div",null,[e("div",null,[e("span",L,o(p.value.value),1),e("span",z,o(p.value.unit),1)]),e("div",B,o(t.data.title),1)])]),e("div",E,[(i(!0),r(x,null,w((c=t.data)==null?void 0:c.opportunities,(s,b)=>{var m;return i(),r("div",{key:s.id,class:l(["flex justify-between item-center py-[5px]",{"border-0 border-b border-[#ECECF2] border-solid":b!=((m=t.data)==null?void 0:m.opportunities.length)-1}])},[e("span",N,o(s.name),1),e("span",null,o(v(s.phase))+"%",1)],2)}),128))])],2)],10,y)}}});export{H as default};
