'use client';
import { useTranslation } from 'react-i18next';
import { Fragment, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { RiArrowDownSLine, RiArrowRightUpLine, RiInformation2Line } from '@remixicon/react';
import Link from 'next/link';
import { Menu, MenuButton, MenuItem, MenuItems, Transition } from '@headlessui/react';
import classNames from '@/utils/classnames';
import Avatar from '@/app/components/base/avatar';
import { logout } from '@/service/common';
import { useAppContext } from '@/context/app-context';
import { useModalContext } from '@/context/modal-context';
import {
  Earth,
  Help,
  Logout,
  PeopleSafe,
  System,
} from '@/app/components/base/icons/src/public/avatar';

import { fetchGetSysConfigs, fetchGetVersion } from '@/service/common';
import type { GetSysConfigsResponse, GetVersionResponse } from '@/models/common';

// import Indicator from '../indicator';

export type IAppSelector = {
  isMobile: boolean;
};

export default function AppSelector({ isMobile }: IAppSelector) {
  const itemClassName = `
    flex items-center w-full h-9 pl-3 pr-2 text-text-secondary system-md-regular
    rounded-lg hover:bg-state-base-hover cursor-pointer gap-1
  `;
  const router = useRouter();
  // const [aboutVisible, setAboutVisible] = useState(true);
  const { t } = useTranslation();
  const { userProfile } = useAppContext();

  const { setShowNewAccountSettingModal } = useModalContext();

  const [versionsData, setVersionsData] = useState<GetVersionResponse | null>(null);
  // const [sysConfigsData, setSysConfigsData] = useState<GetSysConfigsResponse>([]);
  const [helpDocsUrl, setHelpDocsUrl] = useState<string>('');

  const handleLogout = async () => {
    await logout({
      url: '/logout',
      params: {},
    });

    localStorage.removeItem('token');
    localStorage.removeItem('setup_status');
    localStorage.removeItem('console_token');
    localStorage.removeItem('refresh_token');

    router.push('/nq/login');
  };

  useEffect(() => {
    fetchGetVersion().then((res: GetVersionResponse) => {
      setVersionsData(res);
    });
  }, []);

  useEffect(() => {
    fetchGetSysConfigs().then((res: GetSysConfigsResponse) => {
      // console.log(res, '==================');
      // setSysConfigsData(res);
      const helpDocs = res.find(item => item.name === 'HELP_DOCS_URL');
      if (helpDocs) setHelpDocsUrl(helpDocs.value);
    });
  }, []);

  return (
    <div className="">
      <Menu as="div" className="relative inline-block text-left">
        {({ open }) => (
          <>
            <MenuButton
              className={`
                    inline-flex items-center
                    rounded-[20px] py-1 pl-1 pr-2.5 text-sm
                  text-text-secondary hover:bg-state-base-hover
                    mobile:px-1
                    ${open && 'bg-state-base-hover'}
                  `}
            >
              <Avatar
                avatar={userProfile.avatar_url}
                name={userProfile.name}
                className="mr-0 sm:mr-2"
                size={32}
              />
              {!isMobile && (
                <>
                  {userProfile.name}
                  <RiArrowDownSLine className="ml-1 h-3 w-3 text-text-tertiary" />
                </>
              )}
            </MenuButton>
            <Transition
              as={Fragment}
              enter="transition ease-out duration-100"
              enterFrom="transform opacity-0 scale-95"
              enterTo="transform opacity-100 scale-100"
              leave="transition ease-in duration-75"
              leaveFrom="transform opacity-100 scale-100"
              leaveTo="transform opacity-0 scale-95"
            >
              <MenuItems
                className="
                    absolute right-0 mt-1.5 w-60 max-w-80
                    origin-top-right divide-y divide-divider-subtle rounded-xl bg-components-panel-bg
                    px-3 pb-2
                    shadow-lg focus:outline-none
                  "
              >
                <MenuItem disabled>
                  <div className="flex flex-nowrap items-center justify-between p-2">
                    <div className="system-md-medium truncate text-text-primary">
                      {userProfile.email}
                    </div>
                    <Avatar
                      avatar={userProfile.avatar_url}
                      name={userProfile.name}
                      size={36}
                      className="ml-[10px]"
                    />
                  </div>
                </MenuItem>
                <div>
                  <MenuItem>
                    <div
                      className={classNames(itemClassName, 'data-[active]:bg-state-base-hover p-2')}
                      onClick={() => setShowNewAccountSettingModal({ payload: 'account' })}
                    >
                      <PeopleSafe className="h-[14px] w-[14px] shrink-0 text-text-tertiary" />
                      <div className="system-md-regular grow px-1 text-text-secondary">
                        {t('common.account.account')}
                      </div>
                    </div>
                  </MenuItem>
                  <MenuItem>
                    <div
                      className={classNames(itemClassName, 'data-[active]:bg-state-base-hover p-2')}
                      onClick={() => setShowNewAccountSettingModal({ payload: 'language' })}
                    >
                      <Earth className="h-[14px] w-[14px] shrink-0 text-text-tertiary" />
                      <div className="system-md-regular grow px-1 text-text-secondary">
                        {t('common.settings.language')}
                      </div>
                    </div>
                  </MenuItem>
                  <MenuItem>
                    <Link
                      className={classNames(
                        itemClassName,
                        'group',
                        'data-[active]:bg-state-base-hover p-2'
                      )}
                      href="/nq/setting/department"
                      target="_self"
                      rel="noopener noreferrer"
                    >
                      <System className="h-[14px] w-[14px] shrink-0 text-text-tertiary" />
                      <div className="system-md-regular grow px-1 text-text-secondary">
                        {t('common.settings.systemManagement')}
                      </div>
                      <RiArrowRightUpLine className="size-[14px] shrink-0 text-text-tertiary" />
                    </Link>
                  </MenuItem>
                  {/* <MenuItem>
                    <Link
                      className={classNames(
                        itemClassName,
                        "group",
                        "data-[active]:bg-state-base-hover"
                      )}
                      href="/account"
                      target="_self"
                      rel="noopener noreferrer"
                    >
                      <RiAccountCircleLine className="size-4 shrink-0 text-text-tertiary" />
                      <div className="system-md-regular grow px-1 text-red-500">
                        旧{t("common.account.account")}，仅供样式参考
                      </div>
                      <RiArrowRightUpLine className="size-[14px] shrink-0 text-text-tertiary" />
                    </Link>
                  </MenuItem> */}
                  {/* <MenuItem>
                    <div
                      className={classNames(
                        itemClassName,
                        "data-[active]:bg-state-base-hover p-2"
                      )}
                      onClick={() =>
                        setShowAccountSettingModal({ payload: "members" })
                      }
                    >
                      <Earth className="h-[14px] w-[14px] shrink-0 text-text-tertiary" />
                      <div className="system-md-regular grow px-1 text-red-500">
                        旧设置，仅供样式参考
                      </div>
                    </div>
                  </MenuItem> */}
                </div>
                <div>
                  <MenuItem>
                    {helpDocsUrl ? (
                      <Link
                        className={classNames(
                          itemClassName,
                          'group justify-between',
                          'data-[active]:bg-state-base-hover p-2'
                        )}
                        href={helpDocsUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <Help className="h-[14px] w-[14px] shrink-0 text-text-tertiary" />
                        <div className="system-md-regular grow px-1 text-text-secondary">
                          {t('common.userProfile.helpCenter')}
                        </div>
                        {/* <RiArrowRightUpLine className="size-[14px] shrink-0 text-text-tertiary" /> */}
                      </Link>
                    ) : (
                      <div
                        className={classNames(
                          itemClassName,
                          'group justify-between',
                          'data-[active]:bg-state-base-hover p-2'
                        )}
                      >
                        <Help className="h-[14px] w-[14px] shrink-0 text-text-tertiary" />
                        <div className="system-md-regular grow px-1 text-text-secondary">
                          {t('common.userProfile.helpCenter')}
                        </div>
                      </div>
                    )}
                  </MenuItem>
                  <MenuItem>
                    <div
                      className={classNames(
                        itemClassName,
                        'justify-between',
                        'data-[active]:bg-state-base-hover p-2'
                      )}
                      onClick={() => setShowNewAccountSettingModal({ payload: 'about' })}
                    >
                      <RiInformation2Line className="h-[14px] w-[14px] shrink-0 text-text-tertiary" />
                      <div className="system-md-regular grow px-1 text-text-secondary">
                        {t('common.userProfile.about')}
                      </div>
                      {versionsData && (
                        <div className="system-xs-regular mr-2 text-text-tertiary">
                          v {versionsData.current_version}
                        </div>
                      )}
                    </div>
                  </MenuItem>
                </div>
                <MenuItem>
                  <div onClick={() => handleLogout()}>
                    <div
                      className={classNames(
                        itemClassName,
                        'group justify-between',
                        'data-[active]:bg-state-base-hover p-2'
                      )}
                    >
                      <Logout className="h-[14px] w-[14px] shrink-0 text-text-tertiary" />
                      <div className="system-md-regular grow px-1 text-text-secondary">
                        {t('common.userProfile.logout')}
                      </div>
                    </div>
                  </div>
                </MenuItem>
              </MenuItems>
            </Transition>
          </>
        )}
      </Menu>
      {/* {aboutVisible && (
        <AccountAbout
          onCancel={() => setAboutVisible(false)}
          langeniusVersionInfo={langeniusVersionInfo}
        />
      )} */}
    </div>
  );
}
