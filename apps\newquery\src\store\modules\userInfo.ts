import { getSiteuserData } from '@/services/api';
import { getCurrentUserApi } from '@/services/intellido-api';
import { defineStore } from 'pinia';

export const useUserStore = defineStore('user', {
    state: () => ({
        userInfo: null,
    }),
    getters: {
        isLoggedIn: (state) => !!state.userInfo,
    },
    actions: {
        async fetchUserInfo(route) {
            try {
                // debugger;
                const response = await getCurrentUserApi().catch((err) => {
                    // debugger;
                    console.log(err);
                    if (err && err.response && err.response.status === 401) {
                        localStorage.removeItem('console_token');
                        if (route && route.fullPath) {
                            localStorage.setItem(
                                'redirectAfterLogin',
                                route.fullPath
                            );
                        } else {
                            localStorage.setItem(
                                'redirectAfterLogin',
                                window.location.pathname
                            );
                        }
                        window.location.replace('/nq/login');
                    }
                    return null;
                });
                if (response && response.data) {
                    // 当用户信息没有 nickname 时，使用 name 填充 nickname
                    if (
                        response.data &&
                        !response.data.nickname &&
                        response.data.name
                    ) {
                        response.data.nickname = response.data.name;
                    }
                    this.userInfo = response.data;
                    return response.data;
                }
            } catch (error) {
                console.error('Failed to fetch user info:', error);
                this.userInfo = null;
            }
            return null;
        },
        clearUserInfo() {
            this.userInfo = null;
        },
    },
});
