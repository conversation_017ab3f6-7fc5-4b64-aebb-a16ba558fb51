const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["static/js/CustomPagination-DJkAmntm.js","static/js/pnpm-pnpm-B4aX-tnA.js","static/css/pnpm-pnpm-BdGb95pV.css","static/js/index-oCQpNzhc.js","static/css/index-CgHh2pBR.css","static/css/CustomPagination-DcylDgxW.css"])))=>i.map(i=>d[i]);
import{w as O,d as Q,c as G,aK as H,a as W}from"./index-oCQpNzhc.js";import{d as X,r as s,s as Y,z as Z,e as c,c as ee,o as te,b as r,f as t,m as l,p as _,aa as U,aF as ae,a$ as oe,J as le,E as f,Q as ne,x as se,y as ie}from"./pnpm-pnpm-B4aX-tnA.js";const D=g=>(se("data-v-67706baa"),g=g(),ie(),g),re={class:"rounded-[20px]"},de={class:"mb-0"},ue={class:"flex justify-between mb-4"},ce={class:"search-box flex items-center"},me=D(()=>r("div",{class:"whitespace-nowrap text-[14px] leading-[18px] font-medium mr-[10px]"}," 角色名称 ",-1)),pe={class:"rounded-xl h-[calc(100vh-132px)] bg-white"},fe={class:"dialog-footer"},ve={class:"text-center p-4"},_e=D(()=>r("p",{class:"mb-4"},"确定要删除该角色吗？删除后不可恢复。",-1)),ge={class:"dialog-footer"},xe=X({__name:"role",setup(g){const A=ae(()=>G(()=>import("./CustomPagination-DJkAmntm.js"),__vite__mapDeps([0,1,2,3,4,5]))),E=O(),y=Q(),d=s([]),p=s(""),v=s(1),x=s(10),F=s([10,25,50]),V=s(0),w=s(!1),C=s(!1),u=s({id:"",name:"",description:"",updateTime:""}),I=s(null),h=s(!1),L=s(""),T={name:[{required:!0,message:"请输入角色名称",trigger:"blur"},{min:2,max:20,message:"长度在 2 到 20 个字符",trigger:"blur"}],description:[{max:100,message:"描述不能超过100个字符",trigger:"blur"}]},S=async()=>{var e,i;const a=ne.service({lock:!0,text:"加载角色数据中...",background:"rgba(255, 255, 255, 0.7)"});try{y.userInfo||await y.fetchUserInfo(null);const n=(i=(e=y.userInfo)==null?void 0:e.tenant)==null?void 0:i.id;if(!n){f.error("获取租户信息失败");return}const b={page:v.value,limit:x.value};p.value&&(b.name=p.value);const m=await H(n,b);console.log("角色列表API响应:",m),m&&m.data?(d.value=M(m.data.data||[]),V.value=m.data.total_num||0):f.warning("未获取到角色数据")}catch(n){console.error("获取角色数据失败:",n),f.error("获取角色数据失败")}finally{a.close()}},M=a=>!a||!Array.isArray(a)?[]:a.map(e=>({id:e.id.toString(),name:e.name||"",description:e.description||"",updateTime:"-",type:e.type||""})),N=async()=>{await S()},B=async({page:a,pageSize:e})=>{v.value=a,x.value=e,await S()},$=Y(()=>d.value),k=()=>{const a=d.value.filter(e=>p.value?e.name.toLowerCase().includes(p.value.toLowerCase()):!0);V.value=a.length},z=async()=>{v.value=1,await S()},K=()=>{const a=d.value.findIndex(e=>e.id===L.value);a!==-1&&(d.value.splice(a,1),f.success("删除成功"),k()),h.value=!1},j=()=>{var a;(a=I.value)==null||a.validate(e=>{if(e){if(C.value){const i=d.value.findIndex(n=>n.id===u.value.id);i!==-1&&(d.value[i]={...u.value,updateTime:new Date().toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-")},f.success("编辑成功"))}else{const i={...u.value,id:String(d.value.length+1),updateTime:new Date().toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-")};d.value.push(i),f.success("添加成功")}w.value=!1,k()}})};return Z(async()=>{E.setActiveMenu("role"),await N()}),(a,e)=>{const i=c("el-input"),n=c("el-button"),b=c("el-table-column"),m=c("el-table"),P=c("el-form-item"),q=c("el-form"),R=c("el-dialog"),J=c("el-icon");return te(),ee(le,null,[r("div",re,[r("div",de,[r("div",ue,[r("div",ce,[me,t(i,{modelValue:p.value,"onUpdate:modelValue":e[0]||(e[0]=o=>p.value=o),placeholder:"请输入角色名称",class:"mr-[10px] w-[240px] role-input",clearable:"",onClear:z},null,8,["modelValue"]),t(n,{type:"primary",onClick:z,class:"filter-btn rounded-md h-8 min-w-[60px] px-4 text-sm"},{default:l(()=>[_("筛选")]),_:1})])]),r("div",pe,[t(m,{data:$.value,style:{width:"100%"},"row-key":"id"},{default:l(()=>[t(b,{prop:"name",label:"角色名称","min-width":"150"})]),_:1},8,["data"])]),t(U(A),{currentPage:v.value,"onUpdate:currentPage":e[1]||(e[1]=o=>v.value=o),pageSize:x.value,"onUpdate:pageSize":e[2]||(e[2]=o=>x.value=o),total:V.value,pageSizes:F.value,onChange:B},null,8,["currentPage","pageSize","total","pageSizes"])])]),t(R,{modelValue:w.value,"onUpdate:modelValue":e[6]||(e[6]=o=>w.value=o),title:C.value?"编辑角色":"新增角色",width:"500px","destroy-on-close":""},{footer:l(()=>[r("div",fe,[t(n,{onClick:e[5]||(e[5]=o=>w.value=!1)},{default:l(()=>[_("取消")]),_:1}),t(n,{type:"primary",onClick:j},{default:l(()=>[_("确认")]),_:1})])]),default:l(()=>[t(q,{model:u.value,"label-width":"80px",rules:T,ref_key:"roleFormRef",ref:I},{default:l(()=>[t(P,{label:"角色名称",prop:"name"},{default:l(()=>[t(i,{modelValue:u.value.name,"onUpdate:modelValue":e[3]||(e[3]=o=>u.value.name=o),placeholder:"请输入角色名称"},null,8,["modelValue"])]),_:1}),t(P,{label:"角色描述",prop:"description"},{default:l(()=>[t(i,{modelValue:u.value.description,"onUpdate:modelValue":e[4]||(e[4]=o=>u.value.description=o),type:"textarea",rows:3,placeholder:"请输入角色描述"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"]),t(R,{modelValue:h.value,"onUpdate:modelValue":e[8]||(e[8]=o=>h.value=o),title:"删除角色",width:"400px"},{footer:l(()=>[r("div",ge,[t(n,{onClick:e[7]||(e[7]=o=>h.value=!1)},{default:l(()=>[_("取消")]),_:1}),t(n,{type:"danger",onClick:K},{default:l(()=>[_("确认删除")]),_:1})])]),default:l(()=>[r("div",ve,[t(J,{class:"text-warning text-xl mb-4"},{default:l(()=>[t(U(oe))]),_:1}),_e])]),_:1},8,["modelValue"])],64)}}}),be=W(xe,[["__scopeId","data-v-67706baa"]]);export{be as default};
