import{_ as s}from"./index-oCQpNzhc.js";import{d as a,A as l,o as r,m as t,b as o,n as c}from"./pnpm-pnpm-B4aX-tnA.js";const n=o("svg",{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},[o("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M5 11.9961C5 11.4438 5.44772 10.9961 6 10.9961H18C18.5523 10.9961 19 11.4438 19 11.9961C19 12.5484 18.5523 12.9961 18 12.9961H6C5.44772 12.9961 5 12.5484 5 11.9961Z"}),o("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M12.7071 5.29289C13.0976 5.68342 13.0976 6.31658 12.7071 6.70711L7.41421 12L12.7071 17.2929C13.0976 17.6834 13.0976 18.3166 12.7071 18.7071C12.3166 19.0976 11.6834 19.0976 11.2929 18.7071L5.29289 12.7071C4.90237 12.3166 4.90237 11.6834 5.29289 11.2929L11.2929 5.29289C11.6834 4.90237 12.3166 4.90237 12.7071 5.29289Z"})],-1),d={name:"BackArrowIcon"},f=a({...d,props:{color:{default:"#121619"},size:{default:24},className:{default:""}},setup(i){return(e,p)=>(r(),l(s,{color:e.color,size:e.size,class:c(e.className)},{default:t(()=>[n]),_:1},8,["color","size","class"]))}});export{f as _};
