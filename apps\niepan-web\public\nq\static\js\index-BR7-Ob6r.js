import{d as pe,c as f,o as u,n as J,b as s,t as g,w as Q,A as M,a as C,u as Re,r as i,N as oe,aC as le,aM as ne,aN as $e,z as Ae,R as H,P as Te,e as A,f as p,m as k,p as Y,aa as L,a1 as re,J as j,K as F,aO as Be,_ as Ke,k as ie,v as de,E as c,x as De,y as Le,aP as Me}from"./pnpm-pnpm-B4aX-tnA.js";import{_ as Ve}from"./DeleteIcon.vue_vue_type_script_setup_true_lang-TAzPfaAy.js";import{M as Ee,d as Ne,B as Ie,v as Oe,N as je,O as ce,P as ue,Q as ve,R as Fe,T as qe,U as Ue,a as We}from"./index-oCQpNzhc.js";const He={class:"flex items-start space-x-4"},Ye={class:"w-[46px] h-[46px] rounded-full flex items-center justify-center text-white text-xl font-medium",style:{background:`linear-gradient(
                        270deg,
                        #ffcd02 0%,
                        #ffb101 100%
                    )`}},Je={class:"flex-1 min-w-0 !ml-[14px]"},Qe={class:"text-[14px] mb-3 leading-[18px] font-semibold text-gray-900 truncate"},Ge={class:"mt-1 text-[12px] leading-[16px] text-[#A2A9B0]"},Xe={class:"mt-4"},Ze={class:"text-xs leading-[16px] text-gray-600 line-clamp-5"},et={class:"absolute bottom-6 left-6"},tt={class:"flex items-center space-x-2"},st={class:"text-xs text-gray-500"},at=s("span",{class:"text-gray-300"},"|",-1),ot={class:"text-xs text-gray-500"},lt=s("span",{class:"text-gray-300"},"|",-1),nt={class:"text-xs text-gray-500"},rt={class:"relative top-[2px] w-[24px] h-[24px] p-[3px] flex items-center justify-center rounded-[6px] hover:bg-[#F5F5F5] transition-colors duration-200"},it={name:"KnowledgeCard"},dt=pe({...it,props:{knowledge:{},canPerformAction:{type:Boolean}},emits:["edit","delete","cardClick"],setup(T){const b={general:"通用型",specialized:"专用型",encrypted:"加密型"},v=o=>b[o]||"未知",m=o=>({general:"bg-blue-100 text-blue-600",specialized:"bg-green-100 text-green-600",encrypted:"bg-purple-100 text-purple-600"})[o]||"bg-gray-100 text-gray-600";return(o,h)=>{var V,n;return u(),f("div",{class:J(["bg-white rounded-[20px] h-[224px] w-full min-w-[360px] p-[20px] relative group cursor-pointer transition-all duration-300 shadow-[0_2px_6px_0_rgba(0,0,0,0.04)]",{"hover:shadow-[0_4px_12px_0_rgba(0,0,0,0.08)]":o.knowledge.is_edit&&o.canPerformAction}]),onClick:h[2]||(h[2]=d=>o.knowledge.is_edit&&o.canPerformAction?o.$emit("cardClick",o.knowledge):null)},[s("div",He,[s("div",Ye,g((V=o.knowledge.title)==null?void 0:V.charAt(0)),1),s("div",Je,[s("div",Qe,g(o.knowledge.title),1),s("p",Ge," 创建者："+g(((n=o.knowledge.author)==null?void 0:n.name)||"-"),1)]),s("div",{class:J([m(o.knowledge.type),"px-3 py-1 rounded-[6px] text-sm !hidden"])},g(v(o.knowledge.type)),3)]),s("div",Xe,[s("p",Ze,g(o.knowledge.description||"这个知识库还没有介绍～"),1)]),s("div",et,[s("div",tt,[s("span",st,g(o.knowledge.document_count||0)+"文档",1),at,s("span",ot,g(o.knowledge.document_total_chars||0)+"字符",1),lt,s("span",nt,g(o.knowledge.query_app_count||0)+"关联应用",1)])]),s("div",{class:"absolute bottom-6 right-6",onClick:h[1]||(h[1]=Q(()=>{},["stop"]))},[s("div",rt,[o.knowledge.is_edit&&o.canPerformAction?(u(),M(Ve,{key:0,size:"18",color:"#A2A9B0",class:"relative top-[-1px]",onClick:h[0]||(h[0]=Q(d=>o.$emit("delete",o.knowledge),["stop"]))})):C("",!0)])])],2)}}}),ct={general:"通用型",specialized:"专用型",encrypted:"加密型"},x=T=>(De("data-v-48c32bbd"),T=T(),Le(),T),ut={class:"h-full overflow-hidden"},vt={class:"flex justify-between items-center mb-6"},pt=x(()=>s("h2",{class:"text-2xl font-bold text-gray-800"}," ",-1)),mt={key:0,class:"col-span-full flex flex-col items-center justify-center py-20"},ft={class:"text-gray-400 text-6xl mb-4"},gt=x(()=>s("p",{class:"text-gray-500 text-lg mb-6"},"暂无知识库数据",-1)),_t={class:"col-span-full"},ht={key:0,class:"flex justify-center items-center py-4 text-gray-500"},bt=x(()=>s("span",{class:"ml-2 text-sm"},"加载中...",-1)),yt={key:1,class:"flex justify-center py-4 text-gray-500 text-sm"},xt={class:"mt-6"},wt={key:0,class:"new-modal-overlay"},kt={class:"new-modal-container"},Ct={class:"new-modal-top"},Pt={class:"font18 font-zhongcu"},St={class:"new-modal-center"},zt={class:"new-modal-center-item"},Rt=x(()=>s("label",{class:"font16 font-zhongcu"},"名称",-1)),$t={class:"new-modal-center-item"},At=x(()=>s("label",{class:"font16 font-zhongcu"},"知识库描述",-1)),Tt={class:"new-modal-center-item"},Bt=x(()=>s("label",{class:"font16 font-zhongcu"},"数据模型",-1)),Kt={class:"new-modal-center-item !hidden"},Dt=x(()=>s("label",{class:"font16 font-zhongcu"},"知识库类型",-1)),Lt={key:0,class:"new-modal-center-item"},Mt=x(()=>s("label",{class:"font16 font-zhongcu"},"使用权限",-1)),Vt={class:"field-ipt field-siteuser"},Et={class:"siteuser-div"},Nt={class:"siteuser-headimg"},It={class:"new-modal-bottom pt-[20px]"},Ot=x(()=>s("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu common-confirm-btn"}," 保存 ",-1)),jt=pe({__name:"index",setup(T){const b=Ee(),v=Re(),m=i(!1),o=Ne(),h=async()=>{var t,e;if(console.log("=== checkUserPermissions called ==="),console.log("Current route:",v.currentRoute.value),console.log("Route params:",v.currentRoute.value.params),console.log("Route path:",v.currentRoute.value.path),console.log("User permissions:",(t=o.userInfo)==null?void 0:t.permissions),v.currentRoute.value.path==="/admin/knowledge"){m.value=ce(((e=o.userInfo)==null?void 0:e.permissions)||[],["create_docbase"]);return}if(!v.currentRoute.value.params.id){console.log("No route id parameter found, skipping API call"),m.value=!1;return}try{console.log("Calling getKBaseDetail with id:",v.currentRoute.value.params.id);const a=await ue(v.currentRoute.value.params.id);console.log("getKBaseDetail response:",a),a.data.error==="0"?(console.log("Updating store with knowledge:",a.data),b.setCurrentKnowledge(a.data),m.value=a.data.is_edit):a.data.error==="1"?(console.log("Knowledge base does not exist"),c.error("知识库不存在"),m.value=!1):a.data.error==="2"||a.data.error==="403"?(console.log("No permission to view"),c.error("无权限查看"),m.value=!1):(console.log("Other error:",a.data.message),c.error(a.data.message||"获取知识库信息失败"),m.value=!1)}catch(a){console.error("获取知识库信息失败，详细错误:",a),c.error("获取知识库信息失败"),m.value=!1}};oe([()=>o.userInfo,()=>v.currentRoute.value.path],([t,e])=>{console.log("=== Watch triggered ==="),console.log("New user info:",t),console.log("New path:",e),e!=null&&e.startsWith("/admin/knowledge/detail/")?t&&h():m.value=ce((t==null?void 0:t.permissions)||[],["create_docbase"])},{immediate:!0});const V=i([{name:"知识库管理"},{name:"知识库"}]),n=i(null),d=i([]),R=i(1),q=i(20),P=i(!1),E=i(!1),S=i(!1),w=i(!1),G=i(),B=i([]),K=i([]),U=i(!1),N=i(!1),me=i("永久删除知识库"),fe=i("该知识库数据将被永久删除，不可恢复及撤销。确定要删除吗？"),$=i(null),ge=[{value:"text-embedding-v3",label:"text-embedding-v3"}],_e=Object.entries(ct).map(([t,e])=>({value:t,label:e})),r=i({name:"",description:"",type:"text-embedding-v3",kbase_type:"general"});i({name:[{required:!0,message:"请输入知识库名称",trigger:"blur"},{min:2,max:50,message:"长度在 2 到 50 个字符",trigger:"blur"}],description:[{max:200,message:"长度不能超过 200 个字符",trigger:"blur"}],type:[{required:!0,message:"请选择知识库类型",trigger:"change"}]});const X=le(()=>{if(n.value&&v.currentRoute.value.path==="/admin/knowledge"){const{scrollTop:t}=n.value;b.setKnowledgeListScrollPosition(t)}},100),Z=i(1),he=()=>{if(!n.value)return;const t=n.value.clientWidth,e=Math.floor(t/360);e!==Z.value&&(Z.value=e)},be=()=>{if(!n.value)return;const t=new ResizeObserver(ne(()=>{he()},200));return t.observe(n.value),()=>t.disconnect()},W=le(t=>{if(!n.value||v.currentRoute.value.path!=="/admin/knowledge")return;const{scrollTop:e}=n.value;P.value||b.setKnowledgeListScrollPosition(e)},100),ee=ne(async()=>{if(!n.value||P.value||E.value)return;const{scrollTop:t,scrollHeight:e,clientHeight:a}=n.value;e-t-a<100&&(P.value=!0,await I(!0))},200,{leading:!0,trailing:!0}),te=()=>{n.value&&(n.value.addEventListener("scroll",W,{passive:!0}),n.value.addEventListener("scroll",ee,{passive:!0}))},se=()=>{n.value&&(n.value.removeEventListener("scroll",W),n.value.removeEventListener("scroll",ee))};$e(()=>{if(v.currentRoute.value.path==="/admin/knowledge"){te();const t=be();setTimeout(()=>{H(async()=>{if(n.value&&b.knowledgeListScrollPosition>0){await new Promise(a=>setTimeout(a,300));const e=(a=0)=>{a>=3||(n.value.scrollTop=b.knowledgeListScrollPosition,Math.abs(n.value.scrollTop-b.knowledgeListScrollPosition)>10&&setTimeout(()=>e(a+1),100))};e()}})},100),Me(()=>{t==null||t(),se(),X()})}}),oe(()=>v.currentRoute.value.path,(t,e)=>{e==="/admin/knowledge"&&(console.log("路由变化，保存位置"),X())},{immediate:!1});const I=async(t=!1)=>{var e,a;t||(P.value=!0);try{const y=await ve(R.value,q.value),_=y.data.data||[],O=((e=y.data.total_num)==null?void 0:e[0])||0,D=new Set(d.value.map(l=>l.id)),z=_.filter(l=>!D.has(l.id));if(t){if(z.length===0){E.value=!0;return}d.value.push(...z)}else d.value=_;E.value=R.value>=(((a=y.data.num_pages)==null?void 0:a[0])||1),z.length>0&&R.value++,H(()=>{setTimeout(()=>{n.value&&W(null)},300)})}catch(y){console.error("加载数据失败:",y),c.error("加载数据失败")}finally{setTimeout(()=>{P.value=!1},200)}},ye=()=>{w.value=!1,r.value={name:"",description:"",type:"text-embedding-v3",kbase_type:"general"},B.value=[],K.value=[],S.value=!0},xe=async t=>{w.value=!0;try{const e=await ue(t.id);e.data.error=="0"&&(r.value={id:t.id,name:e.data.title||"",description:e.data.description||"",type:e.data.embedding_type||"text-embedding-v3",kbase_type:e.data.type||"general"},B.value=e.data.siteusers.map(a=>({id:a.id,name:a.name,nameOne:a.name.split("")[0]})),K.value=e.data.siteusers.map(a=>a.id))}catch(e){console.error("获取知识库详情失败:",e),c.error("获取知识库详情失败")}S.value=!0},ae=()=>{U.value=!U.value},we=t=>{B.value=t.map(e=>({...e,nameOne:e.name.split("")[0]})),K.value=t.map(e=>e.id)},ke=t=>{N.value=!0,$.value=t.id},Ce=()=>{N.value=!1,$.value=null},Pe=async()=>{var t,e;N.value=!1;try{const a=await Ue($.value);if(a.data.error=="0"){c.success("删除成功");const y=d.value.findIndex(_=>_.id===$.value);if(d.value=d.value.filter(_=>_.id!==$.value),d.value.length===0&&R.value>1)R.value--,await I();else if(y!==-1&&d.value.length<q.value){const _=await ve(Math.floor(d.value.length/q.value)+1,1);if(((e=(t=_.data)==null?void 0:t.data)==null?void 0:e.length)>0){const O=_.data.data.filter(D=>!d.value.some(z=>z.id===D.id));d.value.push(...O)}}}else c.error(a.data.message||"删除失败")}catch(a){console.error("删除失败:",a),c.error("删除失败")}finally{$.value=null}},Se=async()=>{if(G.value)try{if(w.value){if(!r.value.id){c.error("知识库ID不能为空");return}const t=await Fe(r.value.name,r.value.description,r.value.id,K.value.join("|"),r.value.kbase_type);if(t.data.error=="0"){c.success("设置成功");const e=d.value.findIndex(a=>a.id===r.value.id);e!==-1&&(d.value[e]={...d.value[e],title:r.value.name,description:r.value.description,type:r.value.kbase_type,siteusers:B.value}),S.value=!1}else t.data.error=="403"?c.error("暂无权限"):t.data.message&&c.error(t.data.message)}else{const t=await qe(r.value.name,r.value.description,r.value.type,r.value.kbase_type);t.data.error=="0"?(c.success("创建成功"),S.value=!1,R.value=1,I()):t.data.error=="403"?c.error("暂无权限"):t.data.message&&c.error(t.data.message)}}catch(t){console.error(w.value?"编辑失败:":"创建失败:",t),c.error(w.value?"编辑失败":"创建失败")}finally{S.value=!1}},ze=t=>{b.setCurrentKnowledge(t),v.push(`/admin/knowledge/detail/${t.id}/docs`)};return Ae(()=>{h(),I(),H(()=>{te()})}),Te(()=>{se()}),(t,e)=>{const a=A("el-icon"),y=A("el-button"),_=A("el-option"),O=A("el-select"),D=A("el-radio"),z=A("el-radio-group");return u(),f("div",ut,[s("div",vt,[pt,m.value?(u(),M(y,{key:0,type:"primary",onClick:ye,class:"flex items-center !rounded-md"},{default:k(()=>[p(a,{class:"mr-1"},{default:k(()=>[p(L(re))]),_:1}),Y(" 创建知识库 ")]),_:1})):C("",!0)]),s("div",{ref_key:"containerRef",ref:n,class:J(["grid auto-rows-[224px] gap-6 overflow-y-auto h-[calc(100vh-272px)] custom-scrollbar p-2",d.value.length<=1?"grid-cols-[repeat(auto-fit,minmax(360px,0.5fr))]":"grid-cols-[repeat(auto-fit,minmax(360px,1fr))]"])},[(u(!0),f(j,null,F(d.value,l=>(u(),M(dt,{key:l.id,knowledge:l,canPerformAction:m.value,onEdit:xe,onDelete:ke,onCardClick:ze},null,8,["knowledge","canPerformAction"]))),128)),!P.value&&d.value.length===0?(u(),f("div",mt,[s("div",ft,[p(a,null,{default:k(()=>[p(L(Be))]),_:1})]),gt])):C("",!0),s("div",_t,[P.value?(u(),f("div",ht,[p(a,{class:"is-loading"},{default:k(()=>[p(L(Ke))]),_:1}),bt])):C("",!0),E.value&&d.value.length>0?(u(),f("div",yt," 没有更多数据了 ")):C("",!0)])],2),s("div",xt,[p(Ie,{breadcrumbs:V.value},null,8,["breadcrumbs"])]),p(Oe,{show:N.value,title:me.value,message:fe.value,onClose:Ce,onConfirm:Pe},null,8,["show","title","message"]),S.value?(u(),f("div",wt,[s("div",kt,[s("div",Ct,[s("div",Pt,g(w.value?"编辑知识库":"创建知识库"),1)]),s("form",{onSubmit:Q(Se,["prevent"]),ref_key:"formRef",ref:G},[s("div",St,[s("div",zt,[Rt,ie(s("input",{id:"title",class:"field-ipt font14 font-zhongcu","onUpdate:modelValue":e[0]||(e[0]=l=>r.value.name=l),placeholder:"输入知识库名称",required:"",maxlength:"50"},null,512),[[de,r.value.name]])]),s("div",$t,[At,ie(s("textarea",{id:"description",class:"field-ipt field-txt font14 font-zhongcu leading-normal align-text-top p-2 resize-none","onUpdate:modelValue":e[1]||(e[1]=l=>r.value.description=l),placeholder:"描述知识库涵盖的知识范围，适用的业务场景，及具备的特征等，详尽的描述可以帮助AI理解和利用知识",required:"",maxlength:"500",style:{appearance:"none","-webkit-appearance":"none","line-height":"1.5","vertical-align":"top"}},null,512),[[de,r.value.description]])]),s("div",Tt,[Bt,p(O,{modelValue:r.value.type,"onUpdate:modelValue":e[2]||(e[2]=l=>r.value.type=l),placeholder:"选择向量模型",size:"large",class:"font14 font-zhongcu field-select w-full focus:border-[rgba(18,155,254,1)] focus:outline-none",disabled:w.value},{default:k(()=>[(u(),f(j,null,F(ge,l=>p(_,{key:l.value,label:l.label,value:l.value},null,8,["label","value"])),64))]),_:1},8,["modelValue","disabled"])]),s("div",Kt,[Dt,p(z,{modelValue:r.value.kbase_type,"onUpdate:modelValue":e[3]||(e[3]=l=>r.value.kbase_type=l),class:"mt-2"},{default:k(()=>[(u(!0),f(j,null,F(L(_e),l=>(u(),M(D,{key:l.value,label:l.value},{default:k(()=>[Y(g(l.label),1)]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"])]),w.value?(u(),f("div",Lt,[Mt,s("div",Vt,[s("div",Et,[(u(!0),f(j,null,F(B.value.slice(0,4),l=>(u(),f("div",{class:"siteuser-div-item font12 font-zhongcu",key:l.id},[s("div",Nt,g(l.nameOne),1),Y(" "+g(l.name),1)]))),128))]),p(a,{size:"20",class:"siteuser-add cursor-pointer",onClick:ae},{default:k(()=>[p(L(re))]),_:1})])])):C("",!0)]),s("div",It,[s("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:e[4]||(e[4]=l=>S.value=!1)}," 取消 "),Ot])],544)])])):C("",!0),U.value?(u(),M(je,{key:1,type:"kbase",sids:K.value,onValueFromChild:we,closeModal:ae},null,8,["sids"])):C("",!0)])}}}),Wt=We(jt,[["__scopeId","data-v-48c32bbd"]]);export{Wt as default};
