import{d as I,A as ne,o as S,m as u,b as t,n as B,a8 as U,a9 as re,r as s,e as v,c as F,f as a,aa as m,p as w,E as n,x as ce,y as de,z as ie,s as ue,k as pe,a as ve,aK as me,w as E,aL as _e,aE as fe,L as ge,Q as P}from"./pnpm-pnpm-B4aX-tnA.js";import{_ as he,z as M,A as be,a as T,B as Ce,C as we,D as xe,E as ye,F as Me}from"./index-oCQpNzhc.js";import{_ as Z}from"./DeleteIcon.vue_vue_type_script_setup_true_lang-TAzPfaAy.js";const Le=t("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 18 18"},[t("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M1.35587 5.25033C2.5577 2.94476 5.05663 1.6189 7.63958 1.9164C10.0258 2.19123 12.0272 3.79899 12.8277 6.02796L13.1441 6.02796C13.1443 6.02796 13.1445 6.02796 13.1446 6.02796C13.1447 6.02796 13.1446 6.02796 13.1446 6.02796C14.8917 6.02682 16.6373 6.95018 17.215 8.7735C17.7688 10.5217 17.1256 12.4272 15.6255 13.4821C15.2867 13.7203 14.8189 13.6388 14.5806 13.3C14.3424 12.9611 14.4239 12.4933 14.7627 12.2551C15.7276 11.5766 16.1413 10.351 15.785 9.22654C15.4526 8.17742 14.4114 7.52698 13.1453 7.52796L12.2743 7.52796C11.9319 7.52796 11.633 7.29604 11.5479 6.96437C11.0561 5.04786 9.43356 3.63294 7.46795 3.40655C5.5023 3.18015 3.60061 4.18914 2.68601 5.94369C1.77141 7.69824 2.0333 9.83505 3.34454 11.3168C3.61904 11.627 3.59011 12.101 3.27991 12.3755C2.96971 12.65 2.49572 12.6211 2.22122 12.3109C0.498187 10.3638 0.154048 7.5559 1.35587 5.25033Z"}),t("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M8.99965 7.875C9.41386 7.87481 9.74981 8.21044 9.75 8.62465L9.75315 15.3747C9.75334 15.7889 9.41771 16.1248 9.0035 16.125C8.58929 16.1252 8.25334 15.7896 8.25315 15.3753L8.25 8.62535C8.24981 8.21114 8.58544 7.87519 8.99965 7.875Z"}),t("path",{"fill-rule":"evenodd","clip-rule":"evenodd",d:"M6.08295 12.458C6.37584 12.1651 6.85072 12.1651 7.14361 12.458L8.99978 14.3141L10.856 12.458C11.1488 12.1651 11.6237 12.1651 11.9166 12.458C12.2095 12.7508 12.2095 13.2257 11.9166 13.5186L9.53011 15.9051C9.23722 16.198 8.76234 16.198 8.46945 15.9051L6.08295 13.5186C5.79006 13.2257 5.79006 12.7508 6.08295 12.458Z"})],-1),ze={name:"DownloadAppReModelIcon"},ke=I({...ze,props:{color:{default:"white"},size:{default:18},className:{default:""}},setup(_){return(h,c)=>(S(),ne(he,{color:h.color,size:h.size,class:B(h.className)},{default:u(()=>[Le]),_:1},8,["color","size","class"]))}}),Ae=_=>(ce("data-v-e71a9941"),_=_(),de(),_),Se={class:"fixed inset-0 bg-white-50 z-10 backdrop-blur-[29px]"},De={class:"w-[452px] h-[57vh] bg-white rounded-3xl p-6 absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 shadow-[0px_10px_44px_1px_rgba(0,_0,_0,_0.10)] flex justify-between flex-col"},Ve=Ae(()=>t("div",{class:"text-lg font-semibold mb-4"},"获取模型",-1)),$e={class:"flex justify-end dialog-footer mt-9"},Ee=I({__name:"downLoadMcModel",props:U({mcModelList:{}},{dialogVisibleDownLoad:{default:!1},dialogVisibleDownLoadModifiers:{}}),emits:U(["create-success"],["update:dialogVisibleDownLoad"]),setup(_,{emit:h}){const c=re(_,"dialogVisibleDownLoad"),d=h,L=_,i=s([]),z=M(async()=>{if(console.log("submitForm"),console.log(i.value,"selectedMcModelList"),i.value.length>0){c.value=!1;const f=await be(i.value);f.data.error=="0"?(n.success("下载完成"),d("create-success"),console.log("已发送 create-success 事件")):n.error(f.data.msg)}else n.error("请选择模型")},1e3),b=(f,o)=>!0,D=M(f=>{i.value=f.map(o=>({name:o.model_name,type:o.type,parameters:o.parameters}))},1e3);return(f,o)=>{const p=v("el-table-column"),g=v("el-table"),k=v("el-button");return S(),F("div",Se,[t("div",De,[Ve,a(g,{data:L.mcModelList,height:"100%",style:{width:"100%"},onSelectionChange:m(D)},{default:u(()=>[a(p,{type:"selection",selectable:b,width:"55"}),a(p,{prop:"model_name",label:"模型名称"}),a(p,{prop:"parameters",label:"参数规模(B)"})]),_:1},8,["data","onSelectionChange"]),t("div",$e,[a(k,{size:"large",onClick:o[0]||(o[0]=R=>c.value=!1)},{default:u(()=>[w("取消")]),_:1}),a(k,{size:"large",type:"primary",onClick:m(z)},{default:u(()=>[w("确定")]),_:1},8,["onClick"])])])])}}}),Be=T(Ee,[["__scopeId","data-v-e71a9941"]]),Fe={class:"h-full overflow-hidden"},Ie={class:"flex items-center justify-between mb-6"},Re=["disabled"],Ne={key:0,class:"absolute top-[34px] left-0 w-full bg-white rounded-[6px] z-10 text-[#343A3F] overflow-hidden shadow-[0px_10px_44px_1px_rgba(0,_0,_0,_0.10)]"},je={class:"rounded-[12px] border border-[#E5E5E5] bg-[#FFF] overflow-hidden h-[calc(100vh-272px)] overflow-y-auto custom-scrollbar"},Ue={class:"flex items-center justify-end my-3 px-5 py-3"},Pe={class:"mt-6"},Ze=I({__name:"index",setup(_){const h=s([{name:"智能体管理"},{name:"模型管理"}]),c=s(1),d=s(10),L=s(0),i=s(null),z=s([]),b=s(!1),D=s("永久删除模型"),f=s("该模型将被永久删除，不可恢复及撤销。确定要删除吗？"),o=s([]),p=s(!1),g=async(e,l)=>{i.value=P.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});try{const x=await we(e,l);i.value.close(),z.value=x.data.data,L.value=x.data.total_num[0]}finally{i.value.close()}};ie(()=>{g(c.value,d.value)});const k=e=>{c.value=e,g(c.value,d.value)},R=e=>{d.value=e,g(c.value,d.value)},H=ue(()=>o.value.length===0),K=()=>{H.value||(C.value=!C.value)},N=async e=>{try{const l=await Me(o.value,e);l.data.error=="0"?(n.success("修改成功"),g(c.value,d.value)):l.data.message?n.error(l.data.message):n.error("修改失败")}catch(l){console.error("修改失败",l),n.error("修改失败")}},O=M(async e=>{try{o.value=[e.id],await N(e.status)}catch(l){console.error("修改失败",l),n.error("修改失败")}}),C=s(!1),j=M(async e=>{if(C.value=!1,o.value.length>0)try{await N(e)}catch(l){console.error("修改失败",l),n.error("修改失败")}else n.error("请选择模型")}),Q=()=>{b.value=!1,o.value=[]},q=M(async()=>{b.value=!1;try{const e=await ye(o.value);e.data.error=="0"?(n.success("删除成功"),g(c.value,d.value)):e.data.message?n.error(e.data.message):n.error("删除失败")}catch(e){console.error("删除失败",e),n.error("删除失败")}}),G=async e=>{b.value=!0,o.value=[e.id]},J=async()=>{C.value=!1,o.value.length>0?b.value=!0:n.error("请选择模型")},W=(e,l)=>!0,X=e=>{o.value=e.map(l=>l.id),o.value.length>0?p.value||(p.value=!0):p.value=!1},A=s(!1),Y=()=>{g(1,d.value)},V=s([]),ee=async()=>{V.value=[],i.value=P.service({lock:!0,text:"加载中",background:"rgba(0, 0, 0, 0.5)"});try{const e=await xe();i.value.close(),V.value=e.data.data}catch(e){console.error("获取模型列表失败",e)}finally{i.value.close()}};return(e,l)=>{const x=v("el-icon"),y=v("el-table-column"),ae=v("el-switch"),le=v("el-button"),oe=v("el-table"),te=v("el-pagination"),se=v("v2ConfirmsModal");return S(),F("div",Fe,[t("div",Ie,[t("div",{class:B(["rounded-[6px] pt-[5px] pb-[5px] pl-3 pr-4 text-sm flex items-center leading-[22px] font-medium relative caozuo-div",{"caozuo-active":C.value,"cursor-not-allowed caozuo-disabled":!p.value,"cursor-pointer":p.value}]),onClick:K,disabled:p.value},[w(" 批量操作 "),a(x,{class:B(["ml-2 caozuo-up-icon",{"rotate-180":!C.value}]),color:"#343A3F",size:"18"},{default:u(()=>[a(m(me))]),_:1},8,["class"]),C.value?(S(),F("div",Ne,[t("button",{class:"w-full h-full bg-none border-0 py-2 px-3 bg-white flex items-center hover-button",onClick:l[0]||(l[0]=E(r=>m(j)(1),["stop"]))},[a(x,{size:16,color:"#343A3F",class:"mr-2"},{default:u(()=>[a(m(_e))]),_:1}),w("启用")]),t("button",{class:"w-full h-full bg-none border-0 py-2 px-3 bg-white flex items-center hover-button",onClick:l[1]||(l[1]=E(r=>m(j)(0),["stop"]))},[a(x,{size:16,color:"#343A3F",class:"mr-2"},{default:u(()=>[a(m(fe))]),_:1}),w("禁用")]),t("button",{class:"w-full h-full bg-none border-0 py-2 px-3 bg-white flex items-center hover-button",style:{"border-top":"1px solid #E5E5E5"},onClick:E(J,["stop"])},[a(Z,{size:"16",class:"mr-2"}),w("删除")])])):ve("",!0)],10,Re),t("button",{class:"bg-[#129BFE] rounded-[6px] pt-[5px] pb-[5px] pl-3 pr-4 text-white text-sm border-0 flex items-center leading-[22px] font-medium",onClick:l[2]||(l[2]=r=>{A.value=!0,ee()})},[a(ke,{class:"mr-[2px]"}),w(" 获取模型 ")])]),t("div",je,[a(oe,{data:z.value,style:{width:"100%"},"header-row-class-name":"highlight-header",class:"rounded-lg",onSelectionChange:X},{default:u(()=>[a(y,{type:"selection",selectable:W,width:"55"}),a(y,{prop:"model_name",label:"模型名称"}),a(y,{prop:"model_name",label:"启用状态"},{default:u(({row:r})=>[a(ae,{modelValue:r.status,"onUpdate:modelValue":$=>r.status=$,"active-value":1,"inactive-value":0,onChange:$=>m(O)(r)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),a(y,{prop:"parameters",label:"参数规模(B)"}),a(y,{prop:"update_time",label:"更新时间"}),a(y,{label:"操作",width:"100"},{default:u(({row:r})=>[a(le,{link:"",type:"danger",onClick:$=>G(r)},{default:u(()=>[a(Z,{size:"16"})]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data"]),t("div",Ue,[a(te,{"current-page":c.value,"onUpdate:currentPage":l[3]||(l[3]=r=>c.value=r),"page-size":d.value,"onUpdate:pageSize":l[4]||(l[4]=r=>d.value=r),"page-sizes":[2,15,30,50,100],total:L.value,onSizeChange:R,onCurrentChange:k,layout:"total, sizes, prev, pager, next, jumper",background:""},null,8,["current-page","page-size","total"])])]),pe(a(Be,{"dialog-visible-down-load":A.value,"onUpdate:dialogVisibleDownLoad":l[5]||(l[5]=r=>A.value=r),onCreateSuccess:Y,"mc-model-list":V.value},null,8,["dialog-visible-down-load","mc-model-list"]),[[ge,A.value]]),t("div",Pe,[a(Ce,{breadcrumbs:h.value},null,8,["breadcrumbs"])]),a(se,{show:b.value,title:D.value,message:f.value,onClose:Q,onConfirm:m(q)},null,8,["show","title","message","onConfirm"])])}}}),Oe=T(Ze,[["__scopeId","data-v-a1a88411"]]);export{Oe as default};
