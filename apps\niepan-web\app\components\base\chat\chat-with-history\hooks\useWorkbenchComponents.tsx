import React, { Suspense, useCallback, useMemo, useState } from 'react';
import type { ReactNode } from 'react';
import {
  ErrorComponent,
  LoadingComponent,
  getComponent,
} from '../components/workbench-components/componentRegistry';
import type { WorkbenchComponentData } from '../components/workbench-components/componentRegistry';
import { useWorkbenchMessageHandler } from './useWorkbenchMessageHandler';
import type { ChatMessage } from './useWorkbenchMessageHandler';

// 导入组件注册初始化
import '../components/workbench-components';

// 错误边界组件
class ComponentErrorBoundary extends React.Component<
  { children: ReactNode; uid: string },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode; uid: string }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error(`Component ${this.props.uid} error:`, error, errorInfo);
  }

  render() {
    if (this.state.hasError) return <ErrorComponent error={`组件 "${this.props.uid}" 渲染失败`} />;

    return this.props.children;
  }
}

export function useWorkbenchComponents() {
  const messageHandler = useWorkbenchMessageHandler();
  // 工作台组件数据状态
  const [componentsData, setComponentsData] = useState<WorkbenchComponentData[]>([
    // 模拟数据 - 实际使用时会从聊天消息中获取
    {
      uid: 'pptx-preview-component',
      area: 'main',
      data: {
        title: 'PPT预览',
        fileUrl:
          'http://192.168.10.10:8012/PLM21x_%E9%A3%8E%E5%9C%BAGoldenBOM%E7%AE%A1%E7%90%86%E7%94%A8%E6%88%B7%E6%93%8D%E4%BD%9C%E6%89%8B%E5%86%8Cpptx.pdf',
        initialPage: 3,
      },
    },
    {
      uid: 'text-detail-component',
      area: 'main',
      data: {
        title: '分析结果',
        content:
          '根据您提供的文档，我发现了以下关键信息：\n\n1. 项目进度符合预期\n2. 风险评估结果良好\n3. 建议继续按计划执行\n\n详细分析报告已生成，请查看附件。',
        type: 'success',
        collapsible: true,
        defaultExpanded: true,
      },
    },
    {
      uid: 'list-component',
      area: 'main',
      data: {
        title: '相关文档列表',
        showSearch: true,
        showStatus: true,
        items: [
          {
            id: 1,
            title: '项目需求文档',
            description: '详细描述了项目的功能需求和技术规范',
            status: 'active',
            metadata: { version: 'v1.2', author: '张三' },
          },
          {
            id: 2,
            title: '技术方案设计',
            description: '系统架构设计和技术选型方案',
            status: 'pending',
            metadata: { version: 'v0.9', author: '李四' },
          },
          {
            id: 3,
            title: '测试计划',
            description: '完整的测试策略和测试用例设计',
            status: 'inactive',
            metadata: { version: 'v1.0', author: '王五' },
          },
        ],
      },
    },
  ]);

  // 更新组件数据
  const updateComponentsData = useCallback((newData: WorkbenchComponentData[]) => {
    setComponentsData(newData);
  }, []);

  // 添加组件
  const addComponent = useCallback((componentData: WorkbenchComponentData) => {
    setComponentsData(prev => {
      // 检查是否已存在相同uid的组件，如果存在则更新，否则添加
      const existingIndex = prev.findIndex(item => item.uid === componentData.uid);
      if (existingIndex >= 0) {
        const newData = [...prev];
        newData[existingIndex] = componentData;
        return newData;
      }
      return [...prev, componentData];
    });
  }, []);

  // 移除组件
  const removeComponent = useCallback((uid: string) => {
    setComponentsData(prev => prev.filter(item => item.uid !== uid));
  }, []);

  // 更新特定组件的数据
  const updateComponentData = useCallback((uid: string, newData: Record<string, any>) => {
    setComponentsData(prev =>
      prev.map(item => (item.uid === uid ? { ...item, data: { ...item.data, ...newData } } : item))
    );
  }, []);

  // 清空所有组件
  const clearComponents = useCallback(() => {
    setComponentsData([]);
  }, []);

  // 根据区域获取组件
  const getComponentsByArea = useCallback(
    (area: string) => {
      return componentsData.filter(item => item.area === area);
    },
    [componentsData]
  );

  // 渲染单个组件
  const renderComponent = useCallback((componentData: WorkbenchComponentData) => {
    const Component = getComponent(componentData.uid);

    return (
      <ComponentErrorBoundary key={componentData.uid} uid={componentData.uid}>
        <Suspense fallback={<LoadingComponent />}>
          <Component data={componentData.data} />
        </Suspense>
      </ComponentErrorBoundary>
    );
  }, []);

  // 渲染指定区域的所有组件
  const renderComponents = useCallback(
    (area: string) => {
      const areaComponents = getComponentsByArea(area);

      if (areaComponents.length === 0) {
        return (
          <div className="flex h-full items-center justify-center">
            <div className="text-gray-400">暂无内容</div>
          </div>
        );
      }

      return (
        <div className="space-y-4">
          {areaComponents.map(componentData => renderComponent(componentData))}
        </div>
      );
    },
    [getComponentsByArea, renderComponent]
  );

  // 获取组件统计信息
  const componentStats = useMemo(() => {
    const areaStats: Record<string, number> = {};
    componentsData.forEach(item => {
      areaStats[item.area] = (areaStats[item.area] || 0) + 1;
    });

    return {
      total: componentsData.length,
      byArea: areaStats,
    };
  }, [componentsData]);

  // 处理聊天消息触发的工作台更新
  const handleChatMessage = useCallback(
    (message: ChatMessage) => {
      const components = messageHandler.parseWorkbenchInstructions(message);

      if (components.length > 0) {
        components.forEach(component => addComponent(component));
      }

      // 处理工作台指令
      if (message.workbench) {
        const { action, componentId, componentData } = message.workbench;

        switch (action) {
          case 'update':
            if (componentId && componentData) {
              updateComponentData(componentId, componentData);
            }
            break;
          case 'remove':
            if (componentId) {
              removeComponent(componentId);
            }
            break;
          case 'clear':
            clearComponents();
            break;
        }
      }
    },
    [messageHandler, addComponent, updateComponentData, removeComponent, clearComponents]
  );

  return {
    // 数据
    componentsData,
    componentStats,

    // 操作方法
    updateComponentsData,
    addComponent,
    removeComponent,
    updateComponentData,
    clearComponents,
    getComponentsByArea,

    // 渲染方法
    renderComponent,
    renderComponents,

    // 消息处理
    handleChatMessage,
    messageHandler,
  };
}
