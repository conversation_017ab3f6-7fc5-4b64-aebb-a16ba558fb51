import{r as a,s as Rs,N as pe,z as Ze,R as ne,P as Os,e as w,c as r,o as i,b as o,A as X,n as E,J as S,K as V,a as x,p as R,t as p,f as u,m as h,aa as Ps,aD as Ks,w as he,M as Us,aT as Qs,aU as Hs,x as Et,y as It,an as js,_ as Ns,X as Ws,S as Js,$ as Ys,a0 as Xs,W as Gs,a1 as Zs,a2 as gt,a3 as bt,a4 as yt,D as kt,Y as wt,a5 as xt,k as Mt,v as Ct,q as $s,u as eo,B as Tt,O as ze,a6 as to,E as Q}from"./pnpm-pnpm-B4aX-tnA.js";import{a as Ft,g as so,B as oo,N as lo,as as ao,at as Vt,ab as no,e as io,au as Dt,av as co,ao as St,O as At,q as ro,aw as uo,ax as mo,ay as vo,az as fo,aA as po,aB as ho,S as _o,aC as go,aD as bo,ar as yo}from"./index-oCQpNzhc.js";import{_ as ko}from"./BackArrowIcon.vue_vue_type_script_setup_true_lang-BntbR4_Q.js";import{_ as wo}from"./EditIcon.vue_vue_type_script_setup_true_lang-CEndjpto.js";const xo=C=>(Et("data-v-b68c3d68"),C=C(),It(),C),Mo={class:"model-selector"},Co=xo(()=>o("div",{class:"text-sm font-medium text-gray-700 mb-2"},[R(" 选择模型 "),o("sup",{class:"text-red-500 relative top-[1px] left-[-2px] text-[14px]"},"*"),o("span",{class:"text-[#A2A9B0] text-xs"},"第一位为默认展示模型，拖动可切换模型位置顺序")],-1)),To={class:"relative"},Do={class:"flex items-center flex-1 overflow-x-auto hide-scrollbar"},So={key:0,class:"text-gray-400 my-2"},Ao=["data-id"],Lo={key:0,class:"default-badge"},Eo=["src"],Io={class:"arrow-icon"},Fo={class:"model-list"},Vo=["onClick"],qo={class:"model-item-content"},Bo=["src"],zo={class:"model-name"},Ro={__name:"ModelSelector",props:{modelValue:{type:Array,default:()=>[]},initialModels:{type:Array,default:()=>[]},debug:{type:Boolean,default:!1}},emits:["update:modelValue","change"],setup(C,{emit:c}){const _=C,s=c,g=a([]),T=a(!1),O=a(null),K=a(null);let b=null;const y=a(!1),A=a({position:"fixed",left:"0px",top:"0px",width:"200px",maxHeight:"60vh",overflowY:"auto",zIndex:2e3}),D=e=>e.includes("DeepSeek")?"/staticFiles/img/model_icons/DeepSeep R1.png":e.includes("Qwen")?"/staticFiles/img/model_icons/扳手_spanner.png":e.includes("豆包")?"/staticFiles/img/model_icons/豆包.png":e.includes("智谱")?"/staticFiles/img/model_icons/智谱.png":"/staticFiles/img/model_icons/扳手_spanner.png",q=Rs(()=>{const e=g.value.filter(n=>n.checked).sort((n,m)=>n.order-m.order);return _.debug&&console.log("计算已选中的模型，按order排序后:",e.map(n=>`${n.name}(order:${n.order},default:${n.isDefault})`)),e}),P=()=>g.value.filter(e=>e.checked).length,oe=()=>{try{g.value=[];let e=[];if(_.initialModels&&_.initialModels.length>0?e=_.initialModels.map((n,m)=>({...n,checked:!1,isDefault:!1,order:m})):e=[{id:1,name:"gpt-3.5-turbo",checked:!1,isDefault:!1,order:0},{id:2,name:"gpt-4",checked:!1,isDefault:!1,order:1},{id:3,name:"claude-3-opus",checked:!1,isDefault:!1,order:2},{id:4,name:"claude-3-sonnet",checked:!1,isDefault:!1,order:3},{id:5,name:"claude-2",checked:!1,isDefault:!1,order:4}],_.modelValue&&_.modelValue.length>0){const n=[..._.modelValue].sort((v,d)=>v.isDefault?-1:d.isDefault?1:v.order-d.order);for(const v of e){const d=n.find(F=>F.id===v.id);d&&(v.checked=!0,v.order=n.indexOf(d),v.isDefault=d.isDefault)}const m=e.filter(v=>v.checked).sort((v,d)=>v.order-d.order);if(m.length>0){const v=m[0];for(const d of e)d.isDefault=!1;v.isDefault=!0}}else e.length>0;g.value=e,_.debug&&console.log("初始化模型数据:",e.filter(n=>n.checked).map(n=>`${n.name}(order:${n.order},default:${n.isDefault?"true":"false"})`))}catch(e){console.error("初始化模型数据出错:",e)}},le=(e,n)=>{try{if(_.debug&&console.log(`模型 ${e.name} ${n?"选中":"取消选中"}`),n&&P()>=3){console.warn("最多只能选择3个模型");return}if(e.checked=n,!n&&e.isDefault){const m=g.value.find(v=>v.checked);if(m){for(const v of g.value)v.isDefault=!1;m.isDefault=!0}}if(n){const m=g.value.filter(d=>d.checked),v=Math.max(...m.map(d=>d.order),-1);e.order=v+1}G(),ne(()=>{B()})}catch(m){console.error("处理模型选中状态变化出错:",m)}},G=()=>{try{const e=q.value;if(e.length>0){for(const v of g.value)v.isDefault=!1;e.sort((v,d)=>v.order-d.order);const n=e[0],m=g.value.find(v=>v.id===n.id);m&&(m.isDefault=!0),_.debug&&console.log("发送到父组件的数据:",e.map(v=>`${v.name}(order:${v.order},default:${v.isDefault?"true":"false"})`))}s("update:modelValue",e),s("change",e)}catch(e){console.error("发送更新数据出错:",e)}},B=()=>{try{if(!O.value||q.value.length<=1)return;_.debug&&console.log("初始化拖拽功能"),b&&(b.destroy(),b=null);const e=new Image;e.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",b=new Hs(O.value,{animation:150,delay:50,delayOnTouchOnly:!0,draggable:".drag-item",ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",forceFallback:!0,fallbackClass:"sortable-fallback",fallbackOnBody:!0,fallbackTolerance:0,preventOnFilter:!0,dragoverBubble:!1,scroll:!0,scrollSensitivity:80,scrollSpeed:20,supportPointer:!/Edge|IE|Firefox/.test(navigator.userAgent),setData:function(n,m){n.setDragImage(e,0,0),n.effectAllowed="move"},onStart:n=>{document.body.style.cursor="grabbing",document.body.style.userSelect="none";const m=n.item.getBoundingClientRect();n.item.style.transform=`translate(${n.clientX-m.left}px, ${n.clientY-m.top}px)`},onEnd:n=>{document.body.style.cursor="",document.body.style.userSelect="",n.item.style.transform="";const{oldIndex:m,newIndex:v}=n;m!==v&&I(m,v)}})}catch(e){console.error("初始化拖拽功能出错:",e)}},I=(e,n)=>{try{_.debug&&console.log(`模型顺序变更: ${e} -> ${n}`);const m=[...q.value],[v]=m.splice(e,1);m.splice(n,0,v);for(const d of g.value)d.isDefault=!1;m.forEach((d,F)=>{const ie=g.value.find(re=>re.id===d.id);ie&&(ie.order=F,F===0&&(ie.isDefault=!0))}),_.debug&&console.log("拖拽后模型顺序:",m.map(d=>`${d.name}(order:${d.order},default:${d.isDefault})`)),G()}catch(m){console.error("更新模型顺序出错:",m)}},Z=e=>{const n=document.querySelector(".model-selector");n&&!n.contains(e.target)&&(T.value=!1)},H=()=>{T.value?T.value=!1:ne(()=>{z(),T.value=!0})},z=()=>{if(!K.value)return;const e=K.value.getBoundingClientRect(),n=window.innerHeight-e.bottom,m=e.top;y.value=n<300&&m>n,A.value={position:"fixed",left:`${e.left}px`,width:`${e.width}px`,maxHeight:"60vh",overflowY:"auto",zIndex:2e3},y.value?A.value.bottom=`${window.innerHeight-e.top}px`:A.value.top=`${e.bottom}px`,_.debug&&console.log("下拉框位置计算:",{selectRect:e,spaceBelow:n,spaceAbove:m,showOnTop:y.value,style:A.value})},U=()=>{T.value&&z()};return pe(()=>_.modelValue,e=>{_.debug&&console.log("modelValue 变化:",e);const n=q.value.map(d=>d.id),m=e.map(d=>d.id);(n.length!==m.length||n.some((d,F)=>d!==m[F]))&&(oe(),ne(()=>{B()}))},{deep:!0}),pe(()=>q.value,()=>{ne(()=>{B()})},{deep:!0}),Ze(()=>{oe(),document.addEventListener("click",Z),window.addEventListener("resize",U),ne(()=>{setTimeout(()=>{B()},100)})}),Os(()=>{document.removeEventListener("click",Z),window.removeEventListener("resize",U),b&&(b.destroy(),b=null)}),(e,n)=>{const m=w("el-icon"),v=w("el-checkbox");return i(),r("div",Mo,[Co,o("div",To,[o("div",{class:E(["flex items-center px-3 py-1 bg-white border border-solid rounded-md cursor-pointer",{"border-blue-500 hover:border-blue-500 shadow-sm":T.value,"border-gray-300 hover:border-gray-400":!T.value}]),onClick:H,ref_key:"selectRef",ref:K},[o("div",Do,[q.value.length===0?(i(),r("div",So," 请选择模型 ")):(i(),r("div",{key:1,ref_key:"sortableContainer",ref:O,class:"flex flex-wrap items-center w-full sortable-container"},[(i(!0),r(S,null,V(q.value,d=>(i(),r("div",{key:d.id,"data-id":d.id,class:E(["model-tag my-1 mr-2 drag-item",{"is-default":d.isDefault}]),title:"拖动可切换模型位置顺序"},[d.isDefault?(i(),r("span",Lo,"默认")):x("",!0),o("img",{src:D(d.name),class:E(["model-icon",{"ml-2":d.isDefault}]),alt:""},null,10,Eo),R(" "+p(d.name),1)],10,Ao))),128))],512))]),o("div",Io,[u(m,{class:E({"rotate-180":T.value})},{default:h(()=>[u(Ps(Ks))]),_:1},8,["class"])])],2),(i(),X(Qs,{to:"body"},[T.value?(i(),r("div",{key:0,class:E(["dropdown-panel",{"dropdown-panel-top":y.value}]),style:Us(A.value),onClick:n[1]||(n[1]=he(()=>{},["stop"]))},[o("div",Fo,[(i(!0),r(S,null,V(g.value,d=>(i(),r("div",{key:d.id,class:E(["model-item",{"model-item-disabled":!d.checked&&P()>=3,"bg-blue-50":d.checked}]),onClick:F=>!(!d.checked&&P()>=3)&&le(d,!d.checked)},[o("div",qo,[o("img",{src:D(d.name),class:"model-icon",alt:""},null,8,Bo),o("span",zo,p(d.name),1)]),u(v,{"model-value":d.checked,onChange:F=>le(d,F),disabled:!d.checked&&P()>=3,onClick:n[0]||(n[0]=he(()=>{},["stop"])),class:"model-checkbox"},null,8,["model-value","onChange","disabled"])],10,Vo))),128))])],6)):x("",!0)]))])])}}},Oo=Ft(Ro,[["__scopeId","data-v-b68c3d68"]]),Lt=C=>C.includes("DeepSeek")?"/staticFiles/img/model_icons/DeepSeep R1.png":C.includes("Qwen")?"/staticFiles/img/model_icons/扳手_spanner.png":C.includes("豆包")?"/staticFiles/img/model_icons/豆包.png":C.includes("智谱")?"/staticFiles/img/model_icons/智谱.png":"/staticFiles/img/model_icons/扳手_spanner.png",Po={name:"QueryappDetailView",components:{ElButton:xt,ElIcon:wt,Delete:kt,ChatLineRound:yt,Position:bt,Setting:gt,Plus:Zs,CloseBold:Gs,DepartmentPop:lo,CaretTop:Xs,CaretBottom:Ys,ArrowLeftBold:Js,BreadCrumbComponent:oo,CirclePlus:Ws,Loading:Ns,ThinkingBox:so,BackArrowIcon:ko,Close:js,EditIcon:wo,ModelSelector:Oo},setup(){const C=a("queryapp"),c=$s(),_=eo(),s=a("setting"),g=a(null),T=a(""),O=a(""),K=a(""),b=a(""),y=a("给应用介绍一下"),A=a(""),D=a(""),q=a("点击输入历史消息限制条数"),P=a(0),oe=a(!1),le=a("输入提示词"),G=a(null),B=a(null),I=a([]),Z=a(null),H=a(!1),z=a(""),U=a(null),e=a(!0),n=a(!0),m=a(!1),v=a([]),d=a([]),F=a([]),ie=a(!1),re=a(""),_e=a(""),$e=a(1),qt=a("object"),et="开始参考资料分析",ge=a(""),j=a(!1),Bt=a(""),$=a(!1),zt=a(""),Re=a(""),tt=a(!1),st=a(!1),be=a(!1),ee=a([]),ye=a([]),te=a([]),ke=a([]),Oe=a(!1),ot=a(!1),we=a(""),xe=a(""),de=a(!0),lt=a(""),Me=a(""),at=a(2.3),ue=a([]),N=a([]),Pe=a([]),nt=a(!1),it=a(!1),Ke=a([]),me=a([]),W=a([]),dt=a([]),ve=a(!1),ct=a(!1),Ue=a([]),Qe=a(!1),Rt=a("public"),Ce=a([]),Te=a([]),De=a([]),Se=a([]),Ae=a([]),Le=a([]),Ee=a([]),Ie=a([]),He=a(!1),je=a(!1),Ne=a([{id:1,name:"DeepSeek-R1",checked:!1,isDefault:!1},{id:2,name:"Qwen-Max",checked:!1,isDefault:!1},{id:3,name:"Qwen-Plus",checked:!1,isDefault:!1},{id:4,name:"豆包1.5 pro 32k",checked:!1,isDefault:!1},{id:5,name:"智谱 4",checked:!1,isDefault:!1},{id:6,name:"DeepSeek-R1",checked:!1,isDefault:!1},{id:7,name:"DeepSeek-R1",checked:!1,isDefault:!1},{id:8,name:"DeepSeek-R1",checked:!1,isDefault:!1},{id:9,name:"DeepSeek-R1",checked:!1,isDefault:!1},{id:10,name:"DeepSeek-R1",checked:!1,isDefault:!1},{id:11,name:"DeepSeek-R1",checked:!1,isDefault:!1}]),se=a([]),J=a(null),Ot=()=>{const t=se.value.find(l=>l.id===J.value);return t?Lt(t.name):""},Pt=t=>{console.log("聊天框选择了模型:",t)};pe(()=>se.value,t=>{t.length>0&&(J.value||(J.value=t[0].id))},{immediate:!0,deep:!0}),Ze(()=>{ne(()=>{})});const Kt=t=>{console.log("接收到模型选择变更:",t),se.value=t,t.length>0?(!t.some(f=>f.id===J.value)||J.value===null)&&(J.value=t[0].id):J.value=null},{processText:Ut,getThinkingContent:Qt,hasThinkingContent:Ht,isThinkingCompleted:jt}=io(),Nt=t=>{F.value=t,t.forEach(l=>{d.value.push(l.id)})},Wt=t=>{ue.value=t,t.forEach(l=>{N.value.push(l.id)})},Jt=t=>{me.value=t,t.forEach(l=>{W.value.push(l.id)})},We=async()=>{try{const t=await uo(c.params.qid);t.data.error=="1"?_.push({name:"QueryAppList"}):(T.value=t.data.title,O.value=t.data.title,P.value=parseInt(t.data.status),G.value=t.data.prompt,Me.value=t.data.model_name?t.data.model_name:"默认模型",t.data.prompt_is_default&&t.data.prompt&&(le.value=t.data.prompt),t.data.history_limit_is_default?q.value=t.data.history_limit:(A.value=t.data.history_limit,D.value=t.data.history_limit),t.data.description_is_default?t.data.description&&(y.value=t.data.description):(K.value=t.data.description,b.value=t.data.description),B.value=t.data.opening_statement,te.value=t.data.siteusers,ee.value=t.data.departments,we.value=t.data.retrival_type,xe.value=t.data.retrival_type,lt.value=t.data.embedding_model,Nt(t.data.kbases),Wt(t.data.querytools),Jt(t.data.queryfiletemplates),t.data.status=="1"?n.value=!1:n.value=!0,ke.value=te.value.map(l=>l.id),ye.value=ee.value.map(l=>l.id))}catch{}},ce=()=>{oe.value=!1,m.value=!1,nt.value=!1,it.value=!1,ie.value=!1,b.value=K.value,O.value=T.value,xe.value=we.value,D.value=A.value,ot.value=!1,be.value=!1},Yt=t=>{xe.value=t,we.value=t},Xt=async()=>{try{let t=0;D.value&&(t=D.value);const l=await po(O.value,b.value,xe.value,g.value,t);l.data.error=="0"?(Q.success("保存成功"),K.value=b.value,T.value=O.value,A.value=D.value,We()):l.data.error=="403"&&Q.error("暂无权限"),ce()}finally{ce()}},Gt=async()=>{try{const t=await yo(g.value);t.data.error=="0"?_.push({name:"QueryAppList"}):t.data.error=="403"&&Q.error("暂无权限")}catch{}},Zt=async t=>{const l=await bo(g.value,t);l.data.error=="0"?(P.value=parseInt(t),t=="1"?n.value=!1:n.value=!0,rt()):l.data.error=="403"?Q.error("暂无权限"):Q.error("保存失败")},$t=()=>{_.push({name:"IndexWithQid",query:{qid:g.value}})},rt=async()=>{try{const t=te.value.map(Y=>Y.id).join("|"),l=ee.value.map(Y=>Y.id).join("|"),f=Me.value==="默认模型"?"":Me.value,M=se.value.map(Y=>Y.id).join("|"),L=await go(g.value,G.value,B.value,d.value.join("|"),N.value.join("|"),W.value.join("|"),t,l,f||"",M);L.data.error=="0"?Q.success("保存成功"):L.data.error=="2"?Q.error("暂无权限"):L.data.error=="1"&&Q.error("应用不存在")}finally{We(),Fe=0,Je.value="",Ve=0,Ye.value="",Xe()}},es=()=>{let t=0;ge.value="";const l=setInterval(()=>{t<et.length?(ge.value+=et[t],t++):(clearInterval(l),$.value=!0)},100)},ts=t=>{try{if(t.includes("error")){const l=JSON.parse(t);return{topic_id:null,content:[{content:""}],taskmessage_id:"",error:l.error}}else if(t.includes("topic_id")){const l=JSON.parse(t),f=l.topic_id;U.value=f;const M=l.content||"",L=l.taskmessage_id;return{topic_id:f,content:[{content:M}],taskmessage_id:L}}else return{topic_id:null,content:t.match(/\{.*?}/g).map(M=>JSON.parse(M))}}catch{return{topic_id:null,content:"",taskmessage_id:""}}};Tt.setOptions({highlight:function(t,l){return ze.getLanguage(l)?ze.highlight(t,{language:l}).value:ze.highlightAuto(t).value}});const ss=t=>Tt(t);pe(z,t=>{t!==""?H.value=!0:H.value=!1});const os=t=>{if(t.key==="Enter"){if(z.value.trim()===""){t.preventDefault();return}t.shiftKey||t.ctrlKey?(z.value+=`
`,t.preventDefault()):ut()}},ut=async()=>{const t=z.value,l={role:"user",content:t,taskmessage_id:""};I.value.push(l),Re.value="";let f={role:"assistant",content:"",taskmessage_id:"",thinkingContent:"",thinkingCompleted:!1};I.value.push(f),ae(),z.value="",H.value=!1,j.value=!0,ve.value=!0,es(),de.value=!1;try{const M=await ho(t,$e.value,g.value,U.value,qt.value);if($e.value==1)if(M&&M.getReader){const L=M.getReader(),Y=new TextDecoder;let ht=!1;(async()=>{for(;!ht;){const{value:qe,done:Fs}=await L.read();if(ht=Fs,qe){const Vs=Y.decode(qe,{stream:!0}),{content:qs,taskmessage_id:Ge,error:Bs}=ts(Vs);if(Bs=="1"){I.value.pop(),Q.error("应用不存在或无权限使用该应用"),j.value=!1,$.value=!1,ve.value=!1,de.value=!0;return}let _t="";qs.map(fe=>{if(fe.type==_o.StartTool)j.value=!0,ct.value=!0,$.value=!0,ge.value=fe.content;else if(fe.content!==""){j.value&&(j.value=!1),$.value&&($.value=!1);const Be=f.taskmessage_id,zs=Ut(fe.content||"",Be);_t+=zs.normalText,Ht(Be)&&(f.thinkingContent=Qt(Be),f.thinkingCompleted=jt(Be))}}),Re.value+=_t;try{f.content=Re.value,Ge&&(f.taskmessage_id=Ge,l.taskmessage_id=Ge),I.value=[...I.value],ae(),f.content&&(j.value=!1,de.value=!0,$.value=!1)}catch{}}}ve.value=!1})().catch(qe=>{console.error("Stream error:",qe),j.value=!1,$.value=!1,de.value=!0,I.value.pop(),I.value.push({role:"assistant",content:"当前请求网络可能有问题，请重新发起对话"}),ae()})}else throw new Error("Invalid response format for stream mode");else if(de.value=!0,M.data.error=="1")Q.error("应用不存在");else if(M.data.error=="0"){j.value=!1,ve.value=!1;const{topic_id:L,content:Y}=M.data;I.value.push({role:"assistant",content:Y}),U.value=L,ae()}}catch(M){console.log(M),z.value="",H.value=!1,I.value.push({role:"assistant",content:"当前请求网络可能有问题，请重新发起对话"}),ae()}},ae=()=>{const t=Z.value;t&&(t.scrollTop=t.scrollHeight)};pe(I,async()=>{await ne(),document.querySelectorAll("pre code").forEach(t=>{t.dataset.highlighted||(ze.highlightElement(t),t.dataset.highlighted="yes")})}),to(()=>{ae(),window.innerWidth>=800&&window.innerWidth<=1600&&(at.value=1.5)});const ls=async()=>{const t=await fo();v.value=t.data},as=t=>{const l=d.value.indexOf(t);l!==-1?d.value.splice(l,1):d.value.push(t)},ns=()=>{F.value=v.value.filter(t=>d.value.includes(t.id)),ce()};Ze(async()=>{g.value=c.params.qid,ae(),await We();try{const l=await Dt(1,20,"");if(l.data&&Array.isArray(l.data.items)){const f={id:-1,model_name:"默认模型",type:"",parameters:0};Ue.value=[f,...l.data.items],Ne.value=[{id:1,name:"DeepSeek-R1",checked:!0,states:[!1,!0,!0]},{id:2,name:"Qwen-Max",checked:!0,states:[!1,!1,!0]},{id:3,name:"Qwen-Plus",checked:!1,states:[!1,!1,!1]},{id:4,name:"豆包1.5 pro 32k",checked:!1,states:[!1,!1,!1]},{id:5,name:"智谱 4",checked:!0,states:[!1,!0,!0]}],se.value=Ne.value.filter(M=>M.checked),se.value.length>0&&(J.value=se.value[0].id)}}catch(l){console.error("加载模型列表失败:",l)}try{const l=await co();l.data.error==="0"&&(re.value=St(l.data.nickname),_e.value=l.data.nickname,mt(l.data.permissions))}catch(l){console.error("获取用户信息失败:",l)}let t="应用";T.value&&(t=`应用（${T.value}）`),dt.value=[{path:"/admin/application",name:t},{path:"",name:"配置"}],setTimeout(()=>{Xe()},1e3)});const mt=t=>{tt.value=At(t,["del_aiserver"]),st.value=At(t,["create_aiserver","manage_aiserver"])};function is(t){re.value=t}const ds=t=>{_e.value=t},cs=t=>{const l=te.value.findIndex(f=>f.id===t);l!==-1&&te.value.splice(l,1)},rs=t=>{const l=ee.value.findIndex(f=>f.id===t);l!==-1&&ee.value.splice(l,1)},us=()=>{be.value=!be.value},ms=async()=>{const t=await vo();Pe.value=t.data},vs=t=>{const l=N.value.indexOf(t);l!==-1?N.value.splice(l,1):N.value.push(t)},fs=()=>{ue.value=Pe.value.filter(t=>N.value.includes(t.id)),ce()},ps=async()=>{const t=await mo();Ke.value=t.data},hs=t=>{const l=W.value.indexOf(t);l!==-1?W.value.splice(l,1):W.value.push(t)},_s=()=>{me.value=Ke.value.filter(t=>W.value.includes(t.id)),ce()},gs=()=>{Oe.value=!Oe.value},bs=(t,l)=>{console.log(t,"8888888888888");const f=t;te.value=[],ke.value=[],f.forEach(L=>{console.log(L,"新数据"),te.value.push(L),ke.value.push(L.id)});const M=l;ee.value=[],ye.value=[],M.forEach(L=>{ee.value.push(L),ye.value.push(L.id)})},Je=a("");let Fe=0,ys=null;const Ye=a("");let Ve=0,ks=null;const Xe=()=>{let t=`Hello，${_e.value}`;Fe<t.length?(Je.value+=t[Fe],Fe++,setTimeout(Xe,100)):(vt(),clearInterval(ys))},vt=()=>{let t="";B.value?t=B.value:t="很高兴见到你，开始探索吧！",Ve<t.length?(Ye.value+=t[Ve],Ve++,setTimeout(vt,100)):clearInterval(ks)},ws=async t=>{Qe.value=!0;try{const l=await Dt(1,20,t||"");if(l.data.data&&Array.isArray(l.data.data)){const f={id:-1,model_name:"默认模型",type:"",parameters:0};Ue.value=[f,...l.data.data]}}catch(l){console.error("搜索模型失败:",l)}finally{Qe.value=!1}},xs=()=>{_.push("/admin/application")},Ms=(t,l)=>{Ce.value=t,Te.value=l,Ae.value=t.map(f=>f.id),Le.value=l.map(f=>f.id),ft()},Cs=(t,l)=>{De.value=t,Se.value=l,Ee.value=t.map(f=>f.id),Ie.value=l.map(f=>f.id),pt()},Ts=t=>{Ce.value=Ce.value.filter(l=>l.id!==t),Ae.value=Ae.value.filter(l=>l!==t)},Ds=t=>{Te.value=Te.value.filter(l=>l.id!==t),Le.value=Le.value.filter(l=>l!==t)},Ss=t=>{De.value=De.value.filter(l=>l.id!==t),Ee.value=Ee.value.filter(l=>l!==t)},As=t=>{Se.value=Se.value.filter(l=>l.id!==t),Ie.value=Ie.value.filter(l=>l!==t)},Ls=()=>{He.value=!0},Es=()=>{je.value=!0},ft=()=>{He.value=!1},pt=()=>{je.value=!1},Is=a(10);return{selectedBar:C,selectedKBaseNav:s,selectedQid:g,queryappTitle:T,queryappTitle1:O,queryappHistoryLimit1:D,queryappHistoryLimit:A,queryappHistoryLimitPlaceholder:q,ElButton:xt,Delete:kt,ElIcon:wt,delQueryapp:Gt,queryappDesc:K,queryappDesc1:b,queryappDescPlaceholder:y,ChatLineRound:yt,Position:bt,Setting:gt,showModal:oe,setStatus:Zt,toIndex:$t,updateQueryapp:rt,promptPlaceholder:le,queryappPormpt:G,queryappStatement:B,scrollContainer:Z,renderMarkdown:ss,sendTaskmessageHandler:ut,taskmesssageList:I,canSendMessage:H,userContent:z,successAlert:e,queryappStatus:P,fetchcreateQueryapp:Xt,isUp:n,showModalKbase:m,fetchgetAllKBaseList:ls,kbaseList:v,selectedKid:d,chooseKbase:as,closeModal:ce,saveKbase:ns,selectedKbaseList:F,showModalDel:ie,nicknameOne:re,handleNickname:ds,nickname:_e,displayedText:Bt,showEllipsis:$,ellipsis:zt,showLoading:j,handleDataPermissions:mt,handleNicknameOne:is,canDelAction:tt,showAllSiteuser:us,pullDown:be,siteuserList:te,departmentList:ee,siteuserIds:ke,departmentIds:ye,showModalSiteuser:ot,getFirstLetter:St,handleValueFromChild:bs,removeSiteuser:cs,removeDepartment:rs,changeType:Yt,selectedType:we,canWriteIpt:de,queryappEmbeddingModel:lt,minRowsAction:at,selectedToolList:ue,selectedTids:N,getAppToolList:ms,toolList:Pe,showModalTool:nt,chooseTool:vs,saveTool:fs,canPerformAction:st,handleKeyDown:os,showModalFileTemplate:it,fileTemplateList:Ke,selectedfileTemplateList:me,selectedFTids:W,getFileTemplateList:ps,chooseFileTemplate:hs,saveFileTemplate:_s,breadcrumbs:dt,showDepartmentPopModal:Oe,showDepartmentPop:gs,displayedTextLoading:ge,isRendering:ve,showTool:ct,displayedText1:Je,displayedText2:Ye,modelName:Me,modelOptions:Ue,modelLoading:Qe,handleModelSearch:ws,handleBack:xs,visibilityType:Rt,visibleUsers:Ce,visibleDepartments:Te,editableUsers:De,editableDepartments:Se,visibleUserIds:Ae,visibleDeptIds:Le,editableUserIds:Ee,editableDeptIds:Ie,showVisibilityModal:He,showEditableModal:je,handleVisibilitySelection:Ms,handleEditableSelection:Cs,removeVisibleUser:Ts,removeVisibleDept:Ds,removeEditableUser:Ss,removeEditableDept:As,showSelectVisibilityModal:Ls,showSelectEditableModal:Es,closeVisibilityModal:ft,closeEditableModal:pt,availableModels:Ne,selectedModels:se,handleModelSelectionChange:Kt,currentSelectedModel:J,handleCurrentModelChange:Pt,getSelectedModelIcon:Ot,getModelIcon:Lt,historyMessageCount:Is,removeKbase:t=>{const l=d.value.indexOf(t);l!==-1&&(d.value.splice(l,1),F.value=F.value.filter(f=>f.id!==t))},removeTool:t=>{const l=N.value.indexOf(t);l!==-1&&(N.value.splice(l,1),ue.value=ue.value.filter(f=>f.id!==t))},removeFileTemplate:t=>{const l=W.value.indexOf(t);l!==-1&&(W.value.splice(l,1),me.value=me.value.filter(f=>f.id!==t))}}}},k=C=>(Et("data-v-5955ee89"),C=C(),It(),C),Ko={class:"common-layout"},Uo={class:"flex items-center pb-[12px] border-0 border-solid border-b border-[#E5E5E5]"},Qo=k(()=>o("span",{class:"ml-2 text-[18px] font-semibold text-[#121619] leading-[26px] font-[PingFang SC]"},"配置",-1)),Ho={class:"el-main-bottom"},jo={class:"main-content-wrapper border border-solid border-[#E5E5E5] rounded-[12px] !p-[16px] !mr-[16px]"},No=k(()=>o("div",{class:"el-aside2-top"},[o("div",{class:"font18 font-zhongcu"},"应用信息")],-1)),Wo={class:"el-aside2-center h-[85%] pb-[20px] font12 overflow-y !mt-0"},Jo={class:"mt-6"},Yo={class:"el-aside2-center-item !p-0 mt-[16px] !mb-0"},Xo=k(()=>o("div",{class:"subtitle-label"},"提示词",-1)),Go=["placeholder"],Zo={class:"el-aside2-center-item mt-[0px] border border-solid border-[#E5E5E5] !bg-white rounded-[8px]"},$o=k(()=>o("div",{class:"el-aside2-center-item-title text-[#697077]"}," 关联知识库 ",-1)),el={class:"el-aside2-center-item border border-solid border-[#E5E5E5] !bg-white rounded-[8px]"},tl=k(()=>o("div",{class:"el-aside2-center-item-title text-[#697077]"}," 关联工具 ",-1)),sl={class:"el-aside2-center-item border border-solid border-[#E5E5E5] !bg-white rounded-[8px]"},ol=k(()=>o("div",{class:"el-aside2-center-item-title text-[#697077]"}," 关联文件模板 ",-1)),ll={class:"el-aside2-center-item !p-0 mt-[16px] !mb-0"},al=k(()=>o("div",{class:"subtitle-label"},"开场白",-1)),nl={class:"el-aside2-center-item !p-0 mt-[0px] !mb-0"},il=k(()=>o("div",{class:"subtitle-label"},"历史消息条数",-1)),dl={class:"el-aside2-bottom"},cl={class:"el-main2-top bg-white !px-0 !pt-0"},rl={style:{display:"flex","align-items":"center"}},ul=k(()=>o("div",{class:"font18 font-zhongcu"},"调试预览",-1)),ml={class:"el-main2-top-status-div"},vl={key:0,class:"font12"},fl={key:1,class:"font12"},pl={key:0,class:"model-selector-float relative z-50 top-[50px] left-[12px]"},hl={class:"model-icon-wrapper"},_l=["src"],gl={class:"flex items-center"},bl={class:"model-icon-container relative right-2"},yl=["src"],kl={class:"font14"},wl={class:"taskmessage-right-bottom overflow-y relative",ref:"scrollContainer"},xl={key:0,class:"opening-statement-div"},Ml={class:"font36"},Cl={class:"font24"},Tl={key:1,style:{"margin-top":"35px"}},Dl={class:"chat-item chat-item-opening"},Sl=k(()=>o("img",{src:Vt,alt:"",class:"chat-item-ai-headimg"},null,-1)),Al={class:"chat-item-content opening-statement"},Ll={class:"opening-statement-title font18"},El=["innerHTML"],Il={key:0,class:E(["chat-item","taskmessage-user"])},Fl={class:"siteuser-headimg font16"},Vl={class:"chat-item-content taskmessage-user-content font14"},ql=["innerHTML"],Bl={key:1,class:E(["chat-item","taskmessage-ai"])},zl={class:"chat-item-ai-headimg-div"},Rl={key:0,src:ao,alt:"",style:{width:"25px",height:"25px"}},Ol={key:1,src:Vt,alt:"",class:"chat-item-ai-headimg"},Pl={class:"chat-item-content taskmessage-ai-content"},Kl=["innerHTML"],Ul={key:1,class:"font14",style:{}},Ql={key:0,src:no,alt:"",style:{width:"18px",height:"18px"}},Hl={class:"el-main2-bottom"},jl={class:"taskmessage-add"},Nl=["disabled"],Wl=k(()=>o("img",{src:ro,alt:""},null,-1)),Jl=[Wl],Yl={class:"permission-card"},Xl={class:"app-info-header"},Gl={class:"app-info-main"},Zl={class:"app-avatar"},$l={class:"app-title"},ea={class:"font18 font-zhongcu"},ta=k(()=>o("div",{class:"font12 text-gray-500"}," 创建者：ITadmin ",-1)),sa=k(()=>o("div",{class:"app-tag-container"},[o("div",{class:"app-tag"},"专业问答")],-1)),oa={class:"app-info-section mt-6"},la=k(()=>o("div",{class:"font14 font-zhongcu mb-2"}," 应用可见范围： ",-1)),aa={class:"flex flex-col gap-2 mb-2"},na={key:0,class:"mt-4"},ia={class:"user-tags-container"},da=k(()=>o("span",null,"添加人员",-1)),ca={class:"user-tag-avatar"},ra={class:"user-tag-avatar bg-blue-500"},ua={class:"app-info-section mt-6"},ma=k(()=>o("div",{class:"font14 font-zhongcu mb-2"}," 可编辑应用人员： ",-1)),va={class:"user-tags-container"},fa=k(()=>o("span",null,"添加管理员",-1)),pa={class:"user-tag-avatar"},ha={class:"user-tag-avatar bg-blue-500"},_a={class:"mt-0 relative top-4"},ga={key:3,class:"new-modal-overlay"},ba={class:"new-modal-container",style:{width:"45%","max-width":"800px",border:"none"}},ya=k(()=>o("div",{class:"new-modal-top"},[o("div",{class:"font18 font-zhongcu"},"关联知识库")],-1)),ka={class:"new-modal-center"},wa={class:"modal-kbase-list overflow-y"},xa=["onClick"],Ma={class:"el-col-left font18 font-zhongcu"},Ca={class:"el-col-right"},Ta={class:"font14 font-zhongcu overflow-one el-col-right-title"},Da={class:"font12 overflow-one el-col-right-desc"},Sa={class:"el-col-left font18 font-zhongcu"},Aa={class:"el-col-right"},La={class:"font14 font-zhongcu overflow-one el-col-right-title"},Ea={class:"font12 overflow-one el-col-right-desc"},Ia={class:"new-modal-bottom"},Fa={key:4,class:"new-modal-overlay"},Va={class:"new-modal-container",style:{width:"45%","max-width":"800px",border:"none"}},qa=k(()=>o("div",{class:"new-modal-top"},[o("div",{class:"font18 font-zhongcu"},"关联工具")],-1)),Ba={class:"new-modal-center"},za={class:"modal-kbase-list overflow-y"},Ra=["onClick"],Oa={class:"el-col-left el-col-left-tool font18 font-zhongcu"},Pa={class:"el-col-right"},Ka={class:"font14 font-zhongcu overflow-one el-col-right-title"},Ua={class:"font12 overflow-one el-col-right-desc"},Qa={class:"new-modal-bottom"},Ha={key:5,class:"new-modal-overlay"},ja={class:"new-modal-container",style:{width:"45%","max-width":"800px",border:"none"}},Na=k(()=>o("div",{class:"new-modal-top"},[o("div",{class:"font18 font-zhongcu"},"关联文件模板")],-1)),Wa={class:"new-modal-center"},Ja={class:"modal-kbase-list overflow-y"},Ya=["onClick"],Xa={class:"el-col-left el-col-left-file font18 font-zhongcu"},Ga={class:"el-col-right"},Za={class:"font14 font-zhongcu overflow-one el-col-right-title"},$a={class:"font12 overflow-one el-col-right-desc"},en={class:"new-modal-bottom"};function tn(C,c,_,s,g,T){const O=w("BackArrowIcon"),K=w("ModelSelector"),b=w("Close"),y=w("el-icon"),A=w("Plus"),D=w("el-button"),q=w("el-input"),P=w("el-aside"),oe=w("el-option"),le=w("el-select"),G=w("ThinkingBox"),B=w("el-main"),I=w("EditIcon"),Z=w("el-radio"),H=w("el-container"),z=w("BreadCrumbComponent"),U=w("DepartmentPop");return i(),r(S,null,[o("div",Ko,[o("div",Uo,[o("div",{class:"flex items-center cursor-pointer",onClick:c[0]||(c[0]=(...e)=>s.handleBack&&s.handleBack(...e))},[u(O,{size:24,color:"#121619"})]),Qo]),o("div",Ho,[u(H,{class:"el-container2"},{default:h(()=>[o("div",jo,[u(P,{class:"el-aside2 !p-0 !mr-[32px]"},{default:h(()=>[No,o("div",Wo,[o("div",Jo,[u(K,{modelValue:s.selectedModels,"onUpdate:modelValue":c[1]||(c[1]=e=>s.selectedModels=e),"initial-models":s.availableModels,onChange:s.handleModelSelectionChange},null,8,["modelValue","initial-models","onChange"])]),o("div",Yo,[Xo,Mt(o("textarea",{name:"prompt",id:"prompt",class:"el-aside2-center-txt gray-bg",placeholder:s.promptPlaceholder,"onUpdate:modelValue":c[2]||(c[2]=e=>s.queryappPormpt=e),maxlength:"10000"},null,8,Go),[[Ct,s.queryappPormpt]])]),o("div",Zo,[$o,(i(!0),r(S,null,V(s.selectedKbaseList,e=>(i(),r("div",{class:"selected-kbase-item font10 mr-2",key:e.id},[R(p(e.title?e.title:"这个知识库还没有名称")+" ",1),u(y,{class:"ml-1 cursor-pointer",onClick:he(n=>s.removeKbase(e.id),["stop"])},{default:h(()=>[u(b)]),_:2},1032,["onClick"])]))),128)),u(D,{class:"queryapp-btn text-[#A2A9B0] relative top-[1px]",onClick:c[3]||(c[3]=e=>{s.fetchgetAllKBaseList(),s.showModalKbase=!0})},{default:h(()=>[u(y,{class:"plus-icon"},{default:h(()=>[u(A)]),_:1})]),_:1})]),o("div",el,[tl,(i(!0),r(S,null,V(s.selectedToolList,e=>(i(),r("div",{class:"selected-kbase-item font10 mr-2",key:e.id},[R(p(e.name?e.name:"这个工具还没有名称")+" ",1),u(y,{class:"ml-1 cursor-pointer",onClick:he(n=>s.removeTool(e.id),["stop"])},{default:h(()=>[u(b)]),_:2},1032,["onClick"])]))),128)),u(D,{class:"queryapp-btn text-[#A2A9B0] relative top-[1px]",onClick:c[4]||(c[4]=e=>{s.getAppToolList(),s.showModalTool=!0})},{default:h(()=>[u(y,{class:"plus-icon"},{default:h(()=>[u(A)]),_:1})]),_:1})]),o("div",sl,[ol,(i(!0),r(S,null,V(s.selectedfileTemplateList,e=>(i(),r("div",{class:"selected-kbase-item font10 mr-2",key:e.id},[R(p(e.name?e.name:"这个文件模板还没有名称")+" ",1),u(y,{class:"ml-1 cursor-pointer",onClick:he(n=>s.removeFileTemplate(e.id),["stop"])},{default:h(()=>[u(b)]),_:2},1032,["onClick"])]))),128)),u(D,{class:"queryapp-btn text-[#A2A9B0] relative top-[1px]",onClick:c[5]||(c[5]=e=>{s.getFileTemplateList(),s.showModalFileTemplate=!0})},{default:h(()=>[u(y,{class:"plus-icon"},{default:h(()=>[u(A)]),_:1})]),_:1})]),o("div",ll,[al,Mt(o("textarea",{name:"statement",id:"statement",class:"el-aside2-center-txt gray-bg",placeholder:"每次对话开始前，发送一个初始内容。支持标准Markdown语法，可使用的额外标记: [快捷按键]:用户点击后可以直接发送该问题","onUpdate:modelValue":c[6]||(c[6]=e=>s.queryappStatement=e),maxlength:"10000"},null,512),[[Ct,s.queryappStatement]])]),o("div",nl,[il,u(q,{modelValue:s.historyMessageCount,"onUpdate:modelValue":c[7]||(c[7]=e=>s.historyMessageCount=e),placeholder:"请输入历史消息条数",class:"history-count-input !rounded-[8px]",type:"number",min:"0"},null,8,["modelValue"])])]),o("div",dl,[s.canPerformAction?(i(),X(D,{key:0,type:"primary",color:"#129BFF",onClick:s.updateQueryapp,class:"save-btn common-confirm-btn"},{default:h(()=>[R("保存")]),_:1},8,["onClick"])):x("",!0)])]),_:1}),u(B,{class:"el-main2"},{default:h(()=>[o("div",cl,[o("div",rl,[ul,o("div",ml,[o("div",{class:E(s.queryappStatus=="1"?"el-main2-top-status-div-success":"")},null,2),s.queryappStatus=="1"?(i(),r("span",vl,"已发布")):(i(),r("span",fl,"未发布"))])]),s.selectedModels.length>0?(i(),r("div",pl,[u(le,{modelValue:s.currentSelectedModel,"onUpdate:modelValue":c[8]||(c[8]=e=>s.currentSelectedModel=e),placeholder:"选择模型",size:"small",class:"model-select-dropdown-preview",onChange:s.handleCurrentModelChange,"popper-class":"model-custom-dropdown"},{prefix:h(()=>[o("div",hl,[s.currentSelectedModel?(i(),r("img",{key:0,src:s.getSelectedModelIcon(),class:"model-icon-selected",alt:""},null,8,_l)):x("",!0)])]),default:h(()=>[(i(!0),r(S,null,V(s.selectedModels,e=>(i(),X(oe,{key:e.id,label:e.name,value:e.id},{default:h(()=>[o("div",gl,[o("div",bl,[o("img",{src:s.getModelIcon(e.name),class:"model-icon-option",alt:""},null,8,yl)]),o("span",null,p(e.name),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue","onChange"])])):x("",!0),o("div",kl,[u(D,{class:E(["el-main2-top-btn",s.queryappStatus!="1"?"el-main2-top-btn-disabled":"common-confirm-btn"]),disabled:s.queryappStatus==0||s.queryappStatus==2,onClick:s.toIndex},{default:h(()=>[R("对话")]),_:1},8,["class","disabled","onClick"]),s.queryappStatus==0||s.queryappStatus==2?(i(),X(D,{key:0,class:"el-main2-top-btn common-confirm-btn",onClick:c[9]||(c[9]=e=>s.setStatus("1"))},{default:h(()=>[R("上架")]),_:1})):x("",!0),s.queryappStatus==1?(i(),X(D,{key:1,class:"el-main2-top-btn el-main2-top-btn-xiajia common-cancel-btn",onClick:c[10]||(c[10]=e=>s.setStatus("2"))},{default:h(()=>[R("下架")]),_:1})):x("",!0)])]),o("div",wl,[s.taskmesssageList.length<=0?(i(),r("div",xl,[o("div",Ml,p(s.displayedText1),1),o("div",Cl,p(s.displayedText2),1)])):(i(),r("div",Tl,[o("div",Dl,[Sl,o("div",Al,[o("div",Ll," 你好，我是"+p(s.queryappTitle),1),s.queryappStatement?(i(),r("div",{key:0,class:"opening-statement-desc font14",innerHTML:s.renderMarkdown(s.queryappStatement)},null,8,El)):x("",!0)])]),(i(!0),r(S,null,V(s.taskmesssageList,(e,n)=>(i(),r(S,{key:n},[e.role==="user"?(i(),r("div",Il,[o("div",Fl,p(s.nicknameOne),1),o("div",Vl,[o("div",{class:"font14",innerHTML:s.renderMarkdown(e.content)},null,8,ql)])])):e.role==="assistant"?(i(),r("div",Bl,[o("div",zl,[s.isRendering&&n==s.taskmesssageList.length-1?(i(),r("img",Rl)):(i(),r("img",Ol))]),o("div",Pl,[e.thinkingContent?(i(),X(G,{key:0,content:e.thinkingContent,completed:e.thinkingCompleted,style:{"margin-top":"0","margin-bottom":"10px"}},null,8,["content","completed"])):x("",!0),o("div",{class:"font14",innerHTML:s.renderMarkdown(e.content)},null,8,Kl),s.showLoading&&n==s.taskmesssageList.length-1?(i(),r("div",Ul,[R(p(s.displayedTextLoading)+" ",1),s.showEllipsis?(i(),r("img",Ql)):x("",!0)])):x("",!0)])])):x("",!0)],64))),128))]))],512),o("div",Hl,[o("div",jl,[o("div",{class:E(s.canSendMessage?"":"taskmessage-ipt-no")},[u(q,{modelValue:s.userContent,"onUpdate:modelValue":c[11]||(c[11]=e=>s.userContent=e),autosize:{minRows:1,maxRows:6},type:"textarea",placeholder:"输入问题",resize:"none",disabled:!s.canWriteIpt,maxlength:"20000",onKeydown:s.handleKeyDown},null,8,["modelValue","disabled","onKeydown"]),o("button",{class:E(s.canSendMessage?"":"send-btn-no"),disabled:!s.canSendMessage,onClick:c[12]||(c[12]=(...e)=>s.sendTaskmessageHandler&&s.sendTaskmessageHandler(...e))},Jl,10,Nl)],2)])])]),_:1})]),u(P,{class:"permission-aside border border-solid border-[#E5E5E5] rounded-[12px]"},{default:h(()=>[o("div",Yl,[o("div",Xl,[o("div",Gl,[o("div",Zl,[o("span",null,p(s.getFirstLetter(s.queryappTitle)),1)]),o("div",$l,[o("div",ea,p(s.queryappTitle),1),ta,sa])]),o("div",{class:"edit-icon-container",onClick:c[13]||(c[13]=e=>s.showModal=!0)},[u(I,{size:"16",color:"#A2A9B0"})])]),o("div",oa,[la,o("div",aa,[u(Z,{modelValue:s.visibilityType,"onUpdate:modelValue":c[14]||(c[14]=e=>s.visibilityType=e),label:"public"},{default:h(()=>[R("公开")]),_:1},8,["modelValue"]),u(Z,{modelValue:s.visibilityType,"onUpdate:modelValue":c[15]||(c[15]=e=>s.visibilityType=e),label:"limited"},{default:h(()=>[R("指定范围")]),_:1},8,["modelValue"])]),s.visibilityType==="limited"?(i(),r("div",na,[o("div",ia,[o("div",{class:"add-user-btn mr-2",onClick:c[16]||(c[16]=(...e)=>s.showSelectVisibilityModal&&s.showSelectVisibilityModal(...e))},[u(y,null,{default:h(()=>[u(A)]),_:1}),da]),(i(!0),r(S,null,V(s.visibleUsers,e=>(i(),r("div",{key:e.id,class:"user-tag"},[o("div",ca,p(s.getFirstLetter(e.name)),1),o("span",null,p(e.name),1),u(y,{class:"ml-1 cursor-pointer",onClick:n=>s.removeVisibleUser(e.id)},{default:h(()=>[u(b)]),_:2},1032,["onClick"])]))),128)),(i(!0),r(S,null,V(s.visibleDepartments,e=>(i(),r("div",{key:e.id,class:"user-tag"},[o("div",ra,p(s.getFirstLetter(e.name)),1),o("span",null,p(e.name),1),u(y,{class:"ml-1 cursor-pointer",onClick:n=>s.removeVisibleDept(e.id)},{default:h(()=>[u(b)]),_:2},1032,["onClick"])]))),128))])])):x("",!0)]),o("div",ua,[ma,o("div",va,[o("div",{class:"add-user-btn mr-2",onClick:c[17]||(c[17]=(...e)=>s.showSelectEditableModal&&s.showSelectEditableModal(...e))},[u(y,null,{default:h(()=>[u(A)]),_:1}),fa]),(i(!0),r(S,null,V(s.editableUsers,e=>(i(),r("div",{key:e.id,class:"user-tag"},[o("div",pa,p(s.getFirstLetter(e.name)),1),o("span",null,p(e.name),1),u(y,{class:"ml-1 cursor-pointer",onClick:n=>s.removeEditableUser(e.id)},{default:h(()=>[u(b)]),_:2},1032,["onClick"])]))),128)),(i(!0),r(S,null,V(s.editableDepartments,e=>(i(),r("div",{key:e.id,class:"user-tag"},[o("div",ha,p(s.getFirstLetter(e.name)),1),o("span",null,p(e.name),1),u(y,{class:"ml-1 cursor-pointer",onClick:n=>s.removeEditableDept(e.id)},{default:h(()=>[u(b)]),_:2},1032,["onClick"])]))),128))])])])]),_:1})]),_:1})])]),o("div",_a,[u(z,{breadcrumbs:s.breadcrumbs},null,8,["breadcrumbs"])]),s.showDepartmentPopModal?(i(),X(U,{key:0,type:"queryapp",sids:s.siteuserIds,dids:s.departmentIds,onValueFromChild:s.handleValueFromChild,closeModal:s.showDepartmentPop},null,8,["sids","dids","onValueFromChild","closeModal"])):x("",!0),s.showVisibilityModal?(i(),X(U,{key:1,type:"queryapp",sids:s.visibleUserIds,dids:s.visibleDeptIds,onValueFromChild:s.handleVisibilitySelection,closeModal:s.closeVisibilityModal},null,8,["sids","dids","onValueFromChild","closeModal"])):x("",!0),s.showEditableModal?(i(),X(U,{key:2,type:"queryapp",sids:s.editableUserIds,dids:s.editableDeptIds,onValueFromChild:s.handleEditableSelection,closeModal:s.closeEditableModal},null,8,["sids","dids","onValueFromChild","closeModal"])):x("",!0),s.showModalKbase?(i(),r("div",ga,[o("div",ba,[ya,o("div",ka,[o("div",wa,[(i(!0),r(S,null,V(s.kbaseList,e=>(i(),r(S,{key:e.id},[s.queryappEmbeddingModel==e.embedding_type?(i(),r("div",{key:0,onClick:n=>s.chooseKbase(e.id),class:E(["modal-kabse-item",s.selectedKid.includes(e.id)?"modal-kabse-item-selected":""])},[o("div",Ma,p(s.getFirstLetter(e.title)),1),o("div",Ca,[o("div",Ta,p(e.title),1),o("div",Da,p(e.description?e.description:"这个知识库还没有介绍～"),1)])],10,xa)):(i(),r("div",{key:1,class:E(["modal-kabse-item",s.selectedKid.includes(e.id)?"modal-kabse-item-selected":""]),style:{cursor:"not-allowed"}},[o("div",Sa,p(s.getFirstLetter(e.title)),1),o("div",Aa,[o("div",La,p(e.title),1),o("div",Ea,p(e.description?e.description:"这个知识库还没有介绍～"),1)])],2))],64))),128))])]),o("div",Ia,[o("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:c[18]||(c[18]=(...e)=>s.closeModal&&s.closeModal(...e))}," 取消 "),o("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu common-confirm-btn",onClick:c[19]||(c[19]=(...e)=>s.saveKbase&&s.saveKbase(...e))}," 保存 ")])])])):x("",!0),s.showModalTool?(i(),r("div",Fa,[o("div",Va,[qa,o("div",Ba,[o("div",za,[(i(!0),r(S,null,V(s.toolList,e=>(i(),r("div",{key:e.id,onClick:n=>s.chooseTool(e.id),class:E(["modal-kabse-item",s.selectedTids.includes(e.id)?"modal-kabse-item-selected":""])},[o("div",Oa,p(s.getFirstLetter(e.name)),1),o("div",Pa,[o("div",Ka,p(e.name),1),o("div",Ua,p(e.description?e.description:"这个工具还没有介绍～"),1)])],10,Ra))),128))])]),o("div",Qa,[o("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:c[20]||(c[20]=(...e)=>s.closeModal&&s.closeModal(...e))}," 取消 "),o("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu common-confirm-btn",onClick:c[21]||(c[21]=(...e)=>s.saveTool&&s.saveTool(...e))}," 保存 ")])])])):x("",!0),s.showModalFileTemplate?(i(),r("div",Ha,[o("div",ja,[Na,o("div",Wa,[o("div",Ja,[(i(!0),r(S,null,V(s.fileTemplateList,e=>(i(),r("div",{key:e.id,onClick:n=>s.chooseFileTemplate(e.id),class:E(["modal-kabse-item",s.selectedFTids.includes(e.id)?"modal-kabse-item-selected":""])},[o("div",Xa,p(s.getFirstLetter(e.name)),1),o("div",Ga,[o("div",Za,p(e.name),1),o("div",$a,p(e.description?e.description:"这个模板还没有介绍～"),1)])],10,Ya))),128))])]),o("div",en,[o("button",{type:"button",class:"new-modal-bottom-cancel font14 font-zhongcu common-cancel-btn",onClick:c[22]||(c[22]=(...e)=>s.closeModal&&s.closeModal(...e))}," 取消 "),o("button",{type:"submit",class:"new-modal-bottom-confirm font14 font-zhongcu common-confirm-btn",onClick:c[23]||(c[23]=(...e)=>s.saveFileTemplate&&s.saveFileTemplate(...e))}," 保存 ")])])])):x("",!0)],64)}const un=Ft(Po,[["render",tn],["__scopeId","data-v-5955ee89"]]);export{un as default};
