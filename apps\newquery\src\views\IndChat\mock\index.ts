// 定义答案类型
type AnswerType =
    | string
    | Array<{
          uid?: string;
          area: string;
          data: any;
      }>;

// 扩展 mockData，增加关键词数组用于模糊匹配

export const mockData = [
    {
        question: 'thinking测试',
        keywords: ['thinking', '思考', '测试'],
        answer: [
            {
                uid: 'default',
                area: 'mid-def-bottom',
                data: '<think>这是AI的思考过程，我需要分析用户提出的问题并给出合适的回答。\n\n首先，用户想测试思考功能，所以我需要在回复中加入思考内容。\n\n这部分内容应该会在思考框中显示出来，而不是直接显示在回答中。</think>\n\n这是AI的正常回答内容，没有包含在思考标签中的部分会作为回答展示给用户。\n\n您好，我已经收到您的测试请求，思考功能已经正常工作了！',
            },
        ],
    },
    // {
    //   question: "数组形式思考测试",
    //   keywords: ["数组", "思考测试"],
    //   answer: [
    //     {
    //       uid: "default",
    //       area: "mid-def-bottom",
    //       data: "<think>这是第一部分思考内容，将在思考框中显示。</think>开始的一部分回答内容。",
    //     },
    //     {
    //       uid: "default",
    //       area: "mid-def-bottom",
    //       data: "<think>这是第二部分思考内容，应该会被追加到前面的思考内容后面。</think>第二部分回答内容。",
    //     },
    //     {
    //       uid: "default",
    //       area: "mid-def-bottom",
    //       data: "这部分不包含思考内容，只有正常回答。",
    //     },
    //   ],
    // },
    // {
    //   question: "数组形式思考测试2",
    //   keywords: ["数组", "思考测试"],
    //   answer: [
    //     {
    //       uid: "default",
    //       area: "mid-def-bottom",
    //       data: "<think>",
    //     },
    //     {
    //       uid: "default",
    //       area: "mid-def-bottom",
    //       data: "这是第一部分思考内容，将在思考框中显示。<br>",
    //     },
    //     {
    //       uid: "default",
    //       area: "mid-def-bottom",
    //       data: "这是第二部分思考内容，应该会被追加到前面的思考内容后面。<br>",
    //     },
    //     {
    //       uid: "default",
    //       area: "mid-def-bottom",
    //       data: "这是第三部分思考内容，应该会被追加到前面的思考内容后面。",
    //     },
    //     {
    //       uid: "default",
    //       area: "mid-def-bottom",
    //       data: "</think>",
    //     },
    //     {
    //       uid: "default",
    //       area: "mid-def-bottom",
    //       data: "这部分不包含思考内容，只有正常回答。",
    //     },
    //   ],
    // },
    // {
    //   question: "文件列表",
    //   keywords: ["文件列表"],
    //   answer: [
    //     {
    //       uid: "default",
    //       area: "mid-def-bottom",
    //       data: "<think>这是第一部分思考内容，将在思考框中显示。<br>",
    //     },
    //     {
    //       uid: "default",
    //       area: "mid-def-bottom",
    //       data: "这是第二部分思考内容，应该会被追加到前面的思考内容后面。<br>",
    //     },
    //     {
    //       uid: "default",
    //       area: "mid-def-bottom",
    //       data: "这是第三部分思考内容，应该会被追加到前面的思考内容后面。<br></think>结尾内容应该截取放到正常回答。",
    //     },
    //     {
    //       uid: "default",
    //       area: "mid-def-bottom",
    //       data: "这部分不包含思考内容，只有正常回答。",
    //     },
    //     {
    //       uid: "default",
    //       // area: "mid-def-bottom",
    //       data: "以下为文件列表",
    //     },
    //     {
    //       // uid: "file-list-and-pages-component",
    //       uid: "file-list-component",
    //       area: "mid-txt-bottom",
    //       data: {
    //         files: [
    //           {
    //             title: "MaxKB后端文档.docx",
    //             fileUrl:
    //               "http://turbine-api.aialign.com.cn/media/files/pptx-demo/MaxKB后端文档.docx",
    //             initialPage: 4,
    //             fileType: "docx",
    //             pages: [400, 500, 610, 700, 800, 900, 1000],
    //           },
    //           {
    //             title: "PLM21x_风场GoldenBOM管理用户操作手册.pptx",
    //             fileUrl:
    //               "http://turbine-api.aialign.com.cn/media/files/pptx-demo/PLM21x_风场GoldenBOM管理用户操作手册.pptx",
    //             initialPage: 3,
    //             fileType: "pptx",
    //             pages: [1, 2, 3, 4, 5, 60, 70, 800, 90, 100],
    //           },
    //           {
    //             title: "MaxKB后端文档.docx",
    //             fileUrl:
    //               "http://turbine-api.aialign.com.cn/media/files/pptx-demo/MaxKB后端文档.docx",
    //             initialPage: 4,
    //             fileType: "docx",
    //             pages: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14],
    //           },
    //           {
    //             title: "PLM21x_风场GoldenBOM管理用户操作手册.pptx",
    //             fileUrl:
    //               "http://turbine-api.aialign.com.cn/media/files/pptx-demo/PLM21x_风场GoldenBOM管理用户操作手册.pptx",
    //             initialPage: 3,
    //             fileType: "pptx",
    //             pages: [1, 2, 3],
    //           },
    //         ],
    //       },
    //     },
    //   ],
    // },
];

export const mockDatafile = [
    {
        question: '我想看ppt1',
        keywords: ['ppt1'],
        answer: [
            {
                uid: 'pptx-preview-component',
                area: 'mid-def-top',
                data: {
                    title: 'ppt预览',
                    fileUrl:
                        // "http://turbine-api.aialign.com.cn/media/files/Sample_12.pptx",
                        'http://192.168.10.10:8012/PLM21x_%E9%A3%8E%E5%9C%BAGoldenBOM%E7%AE%A1%E7%90%86%E7%94%A8%E6%88%B7%E6%93%8D%E4%BD%9C%E6%89%8B%E5%86%8Cpptx.pdf',
                    initialPage: 3,
                },
            },
        ],
    },
    {
        question: '我想看ppt2',
        keywords: ['ppt2'],
        answer: [
            {
                uid: 'pptx-preview-component',
                area: 'mid-def-top',
                data: {
                    title: 'ppt2预览',
                    fileUrl:
                        'http://turbine-api.aialign.com.cn/media/files/123.ppt',
                    initialPage: 3,
                },
            },
        ],
    },
    {
        question: '我想看pdf',
        keywords: ['pdf'],
        answer: [
            {
                //uid: "pdf-preview-component",
                uid: 'pdf-viewer-component',
                area: 'right-bottom',
                data: {
                    title: 'pdf预览',
                    // fileUrl: "http://turbine-api.aialign.com.cn/media/files/test.pdf",
                    // fileUrl:
                    // "http://192.168.10.10:8012/PLM21x_%E9%A3%8E%E5%9C%BAGoldenBOM%E7%AE%A1%E7%90%86%E7%94%A8%E6%88%B7%E6%93%8D%E4%BD%9C%E6%89%8B%E5%86%8Cpptx.pdf",

                    //http://192.168.10.10:8012/MaxKB%E5%90%8E%E7%AB%AF%E6%96%87%E6%A1%A3docx.pdf
                    // fileUrl:
                    // "http://192.168.10.10:8012/MaxKB%E5%90%8E%E7%AB%AF%E6%96%87%E6%A1%A3docx.pdf",

                    // fileUrl:
                    //   "http://192.168.10.10:8012/PLM21x_%E4%B8%93%E9%A1%B9%E8%AF%BE%E7%A8%8B-02-%E4%BA%A7%E5%93%81%E6%95%B0%E6%8D%AE%E7%AE%A1%E7%90%86pptx.pdf",

                    //http://turbine-api.aialign.com.cn/media/files/pptx-demo/PLM21x_基础课程-06-零件库管理.pptx
                    fileUrl:
                        'http://turbine-api.aialign.com.cn/media/files/pptx-demo/PLM21x_基础课程-06-零件库管理.pptx',

                    //YJ-TEST_dataset .docx
                    // fileUrl:
                    //   "http://turbine-api.aialign.com.cn/media/files/pptx-demo/YJ-TEST_dataset%20.docx",

                    initialPage: 4,
                    // highlights: [
                    //   {
                    //     page: 3,
                    //     texts: ["部分", "操作"],
                    //   },
                    //   {
                    //     page: 4,
                    //     texts: ["需要"],
                    //   },
                    // ],
                    enablePreviewZoom: true,
                },
            },
        ],
    },
    {
        question: 'pdf2',
        keywords: ['pdf2'],
        answer: [
            {
                //uid: "pdf-preview-component",
                uid: 'pdf-viewer-component',
                area: 'right-bottom',
                data: {
                    title: 'pdf预览',
                    // fileUrl: "http://turbine-api.aialign.com.cn/media/files/test.pdf",
                    // fileUrl:
                    // "http://192.168.10.10:8012/PLM21x_%E9%A3%8E%E5%9C%BAGoldenBOM%E7%AE%A1%E7%90%86%E7%94%A8%E6%88%B7%E6%93%8D%E4%BD%9C%E6%89%8B%E5%86%8Cpptx.pdf",

                    //http://192.168.10.10:8012/MaxKB%E5%90%8E%E7%AB%AF%E6%96%87%E6%A1%A3docx.pdf
                    // fileUrl:
                    // "http://192.168.10.10:8012/MaxKB%E5%90%8E%E7%AB%AF%E6%96%87%E6%A1%A3docx.pdf",

                    // fileUrl:
                    //   "http://192.168.10.10:8012/PLM21x_%E4%B8%93%E9%A1%B9%E8%AF%BE%E7%A8%8B-02-%E4%BA%A7%E5%93%81%E6%95%B0%E6%8D%AE%E7%AE%A1%E7%90%86pptx.pdf",

                    //http://turbine-api.aialign.com.cn/media/files/pptx-demo/PLM21x_基础课程-06-零件库管理.pptx
                    // fileUrl:
                    //   "http://turbine-api.aialign.com.cn/media/files/pptx-demo/PLM21x_基础课程-06-零件库管理.pptx",

                    //YJ-TEST_dataset .docx
                    fileUrl:
                        'http://turbine-api.aialign.com.cn/media/files/pptx-demo/YJ-TEST_dataset%20.docx',

                    initialPage: 2,
                    // highlights: [
                    //   {
                    //     page: 3,
                    //     texts: ["部分", "操作"],
                    //   },
                    //   {
                    //     page: 4,
                    //     texts: ["需要"],
                    //   },
                    // ],
                    enablePreviewZoom: true,
                },
            },
        ],
    },
    {
        question: 'pdf3',
        keywords: ['pdf3'],
        answer: [
            {
                //uid: "pdf-preview-component",
                uid: 'pdf-viewer-component',
                area: 'right-bottom',
                data: {
                    title: 'pdf预览',
                    // fileUrl: "http://turbine-api.aialign.com.cn/media/files/test.pdf",
                    // fileUrl:
                    // "http://192.168.10.10:8012/PLM21x_%E9%A3%8E%E5%9C%BAGoldenBOM%E7%AE%A1%E7%90%86%E7%94%A8%E6%88%B7%E6%93%8D%E4%BD%9C%E6%89%8B%E5%86%8Cpptx.pdf",

                    //http://192.168.10.10:8012/MaxKB%E5%90%8E%E7%AB%AF%E6%96%87%E6%A1%A3docx.pdf
                    // fileUrl:
                    // "http://192.168.10.10:8012/MaxKB%E5%90%8E%E7%AB%AF%E6%96%87%E6%A1%A3docx.pdf",

                    // fileUrl:
                    //   "http://192.168.10.10:8012/PLM21x_%E4%B8%93%E9%A1%B9%E8%AF%BE%E7%A8%8B-02-%E4%BA%A7%E5%93%81%E6%95%B0%E6%8D%AE%E7%AE%A1%E7%90%86pptx.pdf",

                    //http://turbine-api.aialign.com.cn/media/files/pptx-demo/PLM21x_基础课程-06-零件库管理.pptx
                    // fileUrl:
                    //   "http://turbine-api.aialign.com.cn/media/files/pptx-demo/PLM21x_基础课程-06-零件库管理.pptx",

                    //YJ-TEST_dataset .docx
                    // fileUrl:
                    //   "http://turbine-api.aialign.com.cn/media/files/pptx-demo/YJ-TEST_dataset%20.docx",

                    fileUrl:
                        'http://turbine-api.aialign.com.cn/media/files/pptx-demo/PLM21x_风场GoldenBOM管理用户操作手册.pptx',

                    initialPage: 3,
                    // highlights: [
                    //   {
                    //     page: 3,
                    //     texts: ["部分", "操作"],
                    //   },
                    //   {
                    //     page: 4,
                    //     texts: ["需要"],
                    //   },
                    // ],
                    enablePreviewZoom: true,
                },
            },
        ],
    },
    {
        question: 'pdf4',
        keywords: ['pdf4'],
        answer: [
            {
                //uid: "pdf-preview-component",
                uid: 'pdf-viewer-component',
                area: 'right-bottom',
                data: {
                    title: 'pdf预览',
                    // fileUrl: "http://turbine-api.aialign.com.cn/media/files/test.pdf",
                    // fileUrl:
                    // "http://192.168.10.10:8012/PLM21x_%E9%A3%8E%E5%9C%BAGoldenBOM%E7%AE%A1%E7%90%86%E7%94%A8%E6%88%B7%E6%93%8D%E4%BD%9C%E6%89%8B%E5%86%8Cpptx.pdf",

                    //http://192.168.10.10:8012/MaxKB%E5%90%8E%E7%AB%AF%E6%96%87%E6%A1%A3docx.pdf
                    // fileUrl:
                    // "http://192.168.10.10:8012/MaxKB%E5%90%8E%E7%AB%AF%E6%96%87%E6%A1%A3docx.pdf",

                    // fileUrl:
                    //   "http://192.168.10.10:8012/PLM21x_%E4%B8%93%E9%A1%B9%E8%AF%BE%E7%A8%8B-02-%E4%BA%A7%E5%93%81%E6%95%B0%E6%8D%AE%E7%AE%A1%E7%90%86pptx.pdf",

                    //http://turbine-api.aialign.com.cn/media/files/pptx-demo/PLM21x_基础课程-06-零件库管理.pptx
                    // fileUrl:
                    //   "http://turbine-api.aialign.com.cn/media/files/pptx-demo/PLM21x_基础课程-06-零件库管理.pptx",

                    //YJ-TEST_dataset .docx
                    // fileUrl:
                    //   "http://turbine-api.aialign.com.cn/media/files/pptx-demo/YJ-TEST_dataset%20.docx",

                    fileUrl:
                        'http://turbine-api.aialign.com.cn/media/files/pptx-demo/MaxKB后端文档.docx',

                    initialPage: 4,
                    // highlights: [
                    //   {
                    //     page: 3,
                    //     texts: ["部分", "操作"],
                    //   },
                    //   {
                    //     page: 4,
                    //     texts: ["需要"],
                    //   },
                    // ],
                    enablePreviewZoom: true,
                },
            },
        ],
    },
    {
        question: '文件列表',
        keywords: ['文件列表'],
        answer: [
            {
                uid: 'default',
                // area: "mid-def-bottom",
                data: '以下为文件列表',
            },
            {
                uid: 'file-list-component',
                area: 'mid-txt-bottom',
                data: {
                    files: [
                        {
                            title: 'MaxKB后端文档.docx',
                            fileUrl:
                                'http://turbine-api.aialign.com.cn/media/files/pptx-demo/MaxKB后端文档.docx',
                            initialPage: 4,
                        },
                        {
                            title: 'PLM21x_风场GoldenBOM管理用户操作手册.pptx',
                            fileUrl:
                                'http://turbine-api.aialign.com.cn/media/files/pptx-demo/PLM21x_风场GoldenBOM管理用户操作手册.pptx',
                            initialPage: 3,
                        },
                        {
                            title: 'MaxKB后端文档.docx',
                            fileUrl:
                                'http://turbine-api.aialign.com.cn/media/files/pptx-demo/MaxKB后端文档.docx',
                            initialPage: 4,
                        },
                        // {
                        //   title: "test.pdf",
                        //   fileUrl: "http://turbine-api.aialign.com.cn/media/files/test.pdf",
                        //   initialPage: 3,
                        // },
                    ],
                },
            },
        ],
    },
];

// 1230
// export const mockData = [
//   // 设计场景
//   {
//     question: "我想看下列表",
//     keywords: ["列表"],
//     answer: [
//         { topic_id: 63, message_id: 248 },
//       {
//         uid: "list-component",
//         area: "mid-def-top",
//         data: {
//             totalNum: 10, //总数量
//             title:'项目',  //标题
//             items:[
//                 { title:'数据1', status:'状态值' },
//                 { title:'数据2', status:'进行中' }
//             ],
//             isMore:true,  //是否展示更多
//             moreTitle: '更多',
//             link:'', //展示更多按钮要跳转的地址
//         }
//     },
//       { uid: "default", area: "mid", data: "工作区中的内容。" },
//     ],
//   },
//   {
//     question: "总数类组件",
//     keywords: ["总数"],
//     answer: [
//         { topic_id: 63, message_id: 248 },
//         {
//             uid: "total-summary-component",
//             area: "mid-def-top",
//             data: {
//                 title:'赢率60%以上的商机',  //标题
//                 titleImgUrl:'',  //标题中的图片地址，前端会有一个默认图片地址（此地址后端看情况是否返回）
//                 items:[
//                     { title:'总金额', total:'100万', imgUrl:'' },   //title建议6个字最多，数量可以在沟通
//                     { title:'总数量', total:'100个', imgUrl:'' }
//                 ]
//             }
//         }
//     ],
//   },
//   {
//     question: "商机详情",
//     keywords: [
//       "商机文本",
//     ],
//     answer: [
//         { topic_id: 63, message_id: 248 },
//         {
//             uid: "text-detail-component",
//             area: "mid-def-top",
//             data:{
//                 title:'标题',  //可没有标题
//                 titleImgUrl:'',  //标题中的图片地址，前端会有一个默认图片地址（此地址后端看情况是否返回）
//                 items:[
//                     { title:'名字',content:'刘三岁', itemType: 'singleLine' },
//                     { title:'供应商', content:'上海电气', itemType: 'singleLine' }
//                 ]
//             }
//         }
//     ],
//   },
//   {
//     question: "工作台详情",
//     keywords: ["工作台详情"],
//     answer: [
//         { topic_id: 63, message_id: 248 },
//         {
//             uid: "workbench-text-detail-component",
//             area: "right-bottom",  //位置：整个右侧工作台,下方
//             data: [
//                 { title:'名字', content:'刘三岁', itemType: 'singleLine' },
//                 { title:'供应商', content:'上海电气', itemType: 'singleLine' }
//             ]
//         }
//     ],
//   }
// ];

// 重命名为更简单的名字
export const getAnswer = (question: string): AnswerType | undefined => {
    // console.log('getAnswer被调用，输入问题:', question);
    const lowerQuestion = question.toLowerCase();
    // console.log('转换为小写:', lowerQuestion);

    // 计算每个答案的匹配分数
    const matchScores = mockData.map((item) => {
        let score = 0;
        // console.log('检查项目:', item.question, '关键词:', item.keywords);

        // 精确匹配得分最高
        if (item.question === question) {
            score += 100;
            // console.log('精确匹配，得分+100');
        }

        // 关键词匹配
        item.keywords.forEach((keyword) => {
            const lowerKeyword = keyword.toLowerCase();
            if (lowerQuestion.includes(lowerKeyword)) {
                score += 1;
                // console.log(`关键词匹配: "${keyword}" -> 得分+1`);
            }
        });

        // console.log(`项目 "${item.question}" 总得分:`, score);
        return { answer: item.answer, score };
    });

    // 获取分数最高的答案
    const bestMatch = matchScores.reduce(
        (best, current) => (current.score > best.score ? current : best),
        { answer: undefined, score: 0 }
    );

    // console.log('最佳匹配结果:', bestMatch);

    // 只有当至少匹配到一个关键词时才返回答案
    return bestMatch.score > 0 ? bestMatch.answer : undefined;
};
