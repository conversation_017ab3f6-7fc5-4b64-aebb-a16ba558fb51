<template>
  <div
    :class="[
      'rounded-[22px] bg-white border border-solid border-[#F1F1F1] p-[20px]',
      containerSize.containerWidth,
    ]"
  >
    <div class="text-[28px] font-zhongcu mb-[5px] text-[#00080E]">
      {{ formattedData.value || "0" }}{{ formattedData.unit || "元" }}
    </div>
    <div class="text-[16px] font-zhongcu text-[#00080E] mb-[20px]">
      {{ data.title || "商机总金额" }}
    </div>
    <div ref="chartRef" class="w-full min-h-[200px]"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from "vue";
import * as echarts from "echarts";
import type { EChartsType } from "echarts";
import { formatAmount } from "../../utils";

const COLOR_SYSTEMS = {
  red: ["#f07c82", "#EF483A", "#c02c38"],
  blue: ["#10aec2", "#3B6DF3", "#1661ab"],
  purple: ["#8076a3", "#7A40F2", "#2e317c"],
  yellow: ["#fed71a", "#FFB200", "#e8b004"],
  green: ["#55bb8a", "#41b349", "#1a6840"],
  orange: ["#f09c5a", "#f97d1c", "#de7622"],
};

// 颜色分配策略
const COLOR_DISTRIBUTION_MODES = {
  // 按色系循环（推荐模式）：每次从不同色系取色，用完一轮后进入下一个深度
  SYSTEM_CYCLE: "SYSTEM_CYCLE",
  // 随机模式：完全随机选择颜色
  RANDOM: "RANDOM",
  // 顺序模式：按颜色数组顺序依次选择
  SEQUENTIAL: "SEQUENTIAL",
  // 色系内循环：在同一色系内循环，用完后切换到下一个色系
  SYSTEM_SEQUENTIAL: "SYSTEM_SEQUENTIAL",
} as const;

// 默认使用推荐模式
const props = defineProps<{
  data: {
    title?: string;
    total_estimated_amount: number;
    items: Array<{
      name: string;
      value: number;
      color: string;
    }>;
  };
  colorMode?: keyof typeof COLOR_DISTRIBUTION_MODES;
}>();

// 设置默认的颜色分配模式
const defaultColorMode = COLOR_DISTRIBUTION_MODES.SYSTEM_CYCLE;

const MAX_DISPLAY_ITEMS = 100; // 最多显示的项目数量

let systemIndex = 0;
let colorIndexInSystem = 0;
let sequentialIndex = 0;
let currentSystemColors: string[] = [];

const getNextColor = () => {
  const mode = props.colorMode || defaultColorMode;

  switch (mode) {
    case COLOR_DISTRIBUTION_MODES.SYSTEM_CYCLE: {
      // 按色系循环（推荐模式）
      const systems = Object.values(COLOR_SYSTEMS);
      const currentSystem = systems[systemIndex];
      const color = currentSystem[colorIndexInSystem];

      systemIndex = (systemIndex + 1) % systems.length;
      if (systemIndex === 0) {
        colorIndexInSystem = (colorIndexInSystem + 1) % currentSystem.length;
      }

      return color;
    }

    case COLOR_DISTRIBUTION_MODES.RANDOM: {
      // 随机模式
      const systems = Object.values(COLOR_SYSTEMS);
      const randomSystem = systems[Math.floor(Math.random() * systems.length)];
      return randomSystem[Math.floor(Math.random() * randomSystem.length)];
    }

    case COLOR_DISTRIBUTION_MODES.SEQUENTIAL: {
      // 顺序模式
      const allColors = Object.values(COLOR_SYSTEMS).flat();
      const color = allColors[sequentialIndex];
      sequentialIndex = (sequentialIndex + 1) % allColors.length;
      return color;
    }

    case COLOR_DISTRIBUTION_MODES.SYSTEM_SEQUENTIAL: {
      // 色系内循环
      if (!currentSystemColors.length) {
        const systems = Object.values(COLOR_SYSTEMS);
        currentSystemColors = systems[systemIndex];
        systemIndex = (systemIndex + 1) % systems.length;
      }

      const color = currentSystemColors[0];
      currentSystemColors = currentSystemColors.slice(1);
      return color;
    }

    default:
      return Object.values(COLOR_SYSTEMS)[0][0];
  }
};

const processedItems = computed(() => {
  // 重置所有索引
  systemIndex = 0;
  colorIndexInSystem = 0;
  sequentialIndex = 0;
  currentSystemColors = [];

  // 处理名称长度的函数
  const formatName = (name: string) => {
    const MAX_LENGTH = 12; // 最大长度
    const TARGET_LENGTH = 10; // 目标长度
    const PADDING_CHAR = "\u3000"; // 使用全角空格填充

    // 如果超过最大长度，截断并添加...
    if (name.length > MAX_LENGTH) {
      return name.slice(0, MAX_LENGTH - 1) + "...";
    }

    // 如果小于目标长度，用全角空格补充
    if (name.length < TARGET_LENGTH) {
      return name + PADDING_CHAR.repeat(TARGET_LENGTH - name.length);
    }

    return name;
  };

  const items = props.data.items.map((item) => ({
    ...item,
    value: parseFloat(item.value as any) || 0,
    color: item.color || getNextColor(),
    name: formatName(item.name), // 处理名称长度
  }));

  if (items.length <= MAX_DISPLAY_ITEMS) return items;

  const topItems = items.slice(0, MAX_DISPLAY_ITEMS - 1);
  const otherItems = items.slice(MAX_DISPLAY_ITEMS - 1);

  const otherTotal = otherItems.reduce((sum, item) => sum + item.value, 0);
  const otherItem = {
    name: formatName("其他"), // 处理"其他"的名称长度
    value: otherTotal,
    color: getNextColor(),
  };

  return [...topItems, otherItem];
});

const formattedData = computed(() => {
  if (props.data.total_estimated_amount == 0) {
    return { value: 0, unit: "" };
  }
  const { value, unit } = formatAmount(
    props.data.total_estimated_amount,
    false
  );
  return { value, unit };
});

const containerSize = computed(() => {
  const itemCount = processedItems.value.length;
  // 计算最长的图例文字长度
  const maxLegendLength = Math.max(
    ...processedItems.value.map((item) => item.name.length)
  );
  // 估算每个图例项所需的宽度（每个字符估算12px + padding等）
  const legendItemWidth = maxLegendLength * 12 + 40; // 40px 为图标和padding的预留空间

  if (itemCount > 8) {
    return {
      containerWidth: "w-[340px]",
      chartCenter: "35%",
      fontSize: 14,
      legendWidth: 0, // 超过8个时不显示图例
    };
  } else if (itemCount > 4) {
    // 两列布局，每列至少需要 legendItemWidth 的宽度
    const minWidth = Math.max(630, legendItemWidth * 2 + 100); // 100px 为列间距和边距
    return {
      containerWidth: `w-[${minWidth}px]`,
      chartCenter: "30%",
      fontSize: 16,
      legendWidth: legendItemWidth,
    };
  } else {
    // 单列布局
    const minWidth = Math.max(430, legendItemWidth + 50); // 50px 为边距
    return {
      containerWidth: `w-[${minWidth}px]`,
      chartCenter: "30%",
      fontSize: 16,
      legendWidth: legendItemWidth,
    };
  }
});

const chartRef = ref<HTMLElement | null>(null);
let chartInstance: EChartsType | null = null;
const selectedItems = ref<Set<string>>(new Set());

const initChart = () => {
  if (!chartRef.value) return;

  if (!chartInstance) {
    chartInstance = echarts.init(chartRef.value);
  }

  const currentData = {
    ...props.data,
    items: processedItems.value,
  };

  // 初始化选中状态
  const selectedMap = Object.fromEntries(
    currentData.items.map((item) => [item.name, true])
  );

  // 计算选中项目的总和
  const calculateSelectedTotal = () => {
    console.log("selectedMap", selectedMap);
    console.log("currentData.items", currentData.items);

    return currentData.items
      .filter((item) => selectedMap[item.name])
      .reduce((sum, item) => sum + item.value, 0);
  };

  const option = {
    legend: {
      orient: "vertical",
      show: processedItems.value.length > 8 ? false : true,
      right: "2%",
      top: "middle",
      itemWidth: 8,
      itemHeight: 8,
      icon: "circle",
      itemGap: 20,
      height: "60%",
      width: containerSize.value.legendWidth, // 设置图例宽度
      textStyle: {
        fontSize: 12,
        fontWeight: 500,
        color: "#505D70",
        overflow: "break", // 文字超出时换行
        width: containerSize.value.legendWidth - 20, // 预留一些空间给图标
      },
      formatter: (name: string) => {
        // const item = currentData.items.find((i) => i.name === name);
        // if (item) {
        //   const { value, unit } = formatAmount(item.value, false);
        //   // 使用模板字符串确保对齐
        //   return ["{name|" + name + "}", "{value|" + value + unit + "}"].join(
        //     "  "
        //   );
        // }

        // const index = currentData.items.findIndex((item) => item.name === name);

        // if (
        //   // (processedItems.value.length === 5 ||
        //   //   processedItems.value.length === 6) &&
        //   index === 4
        // ) {
        //   return `\n\n\n\n\n${name}`; // 添加多个换行来确保新列从顶部开始
        // }

        return name;
      },
      rich: {
        name: {
          fontSize: 12,
          color: "#505D70",
          width: containerSize.value.legendWidth * 0.6, // 名称占60%宽度
          overflow: "truncate", // 超出显示省略号
        },
        value: {
          fontSize: 12,
          color: "#505D70",
          align: "right",
          width: containerSize.value.legendWidth * 0.4, // 数值占40%宽度
        },
      },
      selected: selectedMap,
      // 根据数据项数量自动计算列数
      columns: processedItems.value.length > 4 ? 2 : 1,
    },
    series: [
      {
        type: "pie",
        radius: ["45%", "65%"],
        center: [containerSize.value.chartCenter, "50%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 2,
          borderWidth: 2,
          borderColor: "#ffffff",
        },
        label: {
          show: false,
          position: "outside",
          formatter: function (params: any) {
            const { value, unit } = formatAmount(params.value, false);
            return value + unit;
          },
          fontSize: 12,
          fontWeight: 400,
          color: function (params: any) {
            return "red";
          },
          distance: -5,
        },
        labelLine: {
          show: false,
        },
        emphasis: {
          scale: false,
          label: {
            show: true,
          },
        },
        data: currentData.items.map((item) => ({
          name: item.name,
          value: item.value,
          label: {
            show: true,
            formatter: function (params: any) {
              // 超过8个以上不展示
              if (processedItems.value.length > 8) {
                return "";
              }

              const { value, unit } = formatAmount(params.value, false);
              return value + unit;
            },
            color: item.color,
            fontSize: 13,
            padding: [0, -30],
          },
          itemStyle: {
            color: item.color,
          },
        })),
      },
      {
        type: "pie",
        radius: ["0%", "58%"],
        center: [containerSize.value.chartCenter, "50%"],
        label: {
          show: true,
          position: "center",
          formatter: function (params: any) {
            const { value, unit } = formatAmount(params.value, false);
            return ["{value|" + value + unit + "}"].join("\n");
          },
          rich: {
            value: {
              fontSize: containerSize.value.fontSize,
              color: "#00080E",
              fontWeight: "bold",
              padding: [8, 0, 0, 0],
              lineHeight: 8,
            },
          },
        },
        data: [
          {
            value: calculateSelectedTotal(),
            name: "",
            itemStyle: {
              color: "rgba(255, 255, 255, 0.5)",
            },
          },
        ],
      },
      {
        type: "pie",
        radius: ["0%", "20%"],
        center: [containerSize.value.chartCenter, "50%"],
        label: {
          show: true,
          position: "center",
          formatter: function (params: any) {
            const { value, unit } = formatAmount(params.value, false);
            return ["{value|" + value + unit + "}"].join("\n");
          },
          rich: {
            value: {
              fontSize: containerSize.value.fontSize,
              color: "#00080E",
              fontWeight: "normal",
              padding: [8, 0, 0, 0],
              lineHeight: 8,
            },
          },
        },
        data: [
          {
            value: calculateSelectedTotal(),
            name: "",
            itemStyle: {
              color: "transparent",
            },
          },
        ],
      },
    ],
  };

  chartInstance.setOption(option);

  // 添加图例选择改变事件监听
  chartInstance.on("legendselectchanged", function (params: any) {
    Object.assign(selectedMap, params.selected);
    // 更新总金额
    chartInstance?.setOption({
      series: [
        {}, // 第一个饼图保持不变
        {
          // 更新第二个饼图（中心总金额）
          data: [
            {
              value: calculateSelectedTotal(),
              name: "",
              itemStyle: {
                color: "rgba(255, 255, 255, 0.5)",
              },
            },
          ],
        },
        {}, // 第三个饼图保持不变
      ],
    });
  });
};

onMounted(() => {
  initChart();
  window.addEventListener("resize", () => {
    chartInstance?.resize();
  });
});

watch(
  () => props.data,
  () => {
    initChart();
  },
  { deep: true }
);
</script>

<style scoped>
.business-amount-component {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}
</style>
